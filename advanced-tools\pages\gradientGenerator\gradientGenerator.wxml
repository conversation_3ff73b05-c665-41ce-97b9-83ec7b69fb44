<!--pages/gradientGenerator/gradientGenerator.wxml-->
<view class="page">


  <view class="container">
    <!-- 预设渐变 -->
    <view class="section">
      <view class="section-title">预设渐变</view>
      <view class="presets-description">点击下方预设应用到当前渐变</view>
      <scroll-view scroll-x="true" class="presets-scroll" enhanced="true" show-scrollbar="false">
        <view class="presets-container">
          <view
            wx:for="{{presets}}"
            wx:key="id"
            class="preset-item"
            bindtap="applyPreset"
            data-preset="{{index}}"
          >
            <view class="preset-preview" style="background: {{item.gradient}};"></view>
            <view class="preset-name">{{item.name || '预设 ' + (index + 1)}}</view>
          </view>
        </view>
      </scroll-view>
    </view>



    <!-- 渐变类型选择 -->
    <view class="section">
      <view class="section-title">渐变类型</view>
      <view class="type-selector">
        <view
          class="type-item {{gradientType === 'linear' ? 'active' : ''}}"
          bindtap="switchGradientType"
          data-type="linear"
        >
          <view class="type-icon linear-icon"></view>
          <text>线性渐变</text>
        </view>
        <view
          class="type-item {{gradientType === 'radial' ? 'active' : ''}}"
          bindtap="switchGradientType"
          data-type="radial"
        >
          <view class="type-icon radial-icon"></view>
          <text>径向渐变</text>
        </view>
      </view>
    </view>

    <!-- 渐变方向/角度控制 (线性渐变) -->
    <view class="section" wx:if="{{gradientType === 'linear'}}">
      <view class="section-title">渐变方向</view>
      <view class="direction-control-layout">
        <!-- 左侧可视化按钮 -->
        <view class="direction-visual-container">
          <view class="direction-visual-grid">
            <view
              class="direction-btn {{direction === 'to left top' ? 'active' : ''}}"
              bindtap="setDirection"
              data-direction="to left top"
            >
              <view class="direction-arrow left-top-arrow"></view>
            </view>
            <view
              class="direction-btn {{direction === 'to top' ? 'active' : ''}}"
              bindtap="setDirection"
              data-direction="to top"
            >
              <view class="direction-arrow top-arrow"></view>
            </view>
            <view
              class="direction-btn {{direction === 'to right top' ? 'active' : ''}}"
              bindtap="setDirection"
              data-direction="to right top"
            >
              <view class="direction-arrow right-top-arrow"></view>
            </view>
            <view
              class="direction-btn {{direction === 'to left' ? 'active' : ''}}"
              bindtap="setDirection"
              data-direction="to left"
            >
              <view class="direction-arrow left-arrow"></view>
            </view>
            <view class="direction-btn-center">
              <view class="direction-arrow {{directionIcons[direction]}}"></view>
            </view>
            <view
              class="direction-btn {{direction === 'to right' ? 'active' : ''}}"
              bindtap="setDirection"
              data-direction="to right"
            >
              <view class="direction-arrow right-arrow"></view>
            </view>
            <view
              class="direction-btn {{direction === 'to left bottom' ? 'active' : ''}}"
              bindtap="setDirection"
              data-direction="to left bottom"
            >
              <view class="direction-arrow left-bottom-arrow"></view>
            </view>
            <view
              class="direction-btn {{direction === 'to bottom' ? 'active' : ''}}"
              bindtap="setDirection"
              data-direction="to bottom"
            >
              <view class="direction-arrow bottom-arrow"></view>
            </view>
            <view
              class="direction-btn {{direction === 'to right bottom' ? 'active' : ''}}"
              bindtap="setDirection"
              data-direction="to right bottom"
            >
              <view class="direction-arrow right-bottom-arrow"></view>
            </view>
          </view>
        </view>

        <!-- 右侧角度控制 -->
        <view class="direction-controls-right">
          <view class="angle-control">
            <view class="angle-direction-display">
              <view class="angle-label">当前方向: <text class="angle-direction-text">{{directionLabels[direction]}}</text></view>
              <view class="angle-value">{{angle}}°</view>
            </view>

            <view class="angle-pointer-container">
              <view class="angle-pointer">
                <view class="angle-pointer-indicator" style="transform: rotate({{angle}}deg);"></view>
              </view>
            </view>

            <slider
              min="0"
              max="359"
              value="{{angle}}"
              activeColor="#07c160"
              block-color="#07c160"
              block-size="20"
              show-value="{{false}}"
              bindchange="setAngle"
            ></slider>
          </view>
        </view>
      </view>
    </view>

    <!-- 径向渐变形状控制 -->
    <view class="section" wx:if="{{gradientType === 'radial'}}">
      <view class="section-title">渐变形状</view>
      <view class="shape-selector">
        <view
          class="shape-item {{shape === 'circle' ? 'active' : ''}}"
          bindtap="setShape"
          data-shape="circle"
        >
          <view class="shape-icon circle-icon"></view>
          <text>圆形</text>
        </view>
        <view
          class="shape-item {{shape === 'ellipse' ? 'active' : ''}}"
          bindtap="setShape"
          data-shape="ellipse"
        >
          <view class="shape-icon ellipse-icon"></view>
          <text>椭圆</text>
        </view>
      </view>

      <view class="position-control">
        <view class="position-label">渐变中心位置</view>
        <view class="position-visual-container">
          <view class="position-visual-grid">
            <view
              class="position-btn {{position === 'left top' ? 'active' : ''}}"
              bindtap="setPosition"
              data-position="left top"
            >
              <view class="position-indicator left-top-indicator"></view>
            </view>
            <view
              class="position-btn {{position === 'top' ? 'active' : ''}}"
              bindtap="setPosition"
              data-position="top"
            >
              <view class="position-indicator top-indicator"></view>
            </view>
            <view
              class="position-btn {{position === 'right top' ? 'active' : ''}}"
              bindtap="setPosition"
              data-position="right top"
            >
              <view class="position-indicator right-top-indicator"></view>
            </view>
            <view
              class="position-btn {{position === 'left' ? 'active' : ''}}"
              bindtap="setPosition"
              data-position="left"
            >
              <view class="position-indicator left-indicator"></view>
            </view>
            <view
              class="position-btn {{position === 'center' ? 'active' : ''}}"
              bindtap="setPosition"
              data-position="center"
            >
              <view class="position-indicator center-indicator"></view>
            </view>
            <view
              class="position-btn {{position === 'right' ? 'active' : ''}}"
              bindtap="setPosition"
              data-position="right"
            >
              <view class="position-indicator right-indicator"></view>
            </view>
            <view
              class="position-btn {{position === 'left bottom' ? 'active' : ''}}"
              bindtap="setPosition"
              data-position="left bottom"
            >
              <view class="position-indicator left-bottom-indicator"></view>
            </view>
            <view
              class="position-btn {{position === 'bottom' ? 'active' : ''}}"
              bindtap="setPosition"
              data-position="bottom"
            >
              <view class="position-indicator bottom-indicator"></view>
            </view>
            <view
              class="position-btn {{position === 'right bottom' ? 'active' : ''}}"
              bindtap="setPosition"
              data-position="right bottom"
            >
              <view class="position-indicator right-bottom-indicator"></view>
            </view>
          </view>
        </view>
        <view class="position-display">
          <view class="position-display-label">当前位置: <text class="position-display-text">{{positionLabels[position] || position}}</text></view>
        </view>
      </view>
    </view>

    <!-- 颜色管理 -->
    <view class="section">
      <view class="section-title">渐变颜色</view>
      <view class="color-stops-container">
        <view class="color-stops">
          <view
            wx:for="{{colorStops}}"
            wx:key="index"
            class="color-stop {{currentStopIndex === index ? 'active' : ''}}"
            style="background-color: {{item.color}};"
            bindtap="selectColorStop"
            data-index="{{index}}"
          >
            <view class="color-stop-position">{{item.position}}%</view>
            <view class="color-stop-delete" catchtap="deleteColorStop" data-index="{{index}}">×</view>
          </view>
          <view class="add-color-stop" bindtap="addColorStop" wx:if="{{colorStops.length < 10}}">
            <text class="add-icon">+</text>
          </view>
        </view>
      </view>

      <view class="color-stop-controls" wx:if="{{currentStopIndex !== null}}">
        <view class="color-card">
          <view class="color-card-header">
            <view class="color-card-title">颜色 #{{currentStopIndex + 1}}</view>
            <view class="color-card-copy" catchtap="copyColorValue" data-color="{{colorStops[currentStopIndex].color}}" data-item="color">
              <text class="copy-icon">复制</text>
              <view class="copy-indicator {{copiedItem === 'color' ? 'show' : ''}}">
                <view class="copy-indicator-text">已复制</view>
              </view>
            </view>
          </view>

          <view class="color-card-content">
            <!-- 参考颜色转换器的布局 -->
            <view class="base-color-container">
              <!-- 左侧颜色色块，在中央显示16进制色值 -->
              <view class="color-preview" style="background-color: {{colorStops[currentStopIndex].color}};" catchtap="showColorPicker">
                <view class="color-hex-value" style="color: {{currentTextColor}};">{{colorStops[currentStopIndex].color}}</view>
              </view>
              <!-- 右侧上下并列的按钮 -->
              <view class="color-actions-column">
                <view class="btn-wrapper">
                  <view class="custom-btn random-btn" catchtap="randomColorStop">随机颜色</view>
                </view>
                <view class="btn-wrapper">
                  <view class="custom-btn picker-btn" catchtap="showColorPicker">选择颜色</view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <view class="position-control-container">
          <view class="position-control-header">
            <view class="position-control-title">位置</view>
            <view class="position-value">{{colorStops[currentStopIndex].position}}%</view>
          </view>
          <slider
            min="0"
            max="100"
            value="{{colorStops[currentStopIndex].position}}"
            activeColor="#07c160"
            block-color="#07c160"
            block-size="24"
            show-value="{{false}}"
            bindchange="setColorPosition"
          ></slider>
        </view>
      </view>
    </view>

    <!-- 渐变预览 -->
    <view class="section preview-section">
      <view class="section-title">渐变预览</view>
      <view class="gradient-preview-container">
        <view class="gradient-preview" style="{{gradientStyle}}">
          <view class="gradient-direction-indicator" wx:if="{{gradientType === 'linear'}}">
            <view class="direction-arrow" style="transform: rotate({{angle}}deg)">
              <view class="arrow-head"></view>
              <view class="arrow-line"></view>
            </view>
          </view>
          <view class="gradient-center-indicator" wx:if="{{gradientType === 'radial'}}" style="{{position !== 'center' ? 'transform: translate(' + (position.includes('right') ? '50%' : (position.includes('left') ? '-50%' : '0')) + ', ' + (position.includes('top') ? '-50%' : (position.includes('bottom') ? '50%' : '0')) + ');' : ''}}"></view>
        </view>
      </view>
      <view class="gradient-code-container">
        <view class="gradient-code-header">
          <view class="gradient-code-label">CSS 代码</view>
          <view class="gradient-code-copy" catchtap="copyGradientCode" data-item="code">
            <text class="copy-icon">复制</text>
            <view class="copy-indicator {{copiedItem === 'code' ? 'show' : ''}}">
              <view class="copy-indicator-text">已复制</view>
            </view>
          </view>
        </view>
        <view class="gradient-code">{{gradientCode}}</view>
      </view>

      <!-- 保存为壁纸按钮 -->
      <view class="save-wallpaper-container">
        <button class="save-wallpaper-btn" bindtap="saveAsWallpaper">保存为壁纸</button>
      </view>
    </view>

  </view>

  <!-- 颜色选择器弹窗 -->
  <view class="color-picker-modal" wx:if="{{showColorPicker}}">
    <view class="color-picker-container">
      <view class="color-picker-header">
        <view class="color-picker-title">选择颜色</view>
        <view class="color-picker-close" bindtap="hideColorPicker">×</view>
      </view>
      <color-picker
        color="{{currentStopIndex !== null ? colorStops[currentStopIndex].color : '#ff0000'}}"
        bindcolor="onColorChange"
        bindconfirm="onColorConfirm"
        bindcancel="hideColorPicker"
      ></color-picker>
    </view>
  </view>

  <!-- 隐藏的画布，用于生成壁纸 - 不指定固定尺寸，由JS动态设置 -->
  <canvas type="2d" id="wallpaperCanvas" style="position: absolute; left: -9999px;"></canvas>

  <!-- 保存成功提示 -->
  <view class="save-success {{showSaveSuccess ? 'show' : ''}}">
    <view class="success-icon"></view>
    <view class="success-text">壁纸已保存到相册</view>
  </view>
</view>
