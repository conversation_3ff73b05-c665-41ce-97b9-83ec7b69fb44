// components/color-card-d/color-card-d.js - 色卡D (3:4)
const colorUtils = require('../../utils/colorUtils');
const logUtils = require('../../utils/logUtils');

Component({
  properties: {
    colors: {
      type: Array,
      value: []
    },
    imagePath: {
      type: String,
      value: ''
    }
  },

  data: {
    canvasWidth: 1600, // 画布宽度
    canvasHeight: 2410 // 画布高度，参考3402.html
  },

  lifetimes: {
    attached() {
      this.drawColorCard();
    }
  },

  methods: {
    async drawColorCard() {
      const { canvasWidth, canvasHeight } = this.data;
      const { colors, imagePath } = this.properties;

      if (!imagePath || colors.length === 0) return;

      try {
        // 获取Canvas节点
        const query = this.createSelectorQuery();
        const canvas = await new Promise((resolve) => {
          query.select('#colorCardCanvas')
            .fields({ node: true, size: true })
            .exec((res) => {
              resolve(res[0]);
            });
        });

        if (!canvas || !canvas.node) {
          // 获取Canvas节点失败，静默处理
          return;
        }

        const ctx = canvas.node.getContext('2d');

        // 设置Canvas的实际尺寸
        canvas.node.width = canvasWidth;
        canvas.node.height = canvasHeight;

        // 绘制白色背景
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, canvasWidth, canvasHeight);

        try {
          // 获取图片信息
          const res = await new Promise((resolve, reject) => {
            wx.getImageInfo({
              src: imagePath,
              success: resolve,
              fail: reject
            });
          });

          const { width: imgWidth, height: imgHeight } = res;

          // 创建图片对象
          const img = canvas.node.createImage();
          await new Promise((resolve, reject) => {
            img.onload = resolve;
            img.onerror = reject;
            img.src = imagePath;
          });

          // 绘制标题
          ctx.fillStyle = '#333333';
          ctx.font = 'bold 48px PingFang SC, sans-serif';
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText('COLOR ｜ KALA配色', canvasWidth / 2, 80 + 33.5);

          // 绘制图片 - 3:4比例
          const imageWidth = 1288;
          const imageHeight = 1717;
          const imageX = 156;
          const imageY = 192;

          this.drawImage(ctx, img, imageX, imageY, imageWidth, imageHeight, imgWidth, imgHeight);

          // 绘制颜色块 - 参考3402.html的样式
          // 颜色块是圆形，水平排列
          const colorBlockRadius = 107.5; // 颜色块半径
          const colorBlockY = 2024; // 颜色块Y坐标
          const colorBlockXPositions = [156 + colorBlockRadius, 424 + colorBlockRadius, 693 + colorBlockRadius, 961 + colorBlockRadius, 1229 + colorBlockRadius]; // 颜色块X坐标中心点
          const colorCodeY = 2267; // 颜色代码Y坐标

          // 绘制颜色块
          this.drawColorBlocks(ctx, colors, colorBlockRadius, colorBlockY, colorBlockXPositions, colorCodeY);

          // 保存Canvas
          setTimeout(() => {
            this.saveCanvas(canvas.node);
          }, 300);
        } catch (err) {
          // 绘制失败，触发生成事件
          this.triggerEvent('generated', { path: imagePath });
        }
      } catch (error) {
        // Canvas初始化失败，触发生成事件
        this.triggerEvent('generated', { path: imagePath });
      }
    },

    // 绘制图片 (3:4比例)
    drawImage(ctx, img, destX, destY, destWidth, destHeight, imgWidth, imgHeight) {
      // 计算裁剪参数，保持3:4比例
      let sourceX = 0;
      let sourceY = 0;
      let sourceWidth = imgWidth;
      let sourceHeight = imgHeight;

      // 计算目标区域的实际比例
      const destRatio = destWidth / destHeight;

      // 根据原始图片比例进行裁剪
      const imgRatio = imgWidth / imgHeight;

      if (imgRatio > destRatio) {
        // 图片比目标区域更宽，需要裁剪左右
        sourceWidth = imgHeight * destRatio;
        sourceX = (imgWidth - sourceWidth) / 2;
      } else if (imgRatio < destRatio) {
        // 图片比目标区域更高，需要裁剪上下
        sourceHeight = imgWidth / destRatio;
        sourceY = (imgHeight - sourceHeight) / 2;
      }

      // 绘制图片
      ctx.drawImage(
        img,
        sourceX, sourceY, sourceWidth, sourceHeight, // 源图像参数
        destX, destY, destWidth, destHeight // 目标区域参数
      );
    },

    // 绘制颜色块 - 参考3402.html的样式
    drawColorBlocks(ctx, colors, radius, y, xPositions, codeY) {
      // 确保最多显示5个颜色
      const actualColors = colors.slice(0, 5);

      // 如果颜色不足5个，补充默认颜色
      while (actualColors.length < 5) {
        const defaultColors = ['#4A4130', '#5D2317', '#900407', '#D8DDE1', '#7E96B2'];
        const missingCount = 5 - actualColors.length;
        for (let i = 0; i < missingCount; i++) {
          actualColors.push(defaultColors[i % defaultColors.length]);
        }
      }

      // 绘制每个颜色块
      actualColors.forEach((color, index) => {
        const x = xPositions[index];

        // 绘制圆形颜色块
        ctx.beginPath();
        ctx.arc(x, y + radius, radius, 0, 2 * Math.PI);
        ctx.fillStyle = color;
        ctx.fill();

        // 获取RGB值
        const rgb = colorUtils.hexToRgb(color);

        // 获取适合背景色的文字颜色 - 强制使用黑色
        const textColors = colorUtils.getTextColorsForBackground(color, true);

        // 绘制颜色代码 - 使用适合背景的颜色
        ctx.fillStyle = textColors.hexTextColor;
        ctx.font = 'bold 36px Arial, sans-serif';
        ctx.textAlign = 'center';
        ctx.letterSpacing = '1.4px';

        // 使用格式：# 4A4130
        const colorCode = color.toUpperCase().replace('#', '');

        // 手动实现字母间距
        const text = `# ${colorCode}`;
        const letterSpacing = 1.4; // 字母间距，单位为像素
        let totalWidth = 0;

        // 计算文本总宽度（包含间距）
        for (let i = 0; i < text.length; i++) {
          const metrics = ctx.measureText(text[i]);
          totalWidth += metrics.width + (i < text.length - 1 ? letterSpacing : 0);
        }

        // 计算起始位置，使文本居中
        const startX = x - totalWidth / 2;

        // 逐个字符绘制，实现字母间距效果
        let currentX = startX;
        for (let i = 0; i < text.length; i++) {
          const char = text[i];
          const metrics = ctx.measureText(char);
          ctx.fillText(char, currentX + metrics.width / 2, codeY + 24);
          currentX += metrics.width + letterSpacing;
        }

        // 绘制RGB值 - 使用80%透明度的颜色
        ctx.fillStyle = textColors.rgbTextColor;
        ctx.font = '24px Arial, sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText(`R${rgb.r} G${rgb.g} B${rgb.b}`, x, codeY + 60);
      });
    },

    // 保存Canvas
    async saveCanvas(canvasNode) {
      try {
        const { canvasWidth, canvasHeight } = this.data;

        const res = await new Promise((resolve, reject) => {
          wx.canvasToTempFilePath({
            canvas: canvasNode,
            width: canvasWidth,
            height: canvasHeight,
            destWidth: canvasWidth,
            destHeight: canvasHeight,
            fileType: 'jpg',
            quality: 1,
            success: resolve,
            fail: reject
          }, this);
        });

        logUtils.log('Canvas保存成功', res.tempFilePath);
        this.triggerEvent('generated', { path: res.tempFilePath });
      } catch (err) {
        logUtils.error('Canvas保存失败', err);
        this.triggerEvent('generated', { path: this.properties.imagePath });
      }
    }
  }
})
