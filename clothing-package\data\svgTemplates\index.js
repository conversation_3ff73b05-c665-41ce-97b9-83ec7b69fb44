/**
 * SVG模板索引文件
 * 该文件统一导出所有可用的SVG模板，便于管理和使用
 */

// 导入所有SVG模板
const boy01 = require('./boy01.js'); // 男孩模板1
const girl01 = require('./girl01.js'); // 女孩模板1
const girl02 = require('./girl02.js'); // 女孩模板2
const boy02 = require('./boy02.js'); // 男孩模板2
const girl03 = require('./girl03.js'); // 女孩模板3

// 导出模板对象
module.exports = {
  // 键名为模板ID，值为模板内容
  boy01: boy01, // 男孩模板1 - 支持调整衣服(.st13)、裤子(.st0)和袜子(.st8)颜色
  girl01: girl01, // 女孩模板1 - 支持调整衣服(.st5)、裤子(.st23)、袜子(.st21)和鞋子(.st7)颜色
  girl02: girl02, // 女孩模板2 - 支持调整衣服(.st1)、长裙(.st0)和鞋子(.st41)颜色
  boy02: boy02, // 男孩模板2 - 支持调整T恤(.st2)、外套(.st0)、裤子(.st1)和鞋子(.st16)颜色
  girl03: girl03, // 女孩模板3 - 支持调整内搭(.st1)、开衫(.st0)、裙子(.st14)、挎包(.st18)、鞋子(.st16)和袜子(.st17)颜色
};
