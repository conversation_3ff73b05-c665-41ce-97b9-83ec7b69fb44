// components/colorPicker/colorPicker.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    color: {
      type: String,
      value: '#c83c23'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    r: 200,
    g: 60,
    b: 35,
    h: 9,
    s: 70,
    l: 46,
    hexValue: '#c83c23',
    hexInput: 'C83C23', // 专门用于HEX输入框显示的属性
    cmyk: {
      c: 0,
      m: 70,
      y: 83,
      k: 22
    },
    pickerMode: 'grid', // 默认使用网格模式
    colorGrid: [], // 颜色网格数据
    spectrumSelector: {
      x: 100,
      y: 100
    },
    spectrumSelectorReady: false, // 光谱选择器是否准备好显示
    hueSliderPosition: 0, // 色相滑块位置
    currentHue: 0, // 当前选择的色相值
    spectrumBackground: '#FF0000', // 光谱背景色
    spectrumRect: null, // 光谱区域的尺寸信息
    hueSliderRect: null, // 色相滑块区域的尺寸信息
    isComponentReady: false // 组件是否已经准备好
  },

  lifetimes: {
    attached: function() {
      // 确保初始颜色值有效
      const initialColor = this.properties.color || '#c83c23';

      // 组件初始化时，解析传入的颜色值
      this.parseColor(initialColor);

      // 生成颜色网格
      this.generateColorGrid();

      // 初始化光谱背景色
      const hueColor = this.hslToRgb(this.data.h, 100, 50);
      const hueColorHex = '#' + this.rgbToHex(hueColor.r, hueColor.g, hueColor.b);

      // 确保hexInput有有效值
      const hexInputValue = this.data.hexValue ? this.data.hexValue.substring(1) : 'C83C23';

      this.setData({
        spectrumBackground: hueColorHex,
        currentHue: this.data.h,
        hexInput: hexInputValue,
        isComponentReady: true
      });
    },

    // 组件被移除时执行
    detached: function() {
      // 清理定时器
      if (this.hexInputTimer) {
        clearTimeout(this.hexInputTimer);
        this.hexInputTimer = null;
      }
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
        this.debounceTimer = null;
      }
      // 清理其他可能的定时器
      if (this.inputTimer) {
        clearTimeout(this.inputTimer);
        this.inputTimer = null;
      }
    },

    ready: function() {
      // 组件在视图层布局完成后执行

      // 确保组件已经准备好
      if (!this.data.isComponentReady) {
        return;
      }

      // 确保HEX值被正确设置
      const { r, g, b, h, hexValue } = this.data;

      // 验证当前数据的有效性
      if (typeof r !== 'number' || typeof g !== 'number' || typeof b !== 'number') {
        console.warn('Invalid RGB values in ready:', { r, g, b });
        return;
      }

      const calculatedHexValue = '#' + this.rgbToHex(r, g, b);
      const hexInputValue = (hexValue || calculatedHexValue).substring(1);

      // 确保光谱背景色被正确设置
      const hueColor = this.hslToRgb(h, 100, 50);
      const hueColorHex = '#' + this.rgbToHex(hueColor.r, hueColor.g, hueColor.b);

      // 一次性更新所有必要的数据
      this.setData({
        hexValue: calculatedHexValue,
        hexInput: hexInputValue,
        spectrumBackground: hueColorHex,
        currentHue: h
      });

      // 获取光谱区域的尺寸信息
      this.getSpectrumRect();
    }
  },

  // 页面生命周期函数
  pageLifetimes: {
    // 组件所在页面显示时执行
    show: function() {
      // 检查是否有通过页面栈传递的颜色值
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];

      if (currentPage && currentPage.data && currentPage.data.returnedColor) {

        // 解析颜色并更新所有颜色值
        this.parseColor(currentPage.data.returnedColor);

        // 触发颜色变化事件
        this.triggerEvent('change', { color: currentPage.data.returnedColor });

        // 切换回网格模式
        this.setData({
          pickerMode: 'grid'
        });

        // 清除页面上的returnedColor，避免重复处理
        currentPage.setData({
          returnedColor: ''
        });

        // 显示成功提示
        wx.showToast({
          title: '已应用图片取色',
          icon: 'success',
          duration: 1500
        });
      }
    }
  },

  // 监听属性变化
  observers: {
    'color': function(newColor) {
      // 当外部传入的color属性变化时，重新解析颜色
      if (newColor && this.data.isComponentReady) {
        this.parseColor(newColor);
      }
    },

    'pickerMode': function(mode) {
      // 当切换到光谱模式时的处理已经在switchPickerMode函数中完成
      // 这里不再需要重复处理，避免多次更新导致闪烁
    },

    // 监听色相值变化，更新光谱背景色
    'currentHue': function(hue) {
      if (this.data.pickerMode === 'spectrum') {
        const hueColor = this.hslToRgb(hue, 100, 50);
        const hueColorHex = '#' + this.rgbToHex(hueColor.r, hueColor.g, hueColor.b);

        this.setData({
          spectrumBackground: hueColorHex
        });
      }
    }
  },



  /**
   * 组件的方法列表
   */
  methods: {
    // 切换选择器模式
    switchPickerMode: function(e) {
      const mode = e.currentTarget.dataset.mode;

      // 如果是图片取色模式，直接打开图片选择器
      if (mode === 'image') {
        // 获取当前颜色
        const currentColor = this.data.hexValue;

        // 直接打开图片选择器
        wx.chooseImage({
          count: 1,
          sizeType: ['original', 'compressed'],
          sourceType: ['album', 'camera'],
          success: (res) => {
            const tempFilePath = res.tempFilePaths[0];

            // 获取图片后直接跳转到图片取色页面
            wx.navigateTo({
              url: '/pages/imageColorPicker/imageColorPicker?color=' + encodeURIComponent(currentColor) + '&imagePath=' + encodeURIComponent(tempFilePath),
              events: {
                // 监听从图片取色页面返回的颜色
                acceptColorFromImagePicker: (result) => {

                  // 检查结果是对象还是字符串
                  let colorValue;
                  if (typeof result === 'object' && result !== null) {
                    // 如果是对象，提取color属性
                    colorValue = result.color;
                  } else {
                    // 如果是字符串，直接使用
                    colorValue = result;
                  }

                  if (colorValue) {

                    // 解析颜色并更新所有颜色值
                    this.parseColor(colorValue);

                    // 触发颜色变化事件
                    this.triggerEvent('change', { color: colorValue });

                    // 如果结果包含confirmed标志，并且为true，则自动确认颜色
                    if (result && typeof result === 'object' && result.confirmed) {
                      this.triggerEvent('confirm', { color: colorValue });
                    } else {
                      // 切换回网格模式，避免用户再次点击图片取色
                      this.setData({
                        pickerMode: 'grid'
                      });

                      // 显示成功提示
                      wx.showToast({
                        title: '已应用图片取色',
                        icon: 'success',
                        duration: 1500
                      });
                    }
                  }
                }
              },
              success: (res) => {
                // 获取页面实例，用于后续通信
                this.imagePickerPage = res.eventChannel;
              },
              fail: (err) => {
                wx.showToast({
                  title: '打开取色页面失败',
                  icon: 'none',
                  duration: 1500
                });

                // 切换回网格模式
                this.setData({
                  pickerMode: 'grid'
                });
              }
            });
          },
          fail: (err) => {
            wx.showToast({
              title: '选择图片失败',
              icon: 'none',
              duration: 1500
            });

            // 切换回网格模式
            this.setData({
              pickerMode: 'grid'
            });
          }
        });
        return;
      }

      // 如果要切换到光谱模式，先隐藏选择器，避免闪烁
      if (mode === 'spectrum') {
        // 先设置选择器为不可见
        this.setData({
          spectrumSelectorReady: false
        });

        // 预先计算光谱背景色
        const { h, s, l } = this.data;
        const hueColor = this.hslToRgb(h, 100, 50);
        const hueColorHex = '#' + this.rgbToHex(hueColor.r, hueColor.g, hueColor.b);

        // 将HSL转换为HSV以正确定位光谱选择器
        const hsv = this.hslToHsv(h, s, l);

        // 切换模式
        this.setData({
          pickerMode: mode,
          spectrumBackground: hueColorHex,
          currentHue: h
        });

        // 获取光谱区域尺寸信息并设置选择器位置
        wx.nextTick(() => {
          const query = wx.createSelectorQuery().in(this);
          query.select('.color-spectrum').boundingClientRect();
          query.select('.hue-slider').boundingClientRect();
          query.exec((res) => {
            if (res && res[0] && res[1]) {
              // 设置光谱区域和色相滑块的尺寸信息
              const spectrumRect = res[0];
              const hueSliderRect = res[1];

              // 根据当前色相值设置色相滑块位置
              const huePosition = (h / 360) * hueSliderRect.width;

              // 计算光谱选择器位置
              // 饱和度从左到右递增
              const x = spectrumRect.width - ((100 - hsv.s) / 100) * spectrumRect.width;
              // 明度从上到下递减
              const y = (1 - hsv.v / 100) * spectrumRect.height;

              // 一次性更新所有数据，避免多次渲染
              this.setData({
                spectrumRect: spectrumRect,
                hueSliderRect: hueSliderRect,
                hueSliderPosition: huePosition,
                'spectrumSelector.x': x,
                'spectrumSelector.y': y,
                spectrumSelectorReady: true // 现在可以显示选择器了
              });


            }
          });
        });
      } else {
        // 如果切换到其他模式，直接设置
        this.setData({
          pickerMode: mode
        });
      }
    },

    // 生成颜色网格
    generateColorGrid: function() {
      // 定义40个最常用的颜色，按照色相排序，8列5行排列
      const colors = [
        // 第一行：红色系和橙色系
        '#FF0000', '#E60000', '#CC0000', '#990000', '#FF4500', '#FF8C00', '#FFA500', '#FFD700',

        // 第二行：黄色系和绿色系
        '#FFFF00', '#FFEB3B', '#FFC107', '#CDDC39', '#8BC34A', '#4CAF50', '#009688', '#00FF00',

        // 第三行：青色系和蓝色系
        '#00FFFF', '#00BCD4', '#03A9F4', '#2196F3', '#3F51B5', '#0000FF', '#000080', '#1A237E',

        // 第四行：紫色系和粉色系
        '#673AB7', '#9C27B0', '#E91E63', '#F06292', '#FF69B4', '#FF1493', '#C71585', '#9932CC',

        // 第五行：棕色系和灰度系
        '#795548', '#A52A2A', '#8B4513', '#CD853F', '#FFFFFF', '#E0E0E0', '#9E9E9E', '#000000'
      ];

      this.setData({
        colorGrid: colors
      });
    },

    // 获取光谱区域和色相滑块的尺寸信息
    getSpectrumRect: function() {
      // 先隐藏选择器，避免闪烁
      this.setData({
        spectrumSelectorReady: false
      });

      const query = wx.createSelectorQuery().in(this);
      query.select('.color-spectrum').boundingClientRect();
      query.select('.hue-slider').boundingClientRect();
      query.exec((res) => {
        if (res && res[0] && res[1]) {
          // 设置光谱区域和色相滑块的尺寸信息
          const spectrumRect = res[0];
          const hueSliderRect = res[1];

          // 获取当前HSL值
          const { h, s, l } = this.data;

          // 根据当前色相值设置色相滑块位置
          const huePosition = (h / 360) * hueSliderRect.width;

          // 将HSL转换为HSV以正确定位光谱选择器
          const hsv = this.hslToHsv(h, s, l);

          // 计算光谱选择器位置
          // 饱和度从左到右递增
          const x = spectrumRect.width - ((100 - hsv.s) / 100) * spectrumRect.width;
          // 明度从上到下递减
          const y = (1 - hsv.v / 100) * spectrumRect.height;

          // 一次性更新所有数据，避免多次渲染
          this.setData({
            spectrumRect: spectrumRect,
            hueSliderRect: hueSliderRect,
            hueSliderPosition: huePosition,
            currentHue: h,
            'spectrumSelector.x': x,
            'spectrumSelector.y': y,
            spectrumSelectorReady: true // 现在可以显示选择器了
          });

          // 更新光谱背景色
          this.updateSpectrumBackground();
        }
      });
    },

    // 更新光谱背景色
    updateSpectrumBackground: function() {
      const { currentHue } = this.data;
      // 使用饱和度100%，亮度50%的颜色作为基础色相
      const hueColor = this.hslToRgb(currentHue, 100, 50);
      const hueColorHex = '#' + this.rgbToHex(hueColor.r, hueColor.g, hueColor.b);

      // 直接设置数据，让WXML通过内联样式更新背景
      this.setData({
        spectrumBackground: hueColorHex
      });


    },

    // 处理网格颜色选择
    onGridColorSelect: function(e) {
      const color = e.currentTarget.dataset.color;
      this.parseColor(color);
      this.triggerEvent('change', { color: this.data.hexValue });
    },

    // 处理色相滑块触摸开始
    onHueSliderTouchStart: function(e) {
      this.updateHueFromSlider(e);
    },

    // 处理色相滑块触摸移动
    onHueSliderTouchMove: function(e) {
      this.updateHueFromSlider(e);
    },

    // 从色相滑块位置更新色相值
    updateHueFromSlider: function(e) {
      const touch = e.touches[0];
      const rect = this.data.hueSliderRect;

      if (!rect) {
        this.getSpectrumRect();
        return;
      }

      // 计算相对位置
      let x = touch.pageX - rect.left;

      // 限制在滑块区域内
      x = Math.max(0, Math.min(x, rect.width));

      // 计算色相值 (0-360)
      const hue = Math.round((x / rect.width) * 360);

      // 更新色相滑块位置和当前色相值
      this.setData({
        hueSliderPosition: x,
        currentHue: hue
      });

      // 更新光谱背景色
      this.updateSpectrumBackground();

      // 更新选择器位置对应的颜色
      this.updateColorFromSpectrumPosition();
    },

    // 处理光谱触摸开始
    onSpectrumTouchStart: function(e) {
      this.updateColorFromSpectrum(e);
    },

    // 处理光谱触摸移动
    onSpectrumTouchMove: function(e) {
      this.updateColorFromSpectrum(e);
    },

    // 从光谱位置更新颜色
    updateColorFromSpectrum: function(e) {
      const touch = e.touches[0];
      const rect = this.data.spectrumRect;

      if (!rect) {
        this.getSpectrumRect();
        return;
      }

      // 计算相对位置
      let x = touch.pageX - rect.left;
      let y = touch.pageY - rect.top;

      // 限制在光谱区域内
      x = Math.max(0, Math.min(x, rect.width));
      y = Math.max(0, Math.min(y, rect.height));

      // 更新选择器位置
      this.setData({
        'spectrumSelector.x': x,
        'spectrumSelector.y': y
      });

      // 根据位置和当前色相计算颜色
      this.updateColorFromSpectrumPosition();
    },

    // 根据光谱选择器位置更新颜色
    updateColorFromSpectrumPosition: function() {
      const { spectrumSelector, spectrumRect, currentHue } = this.data;

      if (!spectrumRect) return;

      // 计算饱和度 (x轴，从左到右递增)
      // 0 = 白色 (无饱和度)，1 = 完全饱和
      const s = Math.round(100 - ((spectrumRect.width - spectrumSelector.x) / spectrumRect.width) * 100);

      // 计算明度 (y轴，从上到下递减)
      // 0 = 白色/有色光，0.5 = 正常，1 = 黑色
      const v = Math.round(100 - (spectrumSelector.y / spectrumRect.height) * 100);

      console.log('光谱位置计算:', '位置:', spectrumSelector, '色相:', currentHue, '饱和度:', s, '明度:', v);

      // 转换HSV到RGB
      const rgb = this.hsvToRgb(currentHue, s, v);

      // 更新颜色
      this.setData({
        r: rgb.r,
        g: rgb.g,
        b: rgb.b
      });

      // 更新其他颜色值
      this.updateFromRgb();
    },

    // HSV转RGB
    hsvToRgb: function(h, s, v) {
      h = h / 360;
      s = s / 100;
      v = v / 100;

      let r, g, b;

      if (s === 0) {
        r = g = b = v;
      } else {
        const i = Math.floor(h * 6);
        const f = h * 6 - i;
        const p = v * (1 - s);
        const q = v * (1 - f * s);
        const t = v * (1 - (1 - f) * s);

        switch (i % 6) {
          case 0: r = v; g = t; b = p; break;
          case 1: r = q; g = v; b = p; break;
          case 2: r = p; g = v; b = t; break;
          case 3: r = p; g = q; b = v; break;
          case 4: r = t; g = p; b = v; break;
          case 5: r = v; g = p; b = q; break;
        }
      }

      return {
        r: Math.round(r * 255),
        g: Math.round(g * 255),
        b: Math.round(b * 255)
      };
    },

    // 解析颜色值
    parseColor: function(color) {
      try {
        // 确保color是字符串
        if (typeof color !== 'string') {
          console.warn('parseColor: color must be a string, got:', typeof color);
          return;
        }

        // 移除#号
        color = color.replace(/^#/, '');

        // 如果是3位HEX，转换为6位
        if (color.length === 3) {
          color = color[0] + color[0] + color[1] + color[1] + color[2] + color[2];
        }

        // 验证HEX格式
        if (!/^[0-9A-Fa-f]{6}$/.test(color)) {
          console.warn('parseColor: invalid HEX format:', color);
          return;
        }

        // 解析RGB值
        const r = parseInt(color.substring(0, 2), 16);
        const g = parseInt(color.substring(2, 4), 16);
        const b = parseInt(color.substring(4, 6), 16);

        // 验证RGB值
        if (isNaN(r) || isNaN(g) || isNaN(b)) {
          console.warn('parseColor: invalid RGB values:', { r, g, b });
          return;
        }

        // 计算HSL值
        const hsl = this.rgbToHsl(r, g, b);

        // 计算CMYK值
        const cmyk = this.rgbToCmyk(r, g, b);

        // 确保hexInput值有效
        const hexInputValue = color.toUpperCase();
        const hexValue = '#' + hexInputValue;

        // 创建一个基础的数据更新对象
        const baseData = {
          r,
          g,
          b,
          h: hsl.h,
          s: hsl.s,
          l: hsl.l,
          currentHue: hsl.h, // 更新当前色相值
          cmyk,
          hexValue: hexValue,
          hexInput: hexInputValue // 确保HEX输入框显示的值有效
        };

        // 直接更新UI
        this.setData(baseData);

      // 使用wx.nextTick确保在下一个渲染周期更新光谱相关UI
      wx.nextTick(() => {
        // 如果当前是光谱模式，更新色相滑块位置和光谱背景
        if (this.data.pickerMode === 'spectrum' && this.data.hueSliderRect) {
          // 先隐藏选择器，避免闪烁
          this.setData({
            spectrumSelectorReady: false
          });

          const huePosition = (hsl.h / 360) * this.data.hueSliderRect.width;

          // 计算并更新光谱选择器位置
          if (this.data.spectrumRect) {
            // 根据HSV模型计算位置
            // 将HSL转换为HSV
            const hsv = this.hslToHsv(hsl.h, hsl.s, hsl.l);

            // 饱和度从左到右递增
            const x = this.data.spectrumRect.width - ((100 - hsv.s) / 100) * this.data.spectrumRect.width;
            const y = (1 - hsv.v / 100) * this.data.spectrumRect.height;

            // 一次性更新所有数据，避免多次渲染
            this.setData({
              hueSliderPosition: huePosition,
              'spectrumSelector.x': x,
              'spectrumSelector.y': y,
              spectrumSelectorReady: true // 现在可以显示选择器了
            });

            this.updateSpectrumBackground();
          } else {
            this.setData({
              hueSliderPosition: huePosition
            });
            this.updateSpectrumBackground();
          }
        }
      });

      console.log('解析颜色:', color, '结果:', this.data);
      } catch (error) {
        console.error('parseColor error:', error);
        // 发生错误时，确保hexInput有默认值
        if (!this.data.hexInput) {
          this.setData({
            hexInput: 'C83C23'
          });
        }
      }
    },

    // HSL转HSV
    hslToHsv: function(h, s, l) {
      s /= 100;
      l /= 100;

      let v;
      let sv;

      if (l === 0) {
        return { h, s: 0, v: 0 };
      }

      if (s === 0) {
        return { h, s: 0, v: l * 100 };
      }

      v = l + s * Math.min(l, 1 - l);
      sv = 2 * (1 - l / v);

      return {
        h: h,
        s: Math.round(sv * 100),
        v: Math.round(v * 100)
      };
    },

    // 切换标签
    switchTab: function(e) {
      const tab = e.currentTarget.dataset.tab;

      // 设置当前标签
      this.setData({
        activeTab: tab
      });

      // 使用统一的方法更新所有颜色值
      this.updateAllColorValues();

      // 如果切换到HEX选项卡，确保在下一个渲染周期更新输入框的值
      if (tab === 'hex') {
        wx.nextTick(() => {
          const hexInputValue = this.data.hexValue.substring(1);
          this.setData({
            hexInput: hexInputValue // 更新HEX输入框显示的值
          });
        });
      }

      console.log('切换到标签:', tab, '颜色值:', this.data);
    },

    // 更新所有颜色值 - 使用统一的updateAllColorValues函数

    // RGB滑块变化
    onRgbChange: function(e) {
      const type = e.currentTarget.dataset.type;
      const value = parseInt(e.detail.value);

      const data = {};
      data[type] = value;

      this.setData(data);
      this.updateFromRgb();
    },

    // RGB输入框变化
    onRgbInput: function(e) {
      const type = e.currentTarget.dataset.type;
      let value = parseInt(e.detail.value);

      // 验证输入值是否在有效范围内
      if (isNaN(value)) {
        value = 0;
      } else if (value < 0) {
        value = 0;
      } else if (value > 255) {
        value = 255;
      }

      const data = {};
      data[type] = value;

      this.setData(data);
      this.updateFromRgb();
    },

    // HSL滑块变化
    onHslChange: function(e) {
      const type = e.currentTarget.dataset.type;
      const value = parseInt(e.detail.value);

      const data = {};
      data[type] = value;

      this.setData(data);
      this.updateFromHsl();
    },

    // HSL输入框变化
    onHslInput: function(e) {
      const type = e.currentTarget.dataset.type;
      let value = parseInt(e.detail.value);

      // 验证输入值是否在有效范围内
      if (isNaN(value)) {
        value = 0;
      } else if (value < 0) {
        value = 0;
      } else if (type === 'h' && value > 360) {
        value = 360;
      } else if ((type === 's' || type === 'l') && value > 100) {
        value = 100;
      }

      const data = {};
      data[type] = value;

      this.setData(data);
      this.updateFromHsl();
    },

    // CMYK滑块变化
    onCmykChange: function(e) {
      const type = e.currentTarget.dataset.type;
      const value = parseInt(e.detail.value);

      // 创建CMYK对象的副本，不使用展开运算符
      const cmykCopy = {
        c: this.data.cmyk.c,
        m: this.data.cmyk.m,
        y: this.data.cmyk.y,
        k: this.data.cmyk.k
      };

      // 更新特定属性
      cmykCopy[type] = value;

      // 更新数据
      this.setData({
        cmyk: cmykCopy
      });

      this.updateFromCmyk();
    },

    // CMYK输入框变化
    onCmykInput: function(e) {
      const type = e.currentTarget.dataset.type;
      let value = parseInt(e.detail.value);

      // 验证输入值是否在有效范围内
      if (isNaN(value)) {
        value = 0;
      } else if (value < 0) {
        value = 0;
      } else if (value > 100) {
        value = 100;
      }

      // 创建CMYK对象的副本，不使用展开运算符
      const cmykCopy = {
        c: this.data.cmyk.c,
        m: this.data.cmyk.m,
        y: this.data.cmyk.y,
        k: this.data.cmyk.k
      };

      // 更新特定属性
      cmykCopy[type] = value;

      // 更新数据
      this.setData({
        cmyk: cmykCopy
      });

      this.updateFromCmyk();
    },

    // HEX输入框变化
    onHexInput: function(e) {
      let value = e.detail.value;

      // 转换为大写
      value = value.toUpperCase();

      // 先更新输入框显示值
      this.setData({
        hexInput: value
      });

      // 验证HEX格式
      if (/^[0-9A-F]{6}$/.test(value)) {
        // 使用防抖避免频繁更新
        if (this.hexInputTimer) {
          clearTimeout(this.hexInputTimer);
        }

        this.hexInputTimer = setTimeout(() => {
          const hexValue = '#' + value;
          // 解析颜色并更新所有颜色值
          this.parseColor(hexValue);
          // 触发颜色变化事件
          this.triggerEvent('change', { color: hexValue });
          console.log('HEX输入变化:', hexValue);
        }, 300);
      } else if (value.length === 6) {
        // 尝试修复无效的HEX值
        let fixedValue = '';
        for (let i = 0; i < 6; i++) {
          const char = value[i];
          if (/[0-9A-F]/.test(char)) {
            fixedValue += char;
          } else {
            fixedValue += '0'; // 替换无效字符为0
          }
        }

        if (fixedValue !== value) {
          if (this.hexInputTimer) {
            clearTimeout(this.hexInputTimer);
          }

          this.hexInputTimer = setTimeout(() => {
            const hexValue = '#' + fixedValue;
            this.parseColor(hexValue);
            this.triggerEvent('change', { color: hexValue });
            console.log('修复的HEX输入:', hexValue);
          }, 300);
        }
      }
    },

    // HEX输入框失焦
    onHexBlur: function(e) {
      let value;

      // 检查是否有事件对象传入
      if (e && e.detail) {
        value = e.detail.value;
      } else {
        // 如果没有事件对象，使用当前的hexInput值
        value = this.data.hexInput;
      }

      // 转换为大写
      value = value.toUpperCase();

      // 如果不是6位，尝试补全
      if (value.length > 0 && value.length < 6) {
        // 补全到6位
        while (value.length < 6) {
          value += '0';
        }

        const hexValue = '#' + value;

        // 解析颜色并更新所有颜色值
        this.parseColor(hexValue);

        // 触发颜色变化事件
        this.triggerEvent('change', { color: hexValue });

        console.log('HEX输入补全:', hexValue);
      } else if (value.length === 0) {
        // 如果输入框为空，恢复为当前颜色值
        const { hexValue } = this.data;
        const hexInputValue = hexValue ? hexValue.substring(1) : 'C83C23';

        this.setData({
          hexInput: hexInputValue
        });
      } else if (value.length === 6 && /^[0-9A-F]{6}$/.test(value)) {
        // 如果是6位有效的HEX值，直接解析
        const hexValue = '#' + value;

        // 解析颜色并更新所有颜色值
        this.parseColor(hexValue);

        // 触发颜色变化事件
        this.triggerEvent('change', { color: hexValue });
      }

      // 使用nextTick确保数据同步
      wx.nextTick(() => {
        const { hexValue } = this.data;
        if (hexValue) {
          const hexInputValue = hexValue.substring(1);
          this.setData({
            hexInput: hexInputValue
          });
        }
      });
    },

    // 根据RGB更新其他值
    updateFromRgb: function() {
      const { r, g, b } = this.data;

      // 计算HEX值
      const hexValue = '#' + this.rgbToHex(r, g, b);

      // 使用统一的方法更新所有颜色值
      this.updateAllColorValues();

      // 触发颜色变化事件
      this.triggerEvent('change', { color: hexValue });

      console.log('RGB更新:', r, g, b, '结果:', this.data);
    },

    // 根据HSL更新其他值
    updateFromHsl: function() {
      const { h, s, l } = this.data;

      // 计算RGB值
      const rgb = this.hslToRgb(h, s, l);

      // 更新RGB值
      this.setData({
        r: rgb.r,
        g: rgb.g,
        b: rgb.b
      });

      // 计算HEX值
      const hexValue = '#' + this.rgbToHex(rgb.r, rgb.g, rgb.b);

      // 使用统一的方法更新所有颜色值
      this.updateAllColorValues();

      // 触发颜色变化事件
      this.triggerEvent('change', { color: hexValue });

      console.log('HSL更新:', h, s, l, '结果:', this.data);
    },

    // 根据CMYK更新其他值
    updateFromCmyk: function() {
      const { cmyk } = this.data;

      // 计算RGB值
      const rgb = this.cmykToRgb(cmyk.c, cmyk.m, cmyk.y, cmyk.k);

      // 更新RGB值
      this.setData({
        r: rgb.r,
        g: rgb.g,
        b: rgb.b
      });

      // 计算HEX值
      const hexValue = '#' + this.rgbToHex(rgb.r, rgb.g, rgb.b);

      // 使用统一的方法更新所有颜色值
      this.updateAllColorValues();

      // 触发颜色变化事件
      this.triggerEvent('change', { color: hexValue });

      console.log('CMYK更新:', cmyk, '结果:', this.data);
    },

    // 确认选择
    confirmColor: function() {
      this.triggerEvent('confirm', { color: this.data.hexValue });
    },

    // 取消选择
    cancelColor: function() {
      this.triggerEvent('cancel');
    },

    // 复制HEX值
    copyHexValue: function() {
      const hexValue = '#' + this.data.hexInput;
      wx.setClipboardData({
        data: hexValue,
        success: () => {
          wx.showToast({
            title: '已复制色值',
            icon: 'success',
            duration: 1500
          });
        }
      });
    },

    // 粘贴HEX值
    pasteHexValue: function() {
      wx.getClipboardData({
        success: (res) => {
          let clipboardData = res.data;
          // 尝试提取HEX值
          if (clipboardData) {
            // 移除可能存在的#号
            clipboardData = clipboardData.replace(/^#/, '');

            // 验证是否是有效的HEX值
            if (/^[0-9A-Fa-f]{3,6}$/.test(clipboardData)) {
              // 如果是3位HEX，转换为6位
              if (clipboardData.length === 3) {
                clipboardData = clipboardData[0] + clipboardData[0] +
                                clipboardData[1] + clipboardData[1] +
                                clipboardData[2] + clipboardData[2];
              }

              // 确保是6位HEX
              if (clipboardData.length === 6) {
                const hexValue = '#' + clipboardData.toUpperCase();

                // 直接使用parseColor方法解析颜色并更新所有颜色值
                this.parseColor(hexValue);

                // 触发颜色变化事件
                this.triggerEvent('change', { color: hexValue });

                // 不显示吐司提示
              } else {
                // 不显示吐司提示
                console.log('无效的色值');
              }
            } else {
              // 不显示吐司提示
              console.log('剪贴板内容不是有效色值');
            }
          }
        }
      });
    },

    // RGB转HEX
    rgbToHex: function(r, g, b) {
      const toHex = x => {
        const hex = x.toString(16);
        return hex.length === 1 ? '0' + hex : hex;
      };

      return (toHex(r) + toHex(g) + toHex(b)).toUpperCase();
    },

    // RGB转HSL
    rgbToHsl: function(r, g, b) {
      r /= 255;
      g /= 255;
      b /= 255;

      const max = Math.max(r, g, b);
      const min = Math.min(r, g, b);
      let h, s, l = (max + min) / 2;

      if (max === min) {
        h = s = 0; // 灰色
      } else {
        const d = max - min;
        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

        switch (max) {
          case r: h = (g - b) / d + (g < b ? 6 : 0); break;
          case g: h = (b - r) / d + 2; break;
          case b: h = (r - g) / d + 4; break;
        }

        h /= 6;
      }

      return {
        h: Math.round(h * 360),
        s: Math.round(s * 100),
        l: Math.round(l * 100)
      };
    },

    // HSL转RGB
    hslToRgb: function(h, s, l) {
      h /= 360;
      s /= 100;
      l /= 100;

      let r, g, b;

      if (s === 0) {
        r = g = b = l; // 灰色
      } else {
        const hue2rgb = (p, q, t) => {
          if (t < 0) t += 1;
          if (t > 1) t -= 1;
          if (t < 1/6) return p + (q - p) * 6 * t;
          if (t < 1/2) return q;
          if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
          return p;
        };

        const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
        const p = 2 * l - q;

        r = hue2rgb(p, q, h + 1/3);
        g = hue2rgb(p, q, h);
        b = hue2rgb(p, q, h - 1/3);
      }

      return {
        r: Math.round(r * 255),
        g: Math.round(g * 255),
        b: Math.round(b * 255)
      };
    },

    // RGB转CMYK
    rgbToCmyk: function(r, g, b) {
      r /= 255;
      g /= 255;
      b /= 255;

      const k = 1 - Math.max(r, g, b);
      const c = (1 - r - k) / (1 - k) || 0;
      const m = (1 - g - k) / (1 - k) || 0;
      const y = (1 - b - k) / (1 - k) || 0;

      return {
        c: Math.round(c * 100),
        m: Math.round(m * 100),
        y: Math.round(y * 100),
        k: Math.round(k * 100)
      };
    },

    // CMYK转RGB
    cmykToRgb: function(c, m, y, k) {
      c /= 100;
      m /= 100;
      y /= 100;
      k /= 100;

      const r = 255 * (1 - c) * (1 - k);
      const g = 255 * (1 - m) * (1 - k);
      const b = 255 * (1 - y) * (1 - k);

      return {
        r: Math.round(r),
        g: Math.round(g),
        b: Math.round(b)
      };
    },

    // HSL转HSV
    hslToHsv: function(h, s, l) {
      // 将百分比转换为小数
      s /= 100;
      l /= 100;

      // HSL到HSV的转换
      let v = l + s * Math.min(l, 1 - l);
      let sv = v === 0 ? 0 : 2 * (1 - l / v);

      return {
        h: h, // 色相保持不变
        s: Math.round(sv * 100), // 转回百分比
        v: Math.round(v * 100) // 转回百分比
      };
    },

    // 更新所有颜色值，确保它们保持同步
    updateAllColorValues: function() {
      try {
        // 获取当前RGB值
        const { r, g, b } = this.data;

        // 验证RGB值
        if (typeof r !== 'number' || typeof g !== 'number' || typeof b !== 'number') {
          console.warn('Invalid RGB values:', { r, g, b });
          return;
        }

        // 验证RGB值范围
        if (r < 0 || r > 255 || g < 0 || g > 255 || b < 0 || b > 255) {
          console.warn('RGB values out of range:', { r, g, b });
          return;
        }

        // 计算HEX值
        const hexValue = '#' + this.rgbToHex(r, g, b);
        const hexInputValue = hexValue.substring(1);

        // 计算HSL值
        const hsl = this.rgbToHsl(r, g, b);

        // 计算CMYK值
        const cmyk = this.rgbToCmyk(r, g, b);

        // 验证计算结果
        if (!hexInputValue || hexInputValue.length !== 6) {
          console.warn('Invalid hexInput calculated:', hexInputValue);
          return;
        }

        // 一次性更新所有值，避免多次setData调用
        this.setData({
          hexValue: hexValue,
          hexInput: hexInputValue,
          h: hsl.h,
          s: hsl.s,
          l: hsl.l,
          cmyk: cmyk
        });

        console.log('更新所有颜色值:', { hexValue, hsl, cmyk });
      } catch (error) {
        console.error('更新颜色值时出错:', error);
        // 确保hexInput有默认值
        if (!this.data.hexInput) {
          this.setData({
            hexInput: 'C83C23'
          });
        }
      }
    },


  }
})
