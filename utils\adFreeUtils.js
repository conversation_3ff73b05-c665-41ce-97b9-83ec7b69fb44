/**
 * 去广告功能工具函数
 */

/**
 * 检查是否已激活去广告功能
 * @returns {boolean} 是否已激活去广告
 */
function isAdFreeActivated() {
  try {
    const adFreeStatus = wx.getStorageSync('adFreeStatus');
    return adFreeStatus === true;
  } catch (error) {
    console.error('检查去广告状态失败', error);
    return false;
  }
}

/**
 * 激活去广告功能
 * @param {string} activationCode 激活码
 * @returns {boolean} 是否激活成功
 */
function activateAdFree(activationCode) {
  if (activationCode === 'KALA666') {
    try {
      wx.setStorageSync('adFreeStatus', true);
      return true;
    } catch (error) {
      console.error('激活去广告功能失败', error);
      return false;
    }
  }
  return false;
}

/**
 * 取消激活去广告功能
 * @returns {boolean} 是否取消成功
 */
function deactivateAdFree() {
  try {
    wx.setStorageSync('adFreeStatus', false);
    return true;
  } catch (error) {
    console.error('取消激活去广告功能失败', error);
    return false;
  }
}

module.exports = {
  isAdFreeActivated,
  activateAdFree,
  deactivateAdFree
};
