/* components/colorWheelCanvas/colorWheelCanvas.wxss */
.color-wheel-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 0; /* 减少内边距，使组件在合并模块中更紧凑 */
  position: relative;
  overflow: visible; /* 确保内容不被裁剪 */
  min-height: 320px; /* 增加高度，适应更大的色轮 */
  transform: scale(1); /* 移除缩放，显示原始大小 */
}

.wheel-instructions {
  width: 100%;
  margin-bottom: 10px;
  padding: 0 10px;
}

.instruction-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  text-align: center;
}

/* 色轮和明度滑块的容器 */
.wheel-and-brightness-container {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 5px;
  position: relative;
  min-height: 300px; /* 增加高度，适应更大的色轮 */
  gap: 20px; /* 增加色轮和明度滑块之间的间距 */
  padding-bottom: 15px; /* 增加底部内边距 */
  transform: scale(1); /* 移除缩放，显示原始大小 */
}

.wheel-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

/* 静态色轮样式 - 平滑渐变版本 */
.static-color-wheel {
  position: relative;
  border-radius: 50%;
  background-image: conic-gradient(
    from 0deg,
    #ff0000, /* 0° - 红色 */
    #ff4000, /* 15° */
    #ff8000, /* 30° */
    #ffbf00, /* 45° */
    #ffff00, /* 60° - 黄色 */
    #bfff00, /* 75° */
    #80ff00, /* 90° */
    #40ff00, /* 105° */
    #00ff00, /* 120° - 绿色 */
    #00ff40, /* 135° */
    #00ff80, /* 150° */
    #00ffbf, /* 165° */
    #00ffff, /* 180° - 青色 */
    #00bfff, /* 195° */
    #0080ff, /* 210° */
    #0040ff, /* 225° */
    #0000ff, /* 240° - 蓝色 */
    #4000ff, /* 255° */
    #8000ff, /* 270° */
    #bf00ff, /* 285° */
    #ff00ff, /* 300° - 洋红色 */
    #ff00bf, /* 315° */
    #ff0080, /* 330° */
    #ff0040, /* 345° */
    #ff0000  /* 360° - 红色 */
  );
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); /* 更轻微的阴影 */
  margin: 0 auto;
  overflow: hidden;
  z-index: 5;
}

/* 饱和度渐变叠加层 - 更接近参考图片的效果 */
.saturation-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.5) 0%,    /* 降低中心点的亮度 */
    rgba(255, 255, 255, 0.4) 10%,   /* 更平滑的过渡 */
    rgba(255, 255, 255, 0.3) 20%,
    rgba(255, 255, 255, 0.2) 30%,
    rgba(255, 255, 255, 0.15) 40%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.05) 65%,
    rgba(255, 255, 255, 0.02) 80%,
    transparent 95%
  );
  z-index: 2; /* 确保在色轮上方 */
}

/* 色轮边框 */
.color-wheel-border {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  border: 1px solid rgba(0, 0, 0, 0.08); /* 更淡的边框 */
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.05) inset; /* 内阴影效果 */
  pointer-events: none;
  z-index: 3; /* 确保在饱和度层上方 */
}

/* 颜色连线 - 参考图片样式 */
.color-connection-line {
  position: absolute;
  height: 2px; /* 增加线条宽度 */
  transform-origin: left center;
  z-index: 4; /* 确保在边框上方，但在颜色点下方 */
  pointer-events: none;
  border-radius: 0; /* 扁平效果 */
  opacity: 0.9; /* 稍微透明一点 */
  transition: all 0.2s ease; /* 平滑过渡效果 */
}

/* 基础颜色点到中心点的连线 */
.base-to-center {
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.1); /* 更轻微的阴影 */
}

/* 中心点到配色方案颜色点的连线 */
.center-to-palette {
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.1);
}

/* 基础颜色点到配色方案颜色点的直接连线 */
.base-to-palette {
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.1);
}

/* 配色方案颜色点之间的连线 */
.palette-to-palette {
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.1);
}

/* 配色方案颜色点到基础颜色点的连线 */
.palette-to-base {
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.1);
}

/* 基础颜色点 - 参考图片样式 */
.base-color-point {
  position: absolute;
  width: 20px; /* 增大圆点 */
  height: 20px;
  border-radius: 50%;
  border: 3px solid white; /* 保持边框宽度 */
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.1); /* 轻微阴影 */
  transform: translate(-50%, -50%);
  z-index: 10; /* 确保在最上层 */
  pointer-events: none;
  transition: all 0.2s ease; /* 平滑过渡效果 */
}

/* 配色方案颜色点 - 参考图片样式 */
.palette-color-point {
  position: absolute;
  width: 16px; /* 增大圆点 */
  height: 16px;
  border-radius: 50%;
  border: 3px solid white; /* 保持边框宽度 */
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.1); /* 轻微阴影 */
  transform: translate(-50%, -50%);
  z-index: 9; /* 确保在饱和度层之上，但在基础颜色点之下 */
  pointer-events: none;
  transition: all 0.2s ease; /* 平滑过渡效果 */
}

/* 明度滑块容器 - 竖直方向 */
.brightness-slider-container {
  width: 40px; /* 竖直滑块的宽度 */
  position: relative;
  z-index: 10; /* 提高z-index，确保不被其他元素覆盖 */
  display: flex;
  flex-direction: column;
  align-items: center; /* 居中对齐 */
  justify-content: center;
  margin-left: 5px; /* 与色轮的间距 */
  background-color: rgba(255, 255, 255, 0.5); /* 添加半透明背景 */
  border-radius: 5px;
  padding: 5px 0;
}

/* 明度标签样式 */
.brightness-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  text-align: center;
}

/* 明度值容器 */
.brightness-value-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 8px;
}

/* 明度值显示 */
.brightness-value {
  font-size: 12px;
  color: #666;
  text-align: center;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 2px 5px;
  border-radius: 10px;
  width: 36px;
  box-sizing: border-box;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* 明度指示轨道 - 水平方向 */
.brightness-slider-track {
  height: 10px; /* 更细的轨道 */
  border-radius: 5px;
  position: relative;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); /* 更轻微的阴影 */
  margin: 0 auto;
  overflow: hidden;
  background: linear-gradient(to right, #000000, var(--base-color, #ff0000), #ffffff);
  border: 1px solid rgba(0, 0, 0, 0.05); /* 轻微边框 */
  width: 100%; /* 确保宽度填满容器 */
}

/* 明度指示轨道 - 竖直方向 */
.brightness-slider-track.vertical {
  width: 10px; /* 竖直轨道的宽度 */
  height: 180px; /* 减小高度 */
  border-radius: 5px;
  position: relative;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  margin: 0 auto;
  overflow: hidden;
  background: linear-gradient(to bottom, #ffffff, var(--base-color, #ff0000), #000000);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

/* 明度指示器 - 水平方向 */
.brightness-slider-thumb {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 14px; /* 更小的圆点 */
  height: 14px;
  margin-left: -7px; /* 居中对齐 */
  background-color: #fff; /* 白色背景 */
  border-radius: 50%; /* 确保是圆形 */
  border: 3px solid white; /* 保持边框宽度 */
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.15); /* 轻微阴影 */
  z-index: 2;
  pointer-events: none; /* 禁止交互 */
  transition: all 0.3s ease; /* 平滑过渡效果 */
}

/* 明度指示器 - 竖直方向 */
.brightness-slider-thumb.vertical {
  left: 50%;
  top: 0; /* 会被JS动态设置 */
  transform: translateX(-50%);
  margin-left: 0;
  margin-top: -7px; /* 居中对齐 */
}

.related-colors-info {
  width: 100%;
  margin-top: 15px;
  padding: 0 12px;
}

.related-colors-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  text-align: center;
}
