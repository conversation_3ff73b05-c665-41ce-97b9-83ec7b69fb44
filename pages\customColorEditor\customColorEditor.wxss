/* pages/customColorEditor/customColorEditor.wxss */
.page-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: transparent;
  padding-bottom: 180rpx;
}

/* 预览效果区域 - 模拟实际生成的色卡样式 */
.preview-section {
  margin-top: 40rpx;
  margin-bottom: 30rpx;
  padding: 0 20rpx;
}

.preview-container {
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  background-color: white;
  height: 350rpx !important; /* 强制设置与其他模板相同的高度 */
  box-sizing: border-box; /* 确保尺寸计算正确 */
}

/* 上半部分背景 */
.preview-top-bg {
  padding: 20rpx 30rpx 15rpx; /* 减少内边距，与其他模板一致 */
  text-align: center;
  position: relative;
  height: 175rpx !important; /* 强制设置为总高度的50% */
  box-sizing: border-box; /* 确保padding计算正确 */
}

.preview-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 0;
  position: absolute; /* 改为绝对定位 */
  top: 30rpx; /* 设置位置，与其他模板一致 */
  left: 0;
  right: 0;
}

/* 圆形色块区域 - 圆形色块最终下移 v16.0 */
.preview-circles-area {
  position: absolute !important; /* 绝对定位 */
  top: 175rpx !important; /* 再下移10rpx，从165rpx调整到175rpx */
  left: 0;
  right: 0;
  width: 100%;
  height: 100rpx; /* 增加高度，包含圆形色块和色值文字 */
  display: flex;
  align-items: flex-start; /* 顶部对齐，确保圆形色块位置精确 */
  justify-content: center; /* 水平居中对齐 */
  z-index: 100 !important; /* 高层级，确保在背景之上 */
  pointer-events: none; /* 不阻挡下层元素的点击 */
}

/* 圆形色块完全在下半部分，不需要伪元素背景覆盖 */
.preview-circles-area::before {
  display: none; /* 隐藏伪元素，因为圆形色块已完全在下半部分 */
}

.preview-circles {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  flex-wrap: wrap;
  position: relative;
  z-index: 2;
  width: 100%; /* 确保占满宽度 */
  pointer-events: auto; /* 恢复点击事件 */
}

/* 圆形色块项容器 - 圆形色块在上，色值文字在下 */
.preview-circle-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx; /* 圆形色块和色值文字间距 */
  flex-shrink: 0; /* 防止收缩 */
  position: relative; /* 相对定位 */
}

.preview-circle {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.08);
  /* 圆形色块本身无需额外定位，由父容器控制 */
}

/* 颜色代码 - 显示在圆形下方 */
.preview-color-code {
  font-size: 18rpx;
  font-weight: 600;
  font-family: 'Courier New', monospace;
  text-align: center;
  white-space: nowrap; /* 防止换行 */
}

/* 下半部分背景 */
.preview-bottom-bg {
  padding: 15rpx 30rpx; /* 减少内边距，与其他模板一致 */
  text-align: center;
  margin-top: 0;
  height: 175rpx !important; /* 强制设置为总高度的50% */
  box-sizing: border-box; /* 确保padding计算正确 */
  position: relative; /* 添加相对定位 */
}

/* 下半部分内容容器 - P02样式 */
.preview-bottom-content-p02 {
  position: absolute;
  top: 50%; /* 垂直居中 */
  left: 0;
  right: 0;
  transform: translateY(-50%); /* 垂直居中 */
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 正方形色块预览 - P02样式 */
.preview-square-blocks {
  display: flex;
  justify-content: center;
  gap: 0; /* 色块间距设为0，紧挨着排列 */
  flex-wrap: wrap;
}

.preview-square-block {
  width: 40rpx; /* 正方形尺寸 */
  height: 40rpx;
  border-radius: 0; /* 移除圆角，保持纯正方形 */
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.08); /* 阴影 */
}

/* P01样式预览（同色系模板） - 强制统一高度 v2.0 */
.preview-container-p01 {
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  padding: 40rpx 30rpx;
  text-align: center;
  height: 350rpx !important; /* 强制设置与P03相同的高度 */
  position: relative; /* 添加相对定位 */
  box-sizing: border-box; /* 确保padding计算正确 */
}

.preview-title-p01 {
  font-size: 32rpx;
  font-weight: 600;
  position: absolute; /* 改为绝对定位 */
  top: 20rpx; /* 设置顶部位置 */
  left: 0;
  right: 0;
  margin-bottom: 0; /* 移除margin */
}

/* 长条形色块预览 */
.preview-long-blocks {
  display: flex;
  justify-content: center;
  gap: 30rpx; /* 增大间距，从20rpx增加到30rpx */
  flex-wrap: wrap;
  position: absolute; /* 改为绝对定位 */
  top: 100rpx; /* 下移位置，从80rpx增加到100rpx */
  left: 0;
  right: 0;
  margin-bottom: 0; /* 移除margin */
}

.preview-long-block {
  width: 40rpx;
  height: 120rpx;
  border-radius: 20rpx;
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.08);
}

/* 颜色代码预览 */
.preview-color-codes-p01 {
  display: flex;
  justify-content: center;
  gap: 30rpx; /* 增大间距，与长条形色块保持一致 */
  flex-wrap: wrap;
  position: absolute; /* 改为绝对定位 */
  top: 230rpx; /* 下移位置，从200rpx增加到230rpx */
  left: 0;
  right: 0;
  margin-bottom: 0; /* 移除margin */
}

.preview-color-code-p01 {
  font-size: 14rpx; /* 调小字体，从18rpx减少到14rpx */
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

/* 圆形色块预览 */
.preview-circles-p01 {
  display: flex;
  justify-content: center;
  gap: 30rpx; /* 增大间距，与其他元素保持一致 */
  flex-wrap: wrap;
  position: absolute; /* 改为绝对定位 */
  top: 280rpx; /* 下移位置，从250rpx增加到280rpx */
  left: 0;
  right: 0;
}

.preview-circle-p01 {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.08);
}

/* 颜色选择区域 */
.colors-section {
  margin-bottom: 20rpx;
}

.colors-title {
  font-size: 24rpx;
  color: #333;
  margin: 12rpx auto 16rpx;
  padding: 16rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f7f7f7;
  max-width: 94%;
  border-left: 2rpx solid #07c160;
  min-height: 60rpx;
}

.title-left {
  display: flex;
  align-items: flex-start;
  flex: 1;
}

.title-text {
  display: flex;
  flex-direction: column;
  line-height: 1.4;
}

.info-dot {
  width: 6rpx;
  height: 6rpx;
  margin-right: 8rpx;
  margin-top: 10rpx;
  background-color: #07c160;
  border-radius: 50%;
  flex-shrink: 0;
}

.color-count-controls {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.count-btn {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #07c160;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  transition: all 0.2s;
}

.count-btn:active {
  transform: scale(0.9);
  opacity: 0.8;
}

.count-display {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  min-width: 30rpx;
  text-align: center;
}

/* 颜色网格 - 一行显示2个 */
.colors-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
  padding: 15rpx 20rpx;
}

.color-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 15rpx;
  background-color: white;
  border-radius: 12rpx;
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.color-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.08);
}

.color-block {
  width: 80rpx;
  height: 80rpx;
  border-radius: 10rpx;
  position: relative;
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.08);
  margin-right: 15rpx;
  flex-shrink: 0;
}

/* 棋盘格背景 */
.color-block::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(45deg, #eee 25%, transparent 25%),
    linear-gradient(-45deg, #eee 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, #eee 75%),
    linear-gradient(-45deg, transparent 75%, #eee 75%);
  background-size: 12rpx 12rpx;
  background-position: 0 0, 0 6rpx, 6rpx -6rpx, -6rpx 0px;
  z-index: -1;
  opacity: 0.3;
  border-radius: 12rpx;
}

.color-number {
  position: absolute;
  right: 4rpx;
  bottom: 4rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
  color: #333;
  font-size: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
}



/* 操作按钮 - 右侧水平并列 */
.color-actions {
  display: flex;
  flex-direction: row;
  gap: 8rpx;
  flex: 1;
  justify-content: flex-end;
  align-items: center;
}

.edit-btn,
.delete-btn {
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
  font-size: 20rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  min-width: 50rpx;
}

.edit-btn {
  background-color: #07c160;
  color: white;
}

.edit-btn:active {
  background-color: #06ad56;
  transform: scale(0.95);
}

.delete-btn {
  background-color: #ff4757;
  color: white;
}

.delete-btn:active {
  background-color: #ff3742;
  transform: scale(0.95);
}

/* 添加颜色按钮 */
.add-color-section {
  padding: 20rpx;
  text-align: center;
}

.add-color-btn {
  display: inline-flex;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx 40rpx;
  background-color: #f8f9fa;
  border: 2rpx dashed #07c160;
  border-radius: 12rpx;
  color: #07c160;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.add-color-btn:active {
  background-color: #e8f5e8;
  transform: scale(0.95);
}

.add-icon {
  font-size: 32rpx;
  font-weight: bold;
}

/* 背景颜色选择器弹窗样式 */
.color-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
}

.color-picker-container {
  background-color: white;
  border-radius: 24rpx;
  width: 100%;
  max-width: 700rpx;
  max-height: 85vh;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
}

.color-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.color-picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.color-picker-close {
  font-size: 40rpx;
  color: #999999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.color-picker-close:active {
  background-color: #f0f0f0;
}

.color-picker-content {
  padding: 0;
  max-height: 70vh;
  overflow-y: auto;
}

/* 设置区域 */
.settings-section {
  margin: 20rpx;
  background-color: white;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.04);
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:active {
  background-color: #f8f9fa;
}

.setting-label {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.setting-icon {
  font-size: 24rpx;
}

.setting-value {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 26rpx;
  color: #666;
  font-family: 'Courier New', monospace;
}

.bg-color-preview {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  border: 2rpx solid #e9ecef;
}

.setting-arrow {
  font-size: 28rpx;
  color: #ccc;
}

/* 底部按钮 */
.btn-container {
  padding: 20rpx 30rpx 50rpx;
  width: 100%;
  box-sizing: border-box;
  position: fixed;
  bottom: 0;
  left: 0;
  background-color: white;
  box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.04);
  z-index: 100;
  border-top: 1rpx solid rgba(0, 0, 0, 0.03);
}

.next-btn {
  background-color: #07c160;
  color: white;
  border-radius: 8rpx;
  font-size: 30rpx;
  padding: 18rpx 0;
  width: 100%;
  box-shadow: 0 1rpx 4rpx rgba(7, 193, 96, 0.12);
  transition: all 0.2s ease;
  font-weight: 500;
}

.next-btn:active {
  opacity: 0.9;
  transform: scale(0.98);
}





/* 标题编辑器弹窗 */
.title-editor-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
}

.title-editor-container {
  background-color: white;
  border-radius: 20rpx;
  width: 100%;
  max-width: 600rpx;
  overflow: hidden;
}

.title-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.title-editor-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.title-editor-close {
  font-size: 40rpx;
  color: #999999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.title-editor-close:active {
  background-color: #f0f0f0;
}

.title-editor-content {
  padding: 30rpx;
}

.title-input-section {
  margin-bottom: 20rpx;
}

.title-input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: white;
}

.title-input:focus {
  border-color: #ff6b9d;
}

.title-editor-actions {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

/* P03样式预览（落日漫旅模板） - 与海盐气泡模板高度统一 v2.0 */
.preview-container-p03 {
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  background-color: white;
  height: 350rpx !important; /* 强制调整为350rpx，与海盐气泡模板一致 */
  box-sizing: border-box; /* 确保尺寸计算正确 */
}

/* 上半部分背景 - P03样式 */
.preview-top-bg-p03 {
  padding: 20rpx 30rpx 15rpx; /* 减少内边距 */
  text-align: center;
  position: relative;
  height: 175rpx !important; /* 强制设置为总高度的50% */
  box-sizing: border-box; /* 确保padding计算正确 */
}

.preview-title-p03 {
  font-size: 32rpx; /* 适中的字体大小 */
  font-weight: bold;
  margin-bottom: 15rpx;
  position: absolute;
  top: 30rpx; /* 调整位置，适应紧凑布局 */
  left: 0;
  right: 0;
}

/* 旋转方形色块预览 - 位于整个容器的总高度居中 */
.preview-diamond-blocks {
  position: absolute;
  top: 175rpx; /* 位于总高度350rpx的中心位置 */
  left: 0;
  right: 0;
  transform: translateY(-50%); /* 垂直居中 */
  display: flex;
  justify-content: center;
  gap: 10rpx; /* 减少间距 */
  flex-wrap: wrap;
  z-index: 10; /* 确保在上层显示 */
}

.preview-diamond-block {
  width: 50rpx; /* 调大尺寸，从40rpx增加到50rpx */
  height: 50rpx;
  border-radius: 8rpx; /* 相应增大圆角 */
  transform: rotate(45deg); /* 45度旋转 */
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.12); /* 增强阴影效果 */
}

/* 下半部分背景 - P03样式 */
.preview-bottom-bg-p03 {
  padding: 15rpx 30rpx; /* 减少内边距 */
  text-align: center;
  position: relative;
  height: 175rpx !important; /* 强制设置为总高度的50% */
  box-sizing: border-box; /* 确保padding计算正确 */
}

/* 颜色代码和圆形色块容器 - P03样式 */
.preview-bottom-content-p03 {
  position: absolute;
  top: 50%; /* 垂直居中 */
  left: 0;
  right: 0;
  transform: translateY(-50%); /* 垂直居中 */
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx; /* 颜色代码和圆形色块之间的间距 */
}

/* 颜色代码预览 - P03样式 */
.preview-color-codes-p03 {
  display: flex;
  justify-content: center;
  gap: 12rpx; /* 减少间距 */
  flex-wrap: wrap;
}

.preview-color-code-p03 {
  font-size: 16rpx; /* 适中的字体大小 */
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

/* 圆形色块预览 - P03样式 */
.preview-circles-p03 {
  display: flex;
  justify-content: center;
  gap: 12rpx; /* 减少间距 */
  flex-wrap: wrap;
}

.preview-circle-p03 {
  width: 30rpx; /* 减小尺寸，适应紧凑布局 */
  height: 30rpx;
  border-radius: 50%;
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.08); /* 适中的阴影 */
}

/* 底部按钮 */
.btn-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background-color: white;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;
}

.next-btn {
  width: 100%;
  height: 88rpx;
  background-color: #07c160;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.next-btn:active {
  background-color: #06ad56;
  transform: scale(0.98);
}

.picker-btn {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.picker-btn.cancel {
  background-color: #f8f9fa;
  color: #666;
}

.picker-btn.confirm {
  background-color: #07c160;
  color: white;
}

.picker-btn:active {
  transform: scale(0.95);
}

/* ==================== P04 春日樱语模板预览样式 ==================== */

/* P04模板预览卡片 */
.preview-card-p04 {
  width: 100%;
  height: 350rpx !important; /* 与其他模板保持一致的高度 */
  position: relative;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  background-color: white;
  box-sizing: border-box;
}

/* 上半部分背景 - P04样式 */
.preview-top-bg-p04 {
  height: 175rpx !important; /* 50% */
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 20rpx 30rpx 15rpx;
  box-sizing: border-box;
}

/* 标题样式 - P04样式 */
.preview-title-p04 {
  position: absolute;
  top: 30rpx;
  font-size: 32rpx;
  font-weight: 650;
  text-align: center;
  white-space: nowrap;
  left: 0;
  right: 0;
}

/* 花瓣形状色块容器 - P04样式 */
.preview-petal-shapes {
  position: absolute;
  top: 175rpx; /* 位于总高度350rpx的中心位置 */
  left: 0;
  right: 0;
  transform: translateY(-50%); /* 垂直居中 */
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15rpx;
  flex-wrap: wrap;
  width: 100%;
  z-index: 10; /* 确保在上层显示 */
}

/* 花瓣形状Canvas - P04样式 */
.preview-petal-canvas {
  width: 70rpx;
  height: 70rpx;
  /* 移除边框阴影 */
}

/* 下半部分背景 - P04样式 */
.preview-bottom-bg-p04 {
  position: absolute;
  top: 175rpx; /* 50% */
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15rpx 30rpx;
  box-sizing: border-box;
}

/* 下半部分内容容器 - P04样式 */
.preview-bottom-content-p04 {
  position: absolute;
  top: 50%; /* 垂直居中 */
  left: 0;
  right: 0;
  transform: translateY(-50%); /* 垂直居中 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  gap: 20rpx;
}

/* 颜色代码容器 - P04样式 */
.preview-color-codes-p04 {
  display: flex;
  justify-content: center;
  gap: 12rpx;
  flex-wrap: wrap;
}

/* 单个颜色代码 - P04样式 */
.preview-color-code-p04 {
  font-size: 16rpx;
  color: #7D7D7D;
  font-weight: 650;
  text-align: center;
  font-family: 'Courier New', monospace;
}

/* 圆形色块容器 - P04样式 */
.preview-circles-p04 {
  display: flex;
  justify-content: center;
  gap: 12rpx;
  flex-wrap: wrap;
}

/* 星形圆形色块容器 - P04样式 */
.preview-star-circles {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12rpx;
  flex-wrap: wrap;
}

/* 星形Canvas - P04样式 */
.preview-star-canvas {
  width: 30rpx;
  height: 30rpx;
  /* 移除边框阴影 */
}
