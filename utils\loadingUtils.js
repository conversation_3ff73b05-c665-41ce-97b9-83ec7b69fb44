/**
 * 加载提示工具函数
 * 确保 showLoading 和 hideLoading 正确配对使用
 */

// 调试模式
const DEBUG_LOADING = false; // 生产环境设为 false

// 加载状态管理
let loadingState = {
  isShowing: false,
  count: 0 // 计数器，支持嵌套调用
};

// 调试日志
function debugLog(message, data = {}) {
  if (DEBUG_LOADING) {
    console.log(`[LoadingUtils] ${message}`, {
      ...data,
      state: { ...loadingState }
    });
  }
}

/**
 * 显示加载提示
 * @param {Object} options - 加载提示选项
 * @param {string} options.title - 提示文字
 * @param {boolean} options.mask - 是否显示透明蒙层
 */
function showLoading(options = {}) {
  const defaultOptions = {
    title: '加载中...',
    mask: true
  };

  const finalOptions = { ...defaultOptions, ...options };

  debugLog('showLoading called', { options: finalOptions });

  // 增加计数器
  loadingState.count++;

  // 只有在没有显示加载提示时才调用 wx.showLoading
  if (!loadingState.isShowing) {
    loadingState.isShowing = true;
    debugLog('calling wx.showLoading');
    wx.showLoading(finalOptions);
  } else {
    debugLog('skipping wx.showLoading (already showing)');
  }
}

/**
 * 隐藏加载提示
 * @param {boolean} force - 是否强制隐藏（忽略计数器）
 */
function hideLoading(force = false) {
  debugLog('hideLoading called', { force });

  if (force) {
    // 强制隐藏，重置状态
    debugLog('force hiding');
    if (loadingState.isShowing) {
      try {
        debugLog('calling wx.hideLoading (force)');
        wx.hideLoading();
      } catch (e) {
        debugLog('error in wx.hideLoading (force)', { error: e.message });
      }
    }
    loadingState.count = 0;
    loadingState.isShowing = false;
    return;
  }

  // 减少计数器
  if (loadingState.count > 0) {
    loadingState.count--;
    debugLog('decremented counter');
  } else {
    debugLog('warning: count already 0');
  }

  // 只有当计数器为0且确实在显示时才真正隐藏
  if (loadingState.count === 0 && loadingState.isShowing) {
    loadingState.isShowing = false;
    try {
      debugLog('calling wx.hideLoading');
      wx.hideLoading();
    } catch (e) {
      debugLog('error in wx.hideLoading', { error: e.message });
    }
  } else {
    debugLog('skipping wx.hideLoading', {
      count: loadingState.count,
      isShowing: loadingState.isShowing
    });
  }
}

/**
 * 获取当前加载状态
 * @returns {Object} 当前加载状态
 */
function getLoadingState() {
  return {
    isShowing: loadingState.isShowing,
    count: loadingState.count
  };
}

/**
 * 重置加载状态
 * 用于异常情况下的状态重置
 */
function resetLoadingState() {
  // 只有在确实显示了加载提示时才隐藏
  if (loadingState.isShowing) {
    try {
      wx.hideLoading();
    } catch (e) {
      // 忽略可能的错误
    }
  }

  loadingState.isShowing = false;
  loadingState.count = 0;
}

/**
 * 安全的异步操作包装器
 * 自动管理 showLoading 和 hideLoading 的配对
 * @param {Function} asyncFn - 异步函数
 * @param {Object} loadingOptions - 加载提示选项
 * @returns {Promise} 异步操作的结果
 */
async function withLoading(asyncFn, loadingOptions = {}) {
  showLoading(loadingOptions);

  try {
    const result = await asyncFn();
    hideLoading();
    return result;
  } catch (error) {
    hideLoading();
    throw error;
  }
}

module.exports = {
  showLoading,
  hideLoading,
  getLoadingState,
  resetLoadingState,
  withLoading
};
