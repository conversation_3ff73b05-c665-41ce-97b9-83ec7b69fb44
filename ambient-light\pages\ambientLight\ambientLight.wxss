/* pages/ambientLight/ambientLight.wxss */
.page-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  box-sizing: border-box;
  transition: all 0.3s ease;
  position: relative;
}

.full-screen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}
/* 自定义导航栏 */
.custom-nav-container {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  /* 完全透明的背景 */
  background-color: transparent;
}

.status-bar {
  width: 100%;
}

.custom-nav {
  width: 100%;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 左侧区域 */
.nav-left {
  position: relative;
  padding-left: 16px;
  display: flex;
  align-items: center;
  height: 100%;
  width: 87px; /* 与右侧保持对称 */
  z-index: 10; /* 增加z-index，确保在标题之上 */
}

/* 返回按钮 */
.nav-back-btn {
  padding: 11px 25px 11px 0; /* 增加右侧padding，扩大点击区域 */
  margin: -11px 0 -11px -16px;
  display: flex;
  align-items: center;
  height: 100%;
  z-index: 10; /* 增加z-index，确保可点击 */
}

.nav-back-btn:active {
  opacity: 0.5;
}

.nav-back-arrow {
  width: 12px;
  height: 24px;
  -webkit-mask: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='24' viewBox='0 0 12 24'%3E  %3Cpath fill-opacity='.9' fill-rule='evenodd' d='M10 19.438L8.955 20.5l-7.666-7.79a1.02 1.02 0 0 1 0-1.42L8.955 3.5 10 4.563 2.682 12 10 19.438z'/%3E%3C/svg%3E") no-repeat 50% 50%;
  mask: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='24' viewBox='0 0 12 24'%3E  %3Cpath fill-opacity='.9' fill-rule='evenodd' d='M10 19.438L8.955 20.5l-7.666-7.79a1.02 1.02 0 0 1 0-1.42L8.955 3.5 10 4.563 2.682 12 10 19.438z'/%3E%3C/svg%3E") no-repeat 50% 50%;
  -webkit-mask-size: cover;
  mask-size: cover;
  background-color: #ffffff;
  margin-left: 16px;
}

/* 中间标题区域 */
.nav-center {
  font-size: 17px;
  text-align: center;
  position: absolute;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  z-index: 1;
}

.nav-title {
  font-size: 17px;
  font-weight: 500;
  color: #ffffff;
  /* 移除文字阴影 */
}

/* 右侧区域 */
.nav-right {
  width: 87px;
  height: 100%;
}

/* 颜色描述 */
.color-description {
  position: fixed;
  top: 16.67vh; /* 使用视口高度的16.67%，即屏幕的1/6位置 */
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  z-index: 101;
}

.description-text {
  padding: 14rpx 36rpx;
  background-color: rgba(0, 0, 0, 0.4); /* 使用半透明黑色背景，在任何颜色上都能显示清晰 */
  border-radius: 8rpx; /* 减小圆角，使其更加扁平化 */
  font-size: 28rpx;
  color: #FFFFFF; /* 白色文字在深色背景上更加清晰 */
  text-align: center;
  font-weight: 500; /* 增加字体粗细，提高可读性 */
  letter-spacing: 1rpx; /* 增加字间距，提高可读性 */
  /* 移除文字阴影，使其更加扁平化 */
  /* 移除盒子阴影，使其更加扁平化 */
  backdrop-filter: blur(4rpx); /* 添加背景模糊效果，增强可读性 */
  -webkit-backdrop-filter: blur(4rpx); /* Safari 兼容性 */
}
/* 控制面板 */
.controls-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 30rpx 30rpx 0 30rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 30rpx 30rpx 0 0;
  transition: transform 0.3s ease;
  z-index: 100;
  height: 880rpx; /* 调整高度，确保所有元素都能正常显示 */
}

.controls-panel.show {
  transform: translateY(0);
}

.controls-panel.hide {
  transform: translateY(100%);
}

/* 面板标题 */
.panel-title {
  margin-bottom: 20rpx; /* 增加下边距 */
  padding: 10rpx 0 15rpx; /* 调整内边距 */
  text-align: center;
  position: relative;
}

.panel-title text {
  font-size: 34rpx; /* 增加字体大小 */
  color: #333;
  font-weight: 600; /* 增加字体粗细 */
  position: relative;
  display: inline-block;
  padding-bottom: 12rpx; /* 增加下内边距 */
}

.panel-title text::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100rpx; /* 增加下划线宽度 */
  height: 4rpx; /* 减小下划线高度 */
  background-color: #FF5252; /* 改为红色，与图片中的红色下划线一致 */
  border-radius: 2rpx;
}

/* 预设颜色选择 */
.preset-colors-section {
  margin-bottom: 210rpx; /* 保持底部边距 */
  height: 300rpx; /* 保持整体高度 */
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding-top: 5rpx; /* 减少顶部间距 */
  margin-top: 20rpx; /* 增加顶部外边距，使整体下移更多 */
}

.preset-colors-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 24rpx 16rpx; /* 减少列间距，确保色块不会超出屏幕 */
  padding: 15rpx 30rpx 20rpx; /* 增加左右内边距，确保色块不会超出屏幕 */
  box-sizing: border-box; /* 确保内边距不会增加元素的总宽度 */
  width: 100%; /* 确保网格宽度不超过容器 */
}

.preset-color-item {
  aspect-ratio: 1/1;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  position: relative;
  margin-bottom: 42rpx; /* 保持底部间距 */
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  transform: scale(0.8); /* 进一步缩小圆形大小，确保不会超出屏幕 */
  max-width: 100%; /* 确保不会超出容器 */
}

.preset-color-item:active {
  transform: scale(0.75); /* 调整点击时的缩放比例，使其小于静态缩放比例 */
}

.preset-color-name {
  position: absolute;
  bottom: -38rpx; /* 再次增加与圆形的距离，使名称更下移 */
  font-size: 20rpx;
  color: #333;
  text-align: center;
  white-space: nowrap;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}



.selected-color-preview {
  width: 120rpx;
  height: 60rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 色彩滑块 */
.color-slider-row {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx; /* 增加间距，与图片一致 */
  width: 100%;
  padding: 0 30rpx;
  box-sizing: border-box;
  position: relative;
  justify-content: space-between;
  height: 40rpx; /* 增加高度，确保有足够空间显示滑块 */
}

.color-slider-label {
  display: flex;
  align-items: center;
  width: 140rpx;
  font-size: 30rpx;
  color: #333;
  text-align: left;
  font-weight: 400;
  white-space: nowrap;
  flex-shrink: 0;
}

.color-slider-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  height: 60rpx; /* 增加高度，扩大触摸区域 */
  margin-left: 20rpx;
  margin-right: 20rpx;
  overflow: visible; /* 确保滑块控制点不被裁剪 */
  padding: 18rpx 0; /* 增加上下内边距，扩大触摸区域 */
  cursor: pointer; /* 鼠标悬停时显示手型 */
}

.slider-value {
  width: 80rpx;
  font-size: 30rpx;
  color: #333;
  text-align: right;
  margin-left: 10rpx;
  font-weight: 400;
  flex-shrink: 0;
}

.color-slider-container slider {
  width: 100%;
  height: 28rpx; /* 调整高度，与滑块控制点一致 */
  margin: 0;
  position: relative;
  z-index: 5; /* 确保滑块在背景上方，可以交互 */
}

/* 自定义滑块容器 */
.custom-slider {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 自定义滑块轨道样式 */
.slider-track {
  position: absolute;
  width: 100%;
  height: 8rpx; /* 增加轨道高度 */
  border-radius: 4rpx;
  background-color: #F0F0F0;
  top: 50%;
  margin-top: -4rpx;
  z-index: 1;
  box-shadow: inset 0 1rpx 2rpx rgba(0, 0, 0, 0.1); /* 添加内阴影增强立体感 */
}

/* 自定义滑块控制点样式 */
.slider-handle {
  position: absolute;
  width: 32rpx; /* 增加控制点大小 */
  height: 32rpx;
  border-radius: 50%;
  background-color: #FFFFFF;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15); /* 增强阴影效果 */
  border: 2rpx solid #E0E0E0;
  top: 50%;
  margin-top: -16rpx;
  z-index: 5;
  transition: all 0.2s ease; /* 添加过渡动画 */
}

/* 滑块控制点激活状态 */
.slider-handle.active {
  width: 36rpx;
  height: 36rpx;
  margin-top: -18rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  border-color: #007AFF;
  background-color: #F8F9FA;
  transform: scale(1.1); /* 添加缩放效果 */
}

/* 移除滑块容器的背景色反馈 */
.color-slider-container.touching {
  /* 移除背景色 */
}

/* 移除触摸反馈的背景色 */
.color-slider-container:active {
  /* 移除背景色 */
}

/* 隐藏原生滑块，只使用其功能 */
.hidden-slider {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  opacity: 0;
  z-index: 10;
}

/* 色相滑块轨道 */
.hue-track {
  background: linear-gradient(to right,
    #FF0000, #FF7F00, #FFFF00, #00FF00,
    #00FFFF, #0000FF, #8B00FF, #FF0000);
}

/* 饱和度滑块轨道 - 在WXML中动态设置 */
.saturation-track {
  /* 背景色在WXML中通过style动态设置 */
}

/* 明度滑块轨道 */
.lightness-track {
  background: linear-gradient(to right, #000000, #FFFFFF);
}

/* 屏幕亮度滑块轨道 */
.brightness-track {
  background: linear-gradient(to right, #000000, #FFFFFF);
}



/* 共用颜色滑块部分 */
.color-sliders-section {
  margin-bottom: 0;
  padding: 30rpx 0 50rpx;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  z-index: 10;
  border-top: 1rpx solid #f5f5f5;
}

/* 颜色选择器弹窗 */
.color-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1002;
  display: flex;
  align-items: center;
  justify-content: center;
}

.color-picker-container {
  width: 90%;
  max-width: 600rpx;
  background-color: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
}

.color-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.color-picker-title {
  font-size: 30rpx;
  font-weight: 500;
}

.color-picker-close {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

/* 相机设置弹窗 */
.camera-settings-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1002;
  display: flex;
  align-items: center;
  justify-content: center;
}

.camera-settings-container {
  width: 90%;
  max-width: 600rpx;
  background-color: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
}

.camera-settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.camera-settings-title {
  font-size: 30rpx;
  font-weight: 500;
}

.camera-settings-close {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

.camera-settings-content {
  padding: 30rpx;
}

.camera-setting-item {
  margin-bottom: 30rpx;
}

.camera-setting-item:last-child {
  margin-bottom: 0;
}

.setting-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.setting-options {
  display: flex;
  justify-content: space-between;
}

.setting-option {
  flex: 1;
  text-align: center;
  padding: 15rpx 0;
  font-size: 26rpx;
  color: #666;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  margin: 0 10rpx;
  transition: all 0.2s;
}

.setting-option:first-child {
  margin-left: 0;
}

.setting-option:last-child {
  margin-right: 0;
}

.setting-option.selected {
  background-color: #07c160;
  color: white;
}

/* 相机设置按钮 */
.camera-settings-button {
  position: fixed;
  bottom: 80rpx;
  right: 30rpx;
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 20;
}

/* 相机入口按钮 */
.camera-entry-button {
  position: fixed;
  top: 20%; /* 再上移位置，从30%改为20% */
  right: 30rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  background-color: rgba(255, 255, 255, 0.5); /* 增加不透明度，使按钮更明显 */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000; /* 增加z-index，确保按钮在最上层 */
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2); /* 增强阴影效果 */
}

.camera-icon {
  width: 50rpx;
  height: 50rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 15.2a3.2 3.2 0 100-6.4 3.2 3.2 0 000 6.4z'/%3E%3Cpath d='M9 2L7.17 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2h-3.17L15 2H9zm3 15c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 相机切换图标 */
.camera-switch-icon {
  width: 40rpx;
  height: 40rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M20 4h-3.17L15 2H9L7.17 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm-5 11.5V13H9v2.5L5.5 12 9 8.5V11h6V8.5l3.5 3.5-3.5 3.5z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-bottom: 6rpx;
}

/* 闪光灯图标 */
.camera-flash-icon {
  width: 40rpx;
  height: 40rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-bottom: 6rpx;
}

.flash-off {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M3.27 3L2 4.27l5 5V13h3v9l3.58-6.14L17.73 20 19 18.73 3.27 3zM17 10h-4l4-8H7v2.18l8.46 8.46L17 10z'/%3E%3C/svg%3E");
}

.flash-on {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M7 2v11h3v9l7-12h-4l4-8z'/%3E%3C/svg%3E");
}

/* 相机设置图标 */
.camera-settings-icon {
  width: 40rpx;
  height: 40rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 相机容器样式 */
.camera-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600rpx;
  height: 600rpx;
  z-index: 10;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.2);
}

.camera-view {
  width: 100%;
  height: 100%;
}

/* 相机加载中遮罩 */
.camera-loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 15;
}

.camera-loading-text {
  color: #FFFFFF;
  font-size: 28rpx;
  padding: 20rpx 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 10rpx;
}

/* 相机功能按钮 */
.camera-function-buttons {
  position: fixed;
  bottom: 200rpx;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  z-index: 20;
}

.camera-function-button {
  padding: 10rpx 20rpx;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 30rpx;
  margin: 0 10rpx;
}

.camera-function-button text {
  color: #FFFFFF;
  font-size: 24rpx;
}

/* 拍照按钮 */
.shutter-button {
  position: fixed;
  bottom: 80rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 20;
}

.shutter-button-inner {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  background-color: #FFFFFF;
}

/* 底部提示文本样式 */
.bottom-tip-text {
  position: fixed;
  bottom: 30rpx;
  left: 0;
  width: 100%;
  text-align: center;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.5); /* 改为50%透明的黑色 */
  padding: 10rpx 0;
  z-index: 90;
  /* 移除文字阴影 */
}


