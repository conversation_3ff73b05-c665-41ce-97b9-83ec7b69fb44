// pages/contrastChecker/contrastChecker.js
Page({
  data: {
    foregroundColor: '#FFFFFF', // 默认前景色（白色）
    backgroundColor: '#333333', // 默认背景色（深灰色）
    contrastRatio: '0', // 对比度比率数值
    contrastLevel: 'poor', // 对比度等级：excellent, good, fair, poor
    wcagResult: {
      AA: {
        normalText: false,
        largeText: false
      },
      AAA: {
        normalText: false,
        largeText: false
      }
    },
    showForegroundPicker: false, // 是否显示前景色选择器
    showBackgroundPicker: false, // 是否显示背景色选择器
    fontSize: 16, // 默认字体大小
    fontSizes: [12, 14, 16, 18, 20, 24, 30, 36, 48], // 可选字体大小
    // 移除字体粗细选择，默认使用粗体
    // 移除sampleText变量，因为文本内容已经直接在WXML中定义
    copiedItem: '', // 当前复制的项目，用于显示复制提示
    activeColorType: 'foreground' // 当前激活的颜色类型：foreground 或 background
  },

  onLoad: function (options) {
    // 如果有传入的颜色参数，则使用传入的颜色
    if (options.foreground) {
      this.setData({
        foregroundColor: options.foreground
      });
    }
    if (options.background) {
      this.setData({
        backgroundColor: options.background
      });
    }

    // 计算对比度
    this.calculateContrastRatio();

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '对比度检测'
    });
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {
    // 清理定时器
    if (this.foregroundInputTimer) {
      clearTimeout(this.foregroundInputTimer);
      this.foregroundInputTimer = null;
    }
    if (this.backgroundInputTimer) {
      clearTimeout(this.backgroundInputTimer);
      this.backgroundInputTimer = null;
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {
    // 清理定时器
    if (this.foregroundInputTimer) {
      clearTimeout(this.foregroundInputTimer);
      this.foregroundInputTimer = null;
    }
    if (this.backgroundInputTimer) {
      clearTimeout(this.backgroundInputTimer);
      this.backgroundInputTimer = null;
    }
  },

  // 显示前景色选择器
  showForegroundPicker: function() {
    this.setData({
      showForegroundPicker: true,
      showBackgroundPicker: false,
      activeColorType: 'foreground'
    });
  },

  // 显示背景色选择器
  showBackgroundPicker: function() {
    this.setData({
      showBackgroundPicker: true,
      showForegroundPicker: false,
      activeColorType: 'background'
    });
  },

  // 隐藏颜色选择器
  hideColorPicker: function() {
    this.setData({
      showForegroundPicker: false,
      showBackgroundPicker: false
    });
  },

  // 前景色选择器变化
  onForegroundColorChange: function(e) {
    const color = e.detail.color;
    // 实时更新颜色预览，但不关闭选择器
    this.setData({
      foregroundColor: color,
      activeColorType: 'foreground'
    });
    this.calculateContrastRatio();
  },

  // 前景色选择器确认
  onForegroundColorConfirm: function(e) {
    const color = e.detail.color;
    this.setData({
      foregroundColor: color,
      showForegroundPicker: false,
      activeColorType: 'foreground'
    });
    this.calculateContrastRatio();
  },

  // 背景色选择器变化
  onBackgroundColorChange: function(e) {
    const color = e.detail.color;
    // 实时更新颜色预览，但不关闭选择器
    this.setData({
      backgroundColor: color,
      activeColorType: 'background'
    });
    this.calculateContrastRatio();
  },

  // 背景色选择器确认
  onBackgroundColorConfirm: function(e) {
    const color = e.detail.color;
    this.setData({
      backgroundColor: color,
      showBackgroundPicker: false,
      activeColorType: 'background'
    });
    this.calculateContrastRatio();
  },

  // 手动输入前景色
  onForegroundInput: function(e) {
    const color = e.detail.value;

    // 使用防抖避免频繁计算
    if (this.foregroundInputTimer) {
      clearTimeout(this.foregroundInputTimer);
    }

    this.setData({
      foregroundColor: color,
      activeColorType: 'foreground'
    });

    this.foregroundInputTimer = setTimeout(() => {
      this.calculateContrastRatio();
    }, 300);
  },

  // 手动输入背景色
  onBackgroundInput: function(e) {
    const color = e.detail.value;

    // 使用防抖避免频繁计算
    if (this.backgroundInputTimer) {
      clearTimeout(this.backgroundInputTimer);
    }

    this.setData({
      backgroundColor: color,
      activeColorType: 'background'
    });

    this.backgroundInputTimer = setTimeout(() => {
      this.calculateContrastRatio();
    }, 300);
  },

  // 交换前景色和背景色
  swapColors: function() {
    const { foregroundColor, backgroundColor } = this.data;
    this.setData({
      foregroundColor: backgroundColor,
      backgroundColor: foregroundColor
    });
    this.calculateContrastRatio();
  },

  // 计算对比度
  calculateContrastRatio: function() {
    const { foregroundColor, backgroundColor } = this.data;

    try {
      // 将颜色转换为RGB对象
      const foreground = this.hexToRgbObj(foregroundColor);
      const background = this.hexToRgbObj(backgroundColor);

      // 计算相对亮度
      const luminance1 = this.calculateLuminance(foreground);
      const luminance2 = this.calculateLuminance(background);

      // 计算对比度比率
      const ratio = luminance1 > luminance2
        ? (luminance1 + 0.05) / (luminance2 + 0.05)
        : (luminance2 + 0.05) / (luminance1 + 0.05);

      // 评估WCAG标准
      const wcagResult = {
        AA: {
          normalText: ratio >= 4.5,
          largeText: ratio >= 3
        },
        AAA: {
          normalText: ratio >= 7,
          largeText: ratio >= 4.5
        }
      };

      // 计算对比度等级
      let contrastLevel = '';
      if (ratio >= 7) {
        contrastLevel = 'excellent';
      } else if (ratio >= 4.5) {
        contrastLevel = 'good';
      } else if (ratio >= 3) {
        contrastLevel = 'fair';
      } else {
        contrastLevel = 'poor';
      }

      this.setData({
        contrastRatio: ratio.toFixed(2),
        contrastLevel,
        wcagResult
      });
    } catch (error) {
      console.error('计算对比度出错:', error);
    }
  },

  // 计算相对亮度
  calculateLuminance: function(rgb) {
    // 转换RGB为线性值
    const r = rgb.r / 255;
    const g = rgb.g / 255;
    const b = rgb.b / 255;

    // 应用gamma校正
    const R = r <= 0.03928 ? r / 12.92 : Math.pow((r + 0.055) / 1.055, 2.4);
    const G = g <= 0.03928 ? g / 12.92 : Math.pow((g + 0.055) / 1.055, 2.4);
    const B = b <= 0.03928 ? b / 12.92 : Math.pow((b + 0.055) / 1.055, 2.4);

    // 计算相对亮度
    return 0.2126 * R + 0.7152 * G + 0.0722 * B;
  },

  // HEX转RGB对象
  hexToRgbObj: function(hex) {
    // 移除#号并标准化
    hex = hex.replace(/^#/, '').toUpperCase();

    // 如果是3位HEX，转换为6位
    if (hex.length === 3) {
      hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
    }

    // 验证HEX格式
    if (!/^[0-9A-F]{6}$/.test(hex)) {
      throw new Error('Invalid HEX color');
    }

    // 解析RGB值
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    return { r, g, b };
  },

  // 设置字体大小
  setFontSize: function(e) {
    const fontSize = parseInt(e.currentTarget.dataset.size);
    this.setData({
      fontSize
    });
  },

  // 移除设置字体粗细的函数

  // 复制对比度结果
  copyResult: function(e) {
    const { foregroundColor, backgroundColor, contrastRatio } = this.data;
    const item = e.currentTarget.dataset.item;
    const result = `前景色: ${foregroundColor}\n背景色: ${backgroundColor}\n对比度: ${contrastRatio}:1`;

    // 设置当前复制的项目，用于显示复制提示
    this.setData({
      copiedItem: item
    });

    wx.setClipboardData({
      data: result,
      success: () => {
        wx.showToast({
          title: '结果已复制',
          icon: 'success',
          duration: 1500
        });

        // 1.5秒后隐藏复制提示
        setTimeout(() => {
          this.setData({
            copiedItem: ''
          });
        }, 1500);
      }
    });
  },

  // 随机生成颜色
  randomColor: function(e) {
    const target = e.currentTarget.dataset.target;
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * 16)];
    }

    if (target === 'foreground') {
      this.setData({
        foregroundColor: color,
        activeColorType: 'foreground'
      });
    } else {
      this.setData({
        backgroundColor: color,
        activeColorType: 'background'
      });
    }

    this.calculateContrastRatio();
  },

  // 设置当前激活的颜色类型
  setActiveColorType: function(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      activeColorType: type
    });
  },

  // 复制颜色值
  copyColorValue: function(e) {
    const color = e.currentTarget.dataset.color;
    const item = e.currentTarget.dataset.item;

    // 设置当前复制的项目，用于显示复制提示
    this.setData({
      copiedItem: item
    });

    wx.setClipboardData({
      data: color,
      success: () => {
        wx.showToast({
          title: '颜色已复制',
          icon: 'success',
          duration: 1500
        });

        // 1.5秒后隐藏复制提示
        setTimeout(() => {
          this.setData({
            copiedItem: ''
          });
        }, 1500);
      }
    });
  }
});
