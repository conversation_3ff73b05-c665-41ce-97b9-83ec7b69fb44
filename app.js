// app.js

App({
  globalData: {
    // 全局数据
    systemInfo: null,
    navBarHeight: 0,
    statusBarHeight: 0,
    // 添加系统信息初始化状态
    systemInfoReady: false
  },

  onLaunch() {
    // 异步获取系统信息，避免阻塞启动
    this.initSystemInfo();

    // 添加全局错误处理
    this.setupGlobalErrorHandler();
  },

  // 设置全局错误处理
  setupGlobalErrorHandler() {
    // 捕获未处理的错误
    wx.onError((error) => {
      // 过滤掉开发者工具和微信内部的错误
      if (error && typeof error === 'string') {
        // 忽略开发者工具相关错误
        if (error.includes('aiad error')) {
          return;
        }
        // 忽略微信小程序内部视图管理错误
        if (error.includes('removeTextView') ||
            error.includes('removeBaseView') ||
            error.includes('removeView') ||
            error.includes('fail no root view')) {
          console.warn('忽略微信内部视图管理错误:', error);
          return;
        }
        // 忽略 _getData 相关错误（可能是开发者工具或第三方库的问题）
        if (error.includes('_getData is not a function') ||
            error.includes('this._getData is not a function')) {
          // 只是静默忽略，不做任何处理
          return;
        }
        // 忽略 webviewScriptError 相关错误
        if (error.includes('webviewScriptError') ||
            error.includes('SystemError')) {
          console.warn('忽略 webview 脚本错误:', error);
          return;
        }
        // 忽略 Axure 相关错误（可能是 PP 目录下的原型文件引起的）
        if (error.includes('$ax') ||
            error.includes('$axure') ||
            error.includes('axQuery') ||
            error.includes('repeater') ||
            error.includes('getData') ||
            error.includes('_getDataFromDataSet') ||
            error.includes('PP/') ||
            error.includes('PP\\') ||
            error.includes('axure') ||
            error.includes('sitemap')) {
          // 只是静默忽略
          return;
        }
      }
      console.error('全局错误:', error);
    });

    // 捕获未处理的Promise rejection
    wx.onUnhandledRejection((res) => {
      // 过滤掉微信小程序内部的视图管理错误
      if (res && res.reason) {
        let errorMsg = '';

        // 处理不同类型的错误信息
        if (typeof res.reason === 'string') {
          errorMsg = res.reason;
        } else if (typeof res.reason === 'object') {
          errorMsg = res.reason.errMsg || res.reason.message || '';
        }

        // 忽略微信内部视图管理相关错误
        if (errorMsg.includes('removeTextView') ||
            errorMsg.includes('removeBaseView') ||
            errorMsg.includes('removeView') ||
            errorMsg.includes('fail no root view') ||
            errorMsg.includes('createTextView') ||
            errorMsg.includes('createBaseView')) {
          console.warn('忽略微信内部视图管理错误:', errorMsg);
          return;
        }
      }
      console.error('未处理的Promise rejection:', res);
    });
  },

  // 异步初始化系统信息
  initSystemInfo() {
    // 先设置默认值，确保应用能正常启动
    this.setDefaultSystemInfo();

    // 异步获取真实系统信息
    setTimeout(() => {
      this.getSystemInfo();
    }, 0);
  },

  // 设置默认系统信息
  setDefaultSystemInfo() {
    this.globalData.navBarHeight = 88; // 默认导航栏高度
    this.globalData.statusBarHeight = 20; // 默认状态栏高度
    this.globalData.systemInfoReady = false;
  },

  // 获取系统信息
  getSystemInfo() {
    try {
      // 使用新的API组合获取系统信息
      const windowInfo = wx.getWindowInfo();
      const deviceInfo = wx.getDeviceInfo();
      const appBaseInfo = wx.getAppBaseInfo();

      // 组合系统信息对象
      const systemInfo = {
        ...windowInfo,
        ...deviceInfo,
        ...appBaseInfo,
        // 保持向后兼容性
        windowWidth: windowInfo.windowWidth,
        windowHeight: windowInfo.windowHeight,
        screenWidth: windowInfo.screenWidth,
        screenHeight: windowInfo.screenHeight,
        statusBarHeight: windowInfo.statusBarHeight,
        platform: deviceInfo.platform,
        system: deviceInfo.system,
        version: appBaseInfo.version
      };

      this.processSystemInfo(systemInfo);
    } catch (e) {
      console.warn('获取系统信息失败，使用默认值:', e);
      // 保持默认值
      this.globalData.systemInfoReady = true;
    }
  },

  // 处理系统信息
  processSystemInfo(systemInfo) {
    try {
      // 获取菜单按钮位置信息
      let menuButtonInfo;
      try {
        menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      } catch (err) {
        // 使用默认值
        menuButtonInfo = {
          top: systemInfo.statusBarHeight + 8,
          height: 32,
          width: 87
        };
      }

      // 计算导航栏高度
      const statusBarHeight = systemInfo.statusBarHeight || 20;
      const navBarHeight = menuButtonInfo ?
        (menuButtonInfo.top - statusBarHeight) * 2 + menuButtonInfo.height + statusBarHeight :
        statusBarHeight + 44;

      // 更新全局数据
      this.globalData.systemInfo = systemInfo;
      this.globalData.navBarHeight = navBarHeight;
      this.globalData.statusBarHeight = statusBarHeight;
      this.globalData.systemInfoReady = true;

      // 通知页面系统信息已准备好
      this.notifySystemInfoReady();
    } catch (e) {
      // 保持默认值
      this.globalData.systemInfoReady = true;
    }
  },

  // 通知页面系统信息已准备好
  notifySystemInfoReady() {
    // 可以在这里触发自定义事件或回调
    // 让需要系统信息的页面知道数据已准备好
  },

  // 获取系统信息的便捷方法
  getGlobalSystemInfo() {
    return {
      systemInfo: this.globalData.systemInfo,
      navBarHeight: this.globalData.navBarHeight,
      statusBarHeight: this.globalData.statusBarHeight,
      ready: this.globalData.systemInfoReady
    };
  }
})
