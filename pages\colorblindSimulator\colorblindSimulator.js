// pages/colorblindSimulator/colorblindSimulator.js
const colorUtils = require('../../utils/colorUtils');
Page({
  data: {
    inputColor: '#c83c23', // 默认输入颜色（中国红）
    textColor: '#FFFFFF', // 默认文字颜色（白色）
    colorblindTypes: [
      { id: 'normal', name: '正常视觉' },
      { id: 'protanopia', name: '红色盲' },
      { id: 'deuteranopia', name: '绿色盲' },
      { id: 'tritanopia', name: '蓝色盲' },
      { id: 'protanomaly', name: '红色弱' },
      { id: 'deuteranomaly', name: '绿色弱' },
      { id: 'tritanomaly', name: '蓝色弱' },
      { id: 'achromatopsia', name: '全色盲' },
      { id: 'achromatomaly', name: '全色弱' }
    ],
    // 移除了activeType
    simulatedColors: {}, // 模拟后的颜色
    showColorPicker: false, // 是否显示颜色选择器
    colorInfo: {
      hex: '#c83c23',
      rgb: 'rgb(200, 60, 35)',
      hsl: 'hsl(9, 70%, 46%)'
    }
  },

  onLoad: function (options) {
    // 如果有传入的颜色参数，则使用传入的颜色
    if (options.color) {
      const color = options.color.startsWith('#') ? options.color : '#' + options.color;
      const textColor = colorUtils.getTextColorForBackground(color);
      this.setData({
        inputColor: color,
        textColor: textColor
      });
    } else {
      // 计算默认颜色的文字颜色
      const textColor = colorUtils.getTextColorForBackground(this.data.inputColor);
      this.setData({
        textColor: textColor
      });
    }

    // 模拟色盲效果
    this.simulateColorblindness();

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '色盲模拟器'
    });
  },

  // 用户点击右上角分享或使用分享按钮
  onShareAppMessage: function() {
    return {
      title: '色盲模拟器 - 体验不同色盲类型的视觉效果',
      path: '/pages/colorblindSimulator/colorblindSimulator?color=' + encodeURIComponent(this.data.inputColor),
      imageUrl: '/assets/images/share-colorblind.png' // 分享图片
    };
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {
    // 清理定时器
    if (this.inputChangeTimer) {
      clearTimeout(this.inputChangeTimer);
      this.inputChangeTimer = null;
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {
    // 清理定时器
    if (this.inputChangeTimer) {
      clearTimeout(this.inputChangeTimer);
      this.inputChangeTimer = null;
    }
    // 清理色盲模拟的防抖定时器
    if (this.updateTimer) {
      clearTimeout(this.updateTimer);
      this.updateTimer = null;
    }
  },

  // 移除了切换色盲类型的函数

  // 显示颜色选择器
  showColorPicker: function() {
    this.setData({
      showColorPicker: true
    });
  },

  // 隐藏颜色选择器
  hideColorPicker: function() {
    this.setData({
      showColorPicker: false
    });
  },

  // 颜色选择器变化
  onColorPickerChange: function(e) {
    const color = e.detail.color;
    const textColor = colorUtils.getTextColorForBackground(color);
    // 实时更新颜色预览，但不关闭选择器
    this.setData({
      inputColor: color,
      textColor: textColor
    });
    this.simulateColorblindness();
  },

  // 颜色选择器确认
  onColorPickerConfirm: function(e) {
    const color = e.detail.color;
    const textColor = colorUtils.getTextColorForBackground(color);
    this.setData({
      inputColor: color,
      textColor: textColor,
      showColorPicker: false
    });
    this.simulateColorblindness();

    // 显示轻提示
    wx.showToast({
      title: '颜色已更新',
      icon: 'success',
      duration: 1000
    });
  },

  // 手动输入颜色
  onInputChange: function(e) {
    const color = e.detail.value;
    const textColor = colorUtils.getTextColorForBackground(color);

    // 使用防抖避免频繁计算
    if (this.inputChangeTimer) {
      clearTimeout(this.inputChangeTimer);
    }

    this.setData({
      inputColor: color,
      textColor: textColor
    });

    this.inputChangeTimer = setTimeout(() => {
      this.simulateColorblindness();
    }, 300);
  },

  // 随机生成颜色
  randomColor: function() {
    // 生成更美观的随机颜色 - 使用HSL模型
    const h = Math.floor(Math.random() * 360); // 随机色相
    const s = Math.floor(Math.random() * 40) + 60; // 60-100的饱和度
    const l = Math.floor(Math.random() * 30) + 35; // 35-65的亮度

    // 转换为HEX
    const color = colorUtils.hslToHex(h, s, l);
    const textColor = colorUtils.getTextColorForBackground(color);

    this.setData({
      inputColor: color,
      textColor: textColor
    });
    this.simulateColorblindness();
  },

  // 模拟色盲效果 - 优化版本，使用防抖
  simulateColorblindness: function() {
    const { inputColor } = this.data;

    // 清除之前的防抖定时器
    if (this.updateTimer) {
      clearTimeout(this.updateTimer);
    }

    // 使用防抖，避免频繁计算
    this.updateTimer = setTimeout(() => {
      try {
        // 将颜色转换为RGB对象
        const rgb = this.hexToRgbObj(inputColor);

        // 批量计算所有色盲类型，减少重复计算
        const simulatedColors = this.batchSimulateColors(rgb, inputColor);

        // 更新颜色信息
        const hsl = colorUtils.hexToHSL(inputColor);
        const colorInfo = {
          hex: inputColor,
          rgb: `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`,
          hsl: `hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)`
        };

        // 批量更新数据，减少setData调用
        this.setData({
          simulatedColors,
          colorInfo
        });
      } catch (error) {
        console.error('模拟色盲效果出错:', error);
      }
    }, 100); // 100ms防抖
  },

  // 批量计算色盲模拟颜色，优化性能
  batchSimulateColors: function(rgb, inputColor) {
    return {
      normal: inputColor,
      protanopia: this.simulateProtanopia(rgb),
      deuteranopia: this.simulateDeuteranopia(rgb),
      tritanopia: this.simulateTritanopia(rgb),
      protanomaly: this.simulateProtanomaly(rgb),
      deuteranomaly: this.simulateDeuteranomaly(rgb),
      tritanomaly: this.simulateTritanomaly(rgb),
      achromatopsia: this.simulateAchromatopsia(rgb),
      achromatomaly: this.simulateAchromatomaly(rgb)
    };
  },

  // HEX转RGB对象
  hexToRgbObj: function(hex) {
    // 移除#号并标准化
    hex = hex.replace(/^#/, '').toUpperCase();

    // 如果是3位HEX，转换为6位
    if (hex.length === 3) {
      hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
    }

    // 验证HEX格式
    if (!/^[0-9A-F]{6}$/.test(hex)) {
      throw new Error('Invalid HEX color');
    }

    // 解析RGB值
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    return { r, g, b };
  },

  // RGB对象转HEX
  rgbObjToHex: function(rgb) {
    // 转换为HEX
    const toHex = x => {
      const hex = Math.round(x).toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    };

    return '#' + toHex(rgb.r) + toHex(rgb.g) + toHex(rgb.b);
  },

  // RGB转HSL - 使用colorUtils模块替代

  // 模拟红色盲 (Protanopia)
  simulateProtanopia: function(rgb) {
    // 使用Brettel等人的模型，基于Machado等人的改进
    // 红色盲转换矩阵 - 更准确的模型
    const r = rgb.r;
    const g = rgb.g;
    const b = rgb.b;

    // 红色盲转换矩阵 (Machado et al., 2009)
    const r2 = 0.152286 * r + 1.052583 * g + -0.204868 * b;
    const g2 = 0.114503 * r + 0.786281 * g + 0.099216 * b;
    const b2 = -0.003882 * r + -0.048116 * g + 1.051998 * b;

    // 确保RGB值在[0,255]范围内
    const newR = Math.min(255, Math.max(0, r2));
    const newG = Math.min(255, Math.max(0, g2));
    const newB = Math.min(255, Math.max(0, b2));

    return this.rgbObjToHex({ r: newR, g: newG, b: newB });
  },

  // 模拟绿色盲 (Deuteranopia)
  simulateDeuteranopia: function(rgb) {
    // 使用Brettel等人的模型，基于Machado等人的改进
    // 绿色盲转换矩阵 - 更准确的模型
    const r = rgb.r;
    const g = rgb.g;
    const b = rgb.b;

    // 绿色盲转换矩阵 (Machado et al., 2009)
    const r2 = 0.367322 * r + 0.860646 * g + -0.227968 * b;
    const g2 = 0.280085 * r + 0.672501 * g + 0.047413 * b;
    const b2 = -0.011820 * r + 0.042940 * g + 0.968881 * b;

    // 确保RGB值在[0,255]范围内
    const newR = Math.min(255, Math.max(0, r2));
    const newG = Math.min(255, Math.max(0, g2));
    const newB = Math.min(255, Math.max(0, b2));

    return this.rgbObjToHex({ r: newR, g: newG, b: newB });
  },

  // 模拟蓝色盲 (Tritanopia)
  simulateTritanopia: function(rgb) {
    // 使用Brettel等人的模型，基于Machado等人的改进
    // 蓝色盲转换矩阵 - 更准确的模型
    const r = rgb.r;
    const g = rgb.g;
    const b = rgb.b;

    // 蓝色盲转换矩阵 (Machado et al., 2009)
    const r2 = 1.255528 * r + -0.076749 * g + -0.178779 * b;
    const g2 = -0.078411 * r + 0.930809 * g + 0.147602 * b;
    const b2 = 0.004733 * r + 0.691367 * g + 0.303900 * b;

    // 确保RGB值在[0,255]范围内
    const newR = Math.min(255, Math.max(0, r2));
    const newG = Math.min(255, Math.max(0, g2));
    const newB = Math.min(255, Math.max(0, b2));

    return this.rgbObjToHex({ r: newR, g: newG, b: newB });
  },

  // 模拟红色弱 (Protanomaly)
  simulateProtanomaly: function(rgb) {
    // 使用更准确的红色弱模拟算法
    // 红色弱是红色盲的轻微形式，我们使用更精确的混合比例
    const r = rgb.r;
    const g = rgb.g;
    const b = rgb.b;

    // 红色弱转换矩阵 (基于Machado et al., 2009的改进)
    // 使用0.6的严重程度（severity）
    const r2 = 0.458064 * r + 0.679578 * g + -0.137642 * b;
    const g2 = 0.092785 * r + 0.846313 * g + 0.060902 * b;
    const b2 = -0.007494 * r + -0.016807 * g + 1.024301 * b;

    // 确保RGB值在[0,255]范围内
    const newR = Math.min(255, Math.max(0, r2));
    const newG = Math.min(255, Math.max(0, g2));
    const newB = Math.min(255, Math.max(0, b2));

    return this.rgbObjToHex({ r: newR, g: newG, b: newB });
  },

  // 模拟绿色弱 (Deuteranomaly)
  simulateDeuteranomaly: function(rgb) {
    // 使用更准确的绿色弱模拟算法
    // 绿色弱是绿色盲的轻微形式，我们使用更精确的转换矩阵
    const r = rgb.r;
    const g = rgb.g;
    const b = rgb.b;

    // 绿色弱转换矩阵 (基于Machado et al., 2009的改进)
    // 使用0.6的严重程度（severity）
    const r2 = 0.547494 * r + 0.607765 * g + -0.155259 * b;
    const g2 = 0.181692 * r + 0.781742 * g + 0.036566 * b;
    const b2 = -0.010410 * r + 0.027275 * g + 0.983136 * b;

    // 确保RGB值在[0,255]范围内
    const newR = Math.min(255, Math.max(0, r2));
    const newG = Math.min(255, Math.max(0, g2));
    const newB = Math.min(255, Math.max(0, b2));

    return this.rgbObjToHex({ r: newR, g: newG, b: newB });
  },

  // 模拟蓝色弱 (Tritanomaly)
  simulateTritanomaly: function(rgb) {
    // 使用更准确的蓝色弱模拟算法
    // 蓝色弱是蓝色盲的轻微形式，我们使用更精确的转换矩阵
    const r = rgb.r;
    const g = rgb.g;
    const b = rgb.b;

    // 蓝色弱转换矩阵 (基于Machado et al., 2009的改进)
    // 使用0.6的严重程度（severity）
    const r2 = 1.017277 * r + 0.027029 * g + -0.044306 * b;
    const g2 = -0.006113 * r + 0.958479 * g + 0.047634 * b;
    const b2 = 0.006379 * r + 0.248708 * g + 0.744913 * b;

    // 确保RGB值在[0,255]范围内
    const newR = Math.min(255, Math.max(0, r2));
    const newG = Math.min(255, Math.max(0, g2));
    const newB = Math.min(255, Math.max(0, b2));

    return this.rgbObjToHex({ r: newR, g: newG, b: newB });
  },

  // 模拟全色盲 (Achromatopsia)
  simulateAchromatopsia: function(rgb) {
    // 使用更准确的灰度转换公式，基于人眼对不同颜色的感知
    // 这个公式考虑了人眼对绿色的高敏感度
    const gray = 0.2126 * rgb.r + 0.7152 * rgb.g + 0.0722 * rgb.b;

    // 将RGB值设为相同的灰度值
    const r = gray;
    const g = gray;
    const b = gray;

    return this.rgbObjToHex({ r, g, b });
  },

  // 模拟全色弱 (Achromatomaly)
  simulateAchromatomaly: function(rgb) {
    // 使用更准确的全色弱模拟算法
    // 全色弱是全色盲的轻微形式，我们使用更精确的混合比例
    const normal = rgb;

    // 计算灰度值，使用与全色盲相同的公式
    const gray = 0.2126 * rgb.r + 0.7152 * rgb.g + 0.0722 * rgb.b;

    // 使用0.6的严重程度（severity）- 更接近真实的全色弱体验
    // 混合比例为0.4:0.6（正常:全色盲）
    const r = 0.4 * normal.r + 0.6 * gray;
    const g = 0.4 * normal.g + 0.6 * gray;
    const b = 0.4 * normal.b + 0.6 * gray;

    return this.rgbObjToHex({ r, g, b });
  },

  // 复制颜色值
  copyColor: function(e) {
    const type = e.currentTarget.dataset.type;
    const color = this.data.simulatedColors[type];
    const typeName = this.data.colorblindTypes.find(item => item.id === type).name;

    wx.setClipboardData({
      data: color,
      success: function() {
        wx.showToast({
          title: `已复制${typeName}颜色`,
          icon: 'success',
          duration: 1500
        });
      }
    });
  },

  // HSL转HEX - 使用colorUtils模块替代

  // 根据背景色计算文字颜色 - 使用colorUtils模块替代
});
