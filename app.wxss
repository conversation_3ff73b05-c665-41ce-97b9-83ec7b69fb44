/**app.wxss**/
@import "/styles/common.wxss";

/* 全局样式 */
page {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-page);
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
}

/* 安全区域适配 */
.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 隐藏滚动条但允许滚动 */
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}
