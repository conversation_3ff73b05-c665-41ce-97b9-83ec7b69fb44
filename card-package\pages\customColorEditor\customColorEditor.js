// pages/customColorEditor/customColorEditor.js v2.0 - 强制高度统一
const colorUtils = require('/utils/colorUtils');

Page({
  // 获取设备像素比
  getDevicePixelRatio() {
    try {
      const deviceInfo = wx.getDeviceInfo();
      return deviceInfo.pixelRatio || 2;
    } catch (error) {
      console.warn('获取设备像素比失败，使用默认值:', error);
      return 2; // 默认值
    }
  },

  data: {
    version: '2.0', // 版本标识，强制刷新
    templateId: 101,
    colors: ['#FFD9C5', '#FFB398', '#FF947A', '#FF708D'], // 默认4个颜色
    editingIndex: -1, // 当前编辑的颜色索引
    showColorPicker: false,
    selectedColor: '#FFD9C5',
    tempColor: '#FFD9C5', // 临时颜色，用于颜色选择器
    templateName: '蜜桃汽水',
    topBackgroundColor: '#FFF2EB', // 上半段背景颜色（101、103模板使用）
    bottomBackgroundColor: '#FFFFFF', // 下半段背景颜色（101、103模板使用）
    backgroundColor: '#FFFFFF', // 背景颜色（102模板使用）
    fontColor: '#EB898E', // 字体颜色（101模板的标题和色值颜色，103模板的标题颜色）
    titleColor: '#5865B1', // 标题颜色（102模板使用）

    showTitleEditor: false, // 是否显示标题编辑器
    showTopBgColorPicker: false, // 是否显示上半段背景颜色选择器
    showBottomBgColorPicker: false, // 是否显示下半段背景颜色选择器
    showBgColorPicker: false, // 是否显示背景颜色选择器（102模板）
    showFontColorPicker: false, // 是否显示字体颜色选择器（101、103模板）
    showTitleColorPicker: false, // 是否显示标题颜色选择器（102模板）

    tempTitle: '蜜桃汽水', // 临时标题
    tempTopBgColor: '#FFF2EB', // 临时上半段背景颜色
    tempBottomBgColor: '#FFFFFF', // 临时下半段背景颜色
    tempBgColor: '#FFFFFF', // 临时背景颜色（102模板）
    tempFontColor: '#EB898E', // 临时字体颜色（101、103模板）
    tempTitleColor: '#5865B1', // 临时标题颜色（102模板）

  },

  onLoad(options) {
    if (options.templateId) {
      const templateId = parseInt(options.templateId);
      let colors, templateName, defaultColor;

      // 根据模板ID设置默认值
      if (templateId === 102) {
        colors = ['#0B1F5E', '#1B3CBA', '#3A66E8', '#7AA3FF', '#BED8FF'];
        templateName = '海盐气泡';
        defaultColor = '#0B1F5E';
      } else if (templateId === 103) {
        colors = ['#FFC371', '#F9E0B7', '#C66767', '#8E7F3C'];
        templateName = '落日漫旅';
        defaultColor = '#FFC371';
      } else {
        colors = ['#FFD9C5', '#FFB398', '#FF947A', '#FF708D'];
        templateName = '蜜桃汽水';
        defaultColor = '#FFD9C5';
      }

      if (options.colors) {
        try {
          colors = JSON.parse(decodeURIComponent(options.colors));
        } catch (e) {
          console.error('解析颜色数据失败:', e);
        }
      }

      // 如果传入的颜色数量少于2个，补充到2个
      while (colors.length < 2) {
        colors.push(defaultColor);
      }

      const updateData = {
        templateId,
        colors,
        templateName,
        tempTitle: templateName
      };

      // 根据模板类型设置不同的背景颜色属性
      if (templateId === 102) {
        updateData.backgroundColor = '#FFFFFF';
        updateData.titleColor = '#5865B1';
        updateData.tempBgColor = '#FFFFFF';
        updateData.tempTitleColor = '#5865B1';
      } else if (templateId === 103) {
        // 落日漫旅模板 - 按照P03设计
        updateData.topBackgroundColor = '#F7E8E4';
        updateData.bottomBackgroundColor = '#FFFFFF';
        updateData.fontColor = '#C66767'; // 标题颜色
        updateData.tempTopBgColor = '#F7E8E4';
        updateData.tempBottomBgColor = '#FFFFFF';
        updateData.tempFontColor = '#C66767';
      } else if (templateId === 104) {
        // 春日樱语模板 - 按照P04设计
        updateData.topBackgroundColor = '#FFF1F1';
        updateData.bottomBackgroundColor = '#FFFFFF';
        updateData.fontColor = '#E89EC3'; // 标题颜色
        updateData.tempTopBgColor = '#FFF1F1';
        updateData.tempBottomBgColor = '#FFFFFF';
        updateData.tempFontColor = '#E89EC3';
      } else {
        updateData.topBackgroundColor = '#FFF2EB';
        updateData.bottomBackgroundColor = '#FFFFFF';
        updateData.fontColor = '#EB898E';
        updateData.tempTopBgColor = '#FFF2EB';
        updateData.tempBottomBgColor = '#FFFFFF';
        updateData.tempFontColor = '#EB898E';
      }

      this.setData(updateData, () => {
        // 如果是春日樱语模板，绘制Canvas形状
        if (templateId === 104) {
          this.drawP04Shapes();
        }
      });
    }
  },

  // 绘制春日樱语模板的Canvas形状
  drawP04Shapes() {
    const { colors } = this.data;

    // 绘制花瓣形状
    colors.forEach((color, index) => {
      this.drawPetalShape(`petal-${index}`, color, 35, 35); // 70rpx = 35px
    });

    // 绘制星形圆形
    colors.forEach((color, index) => {
      this.drawStarShape(`star-${index}`, color, 15, 15); // 30rpx = 15px
    });
  },

  // 绘制花瓣形状 - 使用Canvas 2D API
  drawPetalShape(canvasId, color, width, height) {
    const query = wx.createSelectorQuery().in(this);
    query.select(`#${canvasId}`)
      .fields({ node: true, size: true })
      .exec((res) => {
        if (!res[0] || !res[0].node) {
          console.error(`Canvas节点 ${canvasId} 未找到`);
          return;
        }

        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');

        // 设置Canvas实际尺寸
        const dpr = this.getDevicePixelRatio();
        canvas.width = width * dpr;
        canvas.height = height * dpr;
        ctx.scale(dpr, dpr);

        // 设置画布尺寸
        ctx.scale(width / 406, height / 406); // 缩放到目标尺寸

        // 设置填充颜色
        ctx.fillStyle = color;

    // 完全复制生成色卡的三层花瓣路径
    ctx.beginPath();
    // 外圈花瓣路径
    ctx.moveTo(0, 203);
    ctx.bezierCurveTo(0, 188.42931, 5.31923, 175.30931, 14.16028, 165.43744);
    ctx.bezierCurveTo(9.77002, 152.9337, 9.87649, 138.77682, 15.45245, 125.31526);
    ctx.bezierCurveTo(21.02842, 111.8537, 30.96355, 101.76799, 42.90941, 96.03089);
    ctx.bezierCurveTo(43.63832, 82.79886, 49.15429, 69.76036, 59.45732, 59.45732);
    ctx.bezierCurveTo(69.76036, 49.15429, 82.79886, 43.63832, 96.03089, 42.90941);
    ctx.bezierCurveTo(101.76799, 30.96355, 111.8537, 21.02842, 125.31526, 15.45245);
    ctx.bezierCurveTo(138.77682, 9.87649, 152.9337, 9.77002, 165.43744, 14.16028);
    ctx.bezierCurveTo(175.30931, 5.31923, 188.42931, 0, 203, 0);
    ctx.bezierCurveTo(217.57069, 0, 230.69069, 5.31923, 240.56256, 14.16028);
    ctx.bezierCurveTo(253.0663, 9.77002, 267.22318, 9.87649, 280.68472, 15.45245);
    ctx.bezierCurveTo(294.1463, 21.02842, 304.23203, 30.96355, 309.96912, 42.90941);
    ctx.bezierCurveTo(323.20114, 43.63832, 336.23965, 49.15429, 346.54266, 59.45732);
    ctx.bezierCurveTo(356.8457, 69.76036, 362.36169, 82.79886, 363.09058, 96.03089);
    ctx.bezierCurveTo(375.03644, 101.76799, 384.97159, 111.8537, 390.54755, 125.31526);
    ctx.bezierCurveTo(396.1235, 138.77682, 396.22998, 152.9337, 391.83972, 165.43744);
    ctx.bezierCurveTo(400.68076, 175.30931, 406, 188.42931, 406, 203);
    ctx.bezierCurveTo(406, 217.57069, 400.68076, 230.69069, 391.83972, 240.56256);
    ctx.bezierCurveTo(396.22998, 253.0663, 396.1235, 267.22318, 390.54755, 280.68472);
    ctx.bezierCurveTo(384.97159, 294.1463, 375.03644, 304.23203, 363.09058, 309.96912);
    ctx.bezierCurveTo(362.36169, 323.20114, 356.8457, 336.23965, 346.54266, 346.54266);
    ctx.bezierCurveTo(336.23965, 356.8457, 323.20114, 362.36169, 309.96912, 363.09058);
    ctx.bezierCurveTo(304.23203, 375.03644, 294.1463, 384.97159, 280.68472, 390.54755);
    ctx.bezierCurveTo(267.22318, 396.1235, 253.0663, 396.22998, 240.56256, 391.83972);
    ctx.bezierCurveTo(230.69069, 400.68076, 217.57069, 406, 203, 406);
    ctx.bezierCurveTo(188.42931, 406, 175.30931, 400.68076, 165.43744, 391.83972);
    ctx.bezierCurveTo(152.9337, 396.22998, 138.77682, 396.1235, 125.31526, 390.54755);
    ctx.bezierCurveTo(111.8537, 384.97159, 101.76799, 375.03644, 96.03089, 363.09058);
    ctx.bezierCurveTo(82.79886, 362.36169, 69.76036, 356.8457, 59.45732, 346.54266);
    ctx.bezierCurveTo(49.15429, 336.23965, 43.63832, 323.20114, 42.90941, 309.96912);
    ctx.bezierCurveTo(30.96355, 304.23203, 21.02842, 294.1463, 15.45245, 280.68472);
    ctx.bezierCurveTo(9.87649, 267.22318, 9.77002, 253.0663, 14.16028, 240.56256);
    ctx.bezierCurveTo(5.31923, 230.69069, 0, 217.57069, 0, 203);
    ctx.closePath();

    // 中圈花瓣路径
    ctx.moveTo(67.9426, 67.9426);
    ctx.bezierCurveTo(58.28485, 77.60036, 52.98909, 89.75069, 52.05534, 102.142);
    ctx.bezierCurveTo(40.96458, 107.74664, 31.76575, 117.289, 26.53901, 129.90747);
    ctx.bezierCurveTo(21.31227, 142.52592, 21.06936, 155.77797, 24.94863, 167.58337);
    ctx.bezierCurveTo(16.84691, 177.00563, 12, 189.34187, 12, 203);
    ctx.bezierCurveTo(12, 216.65813, 16.84691, 228.99437, 24.94863, 238.41663);
    ctx.bezierCurveTo(21.06936, 250.22203, 21.31227, 263.47406, 26.53901, 276.09253);
    ctx.bezierCurveTo(31.76575, 288.711, 40.96458, 298.25336, 52.05534, 303.858);
    ctx.bezierCurveTo(52.98909, 316.24933, 58.28485, 328.39963, 67.9426, 338.0574);
    ctx.bezierCurveTo(77.60036, 347.71515, 89.75069, 353.01089, 102.142, 353.94467);
    ctx.bezierCurveTo(107.74664, 365.0354, 117.289, 374.23425, 129.90747, 379.461);
    ctx.bezierCurveTo(142.52592, 384.68774, 155.77797, 384.93063, 167.58337, 381.05136);
    ctx.bezierCurveTo(177.00563, 389.15308, 189.34187, 394, 203, 394);
    ctx.bezierCurveTo(216.65813, 394, 228.99437, 389.15308, 238.41663, 381.05136);
    ctx.bezierCurveTo(250.22203, 384.93063, 263.47406, 384.68774, 276.09253, 379.461);
    ctx.bezierCurveTo(288.711, 374.23425, 298.25336, 365.0354, 303.858, 353.94467);
    ctx.bezierCurveTo(316.24933, 353.01089, 328.39963, 347.71515, 338.0574, 338.0574);
    ctx.bezierCurveTo(347.71515, 328.39963, 353.01089, 316.24933, 353.94467, 303.858);
    ctx.bezierCurveTo(365.0354, 298.25336, 374.23425, 288.711, 379.461, 276.09253);
    ctx.bezierCurveTo(384.68774, 263.47406, 384.93063, 250.22203, 381.05136, 238.41663);
    ctx.bezierCurveTo(389.15308, 228.99437, 394, 216.65813, 394, 203);
    ctx.bezierCurveTo(394, 189.34187, 389.15308, 177.00563, 381.05136, 167.58337);
    ctx.bezierCurveTo(384.93063, 155.77797, 384.68774, 142.52592, 379.461, 129.90747);
    ctx.bezierCurveTo(374.23425, 117.289, 365.0354, 107.74664, 353.94467, 102.142);
    ctx.bezierCurveTo(353.01089, 89.75069, 347.71515, 77.60036, 338.0574, 67.9426);
    ctx.bezierCurveTo(328.39963, 58.28485, 316.24933, 52.98909, 303.858, 52.05534);
    ctx.bezierCurveTo(298.25336, 40.96458, 288.711, 31.76575, 276.09253, 26.53901);
    ctx.bezierCurveTo(263.47406, 21.31227, 250.22203, 21.06936, 238.41663, 24.94863);
    ctx.bezierCurveTo(228.99437, 16.84691, 216.65813, 12, 203, 12);
    ctx.bezierCurveTo(189.34187, 12, 177.00563, 16.84691, 167.58337, 24.94863);
    ctx.bezierCurveTo(155.77797, 21.06936, 142.52592, 21.31227, 129.90747, 26.53901);
    ctx.bezierCurveTo(117.289, 31.76575, 107.74664, 40.96458, 102.142, 52.05534);
    ctx.bezierCurveTo(89.75069, 52.98909, 77.60036, 58.28485, 67.9426, 67.9426);
    ctx.closePath();

    // 内圈花瓣路径
    ctx.moveTo(23, 203);
    ctx.bezierCurveTo(23, 190.00795, 27.9339, 178.36151, 36.06736, 169.79503);
    ctx.bezierCurveTo(31.83128, 158.7681, 31.72984, 146.12007, 36.70168, 134.11699);
    ctx.bezierCurveTo(41.67353, 122.11389, 50.68876, 113.24211, 61.48134, 108.44025);
    ctx.bezierCurveTo(61.78754, 96.63161, 66.53401, 84.90755, 75.72078, 75.72078);
    ctx.bezierCurveTo(84.90755, 66.53401, 96.63161, 61.78754, 108.44025, 61.48134);
    ctx.bezierCurveTo(113.24211, 50.68876, 122.11389, 41.67353, 134.11699, 36.70168);
    ctx.bezierCurveTo(146.12007, 31.72984, 158.7681, 31.83128, 169.79503, 36.06736);
    ctx.bezierCurveTo(178.36151, 27.9339, 190.00795, 23, 203, 23);
    ctx.bezierCurveTo(215.99205, 23, 227.63849, 27.9339, 236.20497, 36.06736);
    ctx.bezierCurveTo(247.2319, 31.83128, 259.87994, 31.72984, 271.88303, 36.70168);
    ctx.bezierCurveTo(283.88611, 41.67353, 292.75787, 50.68876, 297.55975, 61.48134);
    ctx.bezierCurveTo(309.36838, 61.78754, 321.09247, 66.53401, 330.27924, 75.72078);
    ctx.bezierCurveTo(339.46597, 84.90755, 344.21246, 96.63161, 344.51865, 108.44025);
    ctx.bezierCurveTo(355.31125, 113.24211, 364.32648, 122.11389, 369.29831, 134.11699);
    ctx.bezierCurveTo(374.27017, 146.12007, 374.16873, 158.7681, 369.93265, 169.79503);
    ctx.bezierCurveTo(378.0661, 178.36151, 383, 190.00795, 383, 203);
    ctx.bezierCurveTo(383, 215.99205, 378.0661, 227.63849, 369.93265, 236.20497);
    ctx.bezierCurveTo(374.16873, 247.2319, 374.27017, 259.87994, 369.29831, 271.88303);
    ctx.bezierCurveTo(364.32648, 283.88611, 355.31125, 292.75787, 344.51865, 297.55975);
    ctx.bezierCurveTo(344.21246, 309.36838, 339.46597, 321.09247, 330.27924, 330.27924);
    ctx.bezierCurveTo(321.09247, 339.46597, 309.36838, 344.21246, 297.55975, 344.51865);
    ctx.bezierCurveTo(292.75787, 355.31125, 283.88611, 364.32648, 271.88303, 369.29831);
    ctx.bezierCurveTo(259.87994, 374.27017, 247.2319, 374.16873, 236.20497, 369.93265);
    ctx.bezierCurveTo(227.63849, 378.0661, 215.99205, 383, 203, 383);
    ctx.bezierCurveTo(190.00795, 383, 178.36151, 378.0661, 169.79503, 369.93265);
    ctx.bezierCurveTo(158.7681, 374.16873, 146.12007, 374.27017, 134.11699, 369.29831);
    ctx.bezierCurveTo(122.11389, 364.32648, 113.24211, 355.31125, 108.44025, 344.51865);
    ctx.bezierCurveTo(96.63161, 344.21246, 84.90755, 339.46597, 75.72078, 330.27924);
    ctx.bezierCurveTo(66.53401, 321.09247, 61.78754, 309.36838, 61.48134, 297.55975);
    ctx.bezierCurveTo(50.68876, 292.75787, 41.67353, 283.88611, 36.70168, 271.88303);
    ctx.bezierCurveTo(31.72984, 259.87994, 31.83128, 247.2319, 36.06736, 236.20497);
    ctx.bezierCurveTo(27.9339, 227.63849, 23, 215.99205, 23, 203);
    ctx.closePath();

        ctx.fill();
      });
  },

  // 绘制星形圆形 - 使用Canvas 2D API
  drawStarShape(canvasId, color, width, height) {
    const query = wx.createSelectorQuery().in(this);
    query.select(`#${canvasId}`)
      .fields({ node: true, size: true })
      .exec((res) => {
        if (!res[0] || !res[0].node) {
          console.error(`Canvas节点 ${canvasId} 未找到`);
          return;
        }

        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');

        // 设置Canvas实际尺寸
        const dpr = this.getDevicePixelRatio();
        canvas.width = width * dpr;
        canvas.height = height * dpr;
        ctx.scale(dpr, dpr);

        // 设置画布尺寸
        ctx.scale(width / 202, height / 202); // 缩放到目标尺寸

        // 设置填充颜色
        ctx.fillStyle = color;

    // 完全复制生成色卡的星形路径
    ctx.beginPath();
    // 上方花瓣
    ctx.moveTo(101, 80);
    ctx.bezierCurveTo(92.05381, 59.91061, 79, 49.17206, 79, 29.17206);
    ctx.bezierCurveTo(79, 18.77206, 83.4, 0, 101, 0);
    ctx.bezierCurveTo(118.6, 0, 123, 18.77206, 123, 29.17206);
    ctx.bezierCurveTo(123, 49.17206, 109.76714, 59.83183, 101, 80);
    ctx.closePath();

    // 下方花瓣
    ctx.moveTo(101, 122);
    ctx.bezierCurveTo(109.94619, 142.0894, 123, 152.82794, 123, 172.82794);
    ctx.bezierCurveTo(123, 183.22794, 118.6, 202, 101, 202);
    ctx.bezierCurveTo(83.4, 202, 79, 183.22794, 79, 172.82794);
    ctx.bezierCurveTo(79, 152.82794, 92.23286, 142.16818, 101, 122);
    ctx.closePath();

    // 右方花瓣
    ctx.moveTo(172.82794, 79);
    ctx.bezierCurveTo(183.22794, 79, 202, 83.4, 202, 101);
    ctx.bezierCurveTo(202, 118.6, 183.22794, 123, 172.82794, 123);
    ctx.bezierCurveTo(152.82794, 123, 142.16818, 109.76714, 122, 101);
    ctx.bezierCurveTo(142.0894, 92.05381, 152.82794, 79, 172.82794, 79);
    ctx.closePath();

    // 左方花瓣
    ctx.moveTo(29.17206, 123);
    ctx.bezierCurveTo(18.77206, 123, 0, 118.6, 0, 101);
    ctx.bezierCurveTo(0, 83.4, 18.77206, 79, 29.17206, 79);
    ctx.bezierCurveTo(49.17206, 79, 59.83183, 92.23286, 80, 101);
    ctx.bezierCurveTo(59.91061, 109.94619, 49.17206, 123, 29.17206, 123);
    ctx.closePath();

    // 对角线花瓣 - 右上
    ctx.moveTo(115.84924, 86.15076);
    ctx.bezierCurveTo(123.72868, 65.6195, 122.09154, 48.79576, 136.23367, 34.65363);
    ctx.bezierCurveTo(143.58759, 27.29972, 159.9727, 17.13714, 172.41779, 29.58221);
    ctx.bezierCurveTo(184.86287, 42.02729, 174.70029, 58.41241, 167.34637, 65.76633);
    ctx.bezierCurveTo(153.20424, 79.90846, 136.3096, 78.08901, 115.84924, 86.15076);
    ctx.closePath();

    // 对角线花瓣 - 左下
    ctx.moveTo(86.15076, 115.84924);
    ctx.bezierCurveTo(78.27132, 136.38051, 79.90846, 153.20424, 65.76633, 167.34637);
    ctx.bezierCurveTo(58.41241, 174.70029, 42.02729, 184.86287, 29.58221, 172.41779);
    ctx.bezierCurveTo(17.13714, 159.9727, 27.29972, 143.58759, 34.65363, 136.23367);
    ctx.bezierCurveTo(48.79576, 122.09154, 65.6904, 123.91099, 86.15076, 115.84924);
    ctx.closePath();

    // 对角线花瓣 - 右下
    ctx.moveTo(172.41779, 172.41779);
    ctx.bezierCurveTo(159.9727, 184.86287, 143.58759, 174.70029, 136.23367, 167.34637);
    ctx.bezierCurveTo(122.09154, 153.20424, 123.91099, 136.3096, 115.84924, 115.84924);
    ctx.bezierCurveTo(136.38051, 123.72868, 153.20424, 122.09154, 167.34637, 136.23367);
    ctx.bezierCurveTo(174.70029, 143.58759, 184.86287, 159.9727, 172.41779, 172.41779);
    ctx.closePath();

    // 对角线花瓣 - 左上
    ctx.moveTo(29.58221, 29.58221);
    ctx.bezierCurveTo(42.02729, 17.13714, 58.41241, 27.29972, 65.76633, 34.65363);
    ctx.bezierCurveTo(79.90846, 48.79576, 78.08901, 65.6904, 86.15076, 86.15076);
    ctx.bezierCurveTo(65.6195, 78.27132, 48.79576, 79.90846, 34.65363, 65.76633);
    ctx.bezierCurveTo(27.29972, 58.41241, 17.13714, 42.02729, 29.58221, 29.58221);
    ctx.closePath();

    // 中心星形
    ctx.moveTo(85.29404, 107.50562);
    ctx.lineTo(91.21365, 101.00019);
    ctx.lineTo(85.29404, 94.49438);
    ctx.lineTo(94.07988, 94.07988);
    ctx.lineTo(94.49438, 85.29404);
    ctx.lineTo(100.99998, 91.21349);
    ctx.lineTo(107.50562, 85.29404);
    ctx.lineTo(107.92012, 94.07988);
    ctx.lineTo(116.70596, 94.49438);
    ctx.lineTo(110.78654, 101.00002);
    ctx.lineTo(116.70596, 107.50562);
    ctx.lineTo(107.92012, 107.92012);
    ctx.lineTo(107.50562, 116.70596);
    ctx.lineTo(101.00002, 110.78651);
    ctx.lineTo(94.49438, 116.70596);
    ctx.lineTo(94.07988, 107.92012);
    ctx.lineTo(85.29404, 107.50562);
    ctx.closePath();

        ctx.fill();
      });
  },

  // 编辑颜色
  editColor(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    const color = this.data.colors[index];

    this.setData({
      editingIndex: index,
      selectedColor: color,
      tempColor: color,
      showColorPicker: true
    });
  },

  // 添加颜色
  addColor() {
    const colors = [...this.data.colors];
    if (colors.length < 5) {
      let defaultColor;
      if (this.data.templateId === 102) {
        defaultColor = '#0B1F5E';
      } else if (this.data.templateId === 103) {
        defaultColor = '#FFC371';
      } else if (this.data.templateId === 104) {
        defaultColor = '#F9DFE7';
      } else {
        defaultColor = '#FFD9C5';
      }
      colors.push(defaultColor);
      this.setData({ colors }, () => {
        // 如果是春日樱语模板，重新绘制Canvas
        if (this.data.templateId === 104) {
          this.drawP04Shapes();
        }
      });

      wx.showToast({
        title: '颜色已添加',
        icon: 'success',
        duration: 1000
      });
    } else {
      wx.showToast({
        title: '最多只能添加5个颜色',
        icon: 'none'
      });
    }
  },

  // 移除指定位置的颜色
  removeColor(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    const colors = [...this.data.colors];

    if (colors.length > 2) {
      wx.showModal({
        title: '确认删除',
        content: `确定要删除颜色 ${index + 1} 吗？`,
        success: (res) => {
          if (res.confirm) {
            colors.splice(index, 1);
            this.setData({ colors }, () => {
              // 如果是春日樱语模板，重新绘制Canvas
              if (this.data.templateId === 104) {
                this.drawP04Shapes();
              }
            });

            wx.showToast({
              title: '颜色已删除',
              icon: 'success',
              duration: 1000
            });
          }
        }
      });
    } else {
      wx.showToast({
        title: '至少需要保留2个颜色',
        icon: 'none'
      });
    }
  },

  // 复制颜色代码
  copyColorCode(e) {
    const color = e.currentTarget.dataset.color;
    wx.setClipboardData({
      data: color,
      success: () => {
        wx.showToast({
          title: '颜色代码已复制',
          icon: 'success',
          duration: 1000
        });
      }
    });
  },

  // 颜色变化事件（来自公共颜色选择器）
  onColorChange(e) {
    const color = e.detail.color;
    this.setData({
      tempColor: color
    });
  },

  // 确认颜色选择（来自公共颜色选择器）
  onColorConfirm(e) {
    const color = e.detail.color;
    const { editingIndex } = this.data;

    if (editingIndex === -1) return;

    // 更新颜色
    const colors = [...this.data.colors];
    colors[editingIndex] = color.toUpperCase();

    this.setData({
      colors,
      selectedColor: color.toUpperCase(),
      showColorPicker: false,
      editingIndex: -1
    }, () => {
      // 如果是春日樱语模板，重新绘制Canvas
      if (this.data.templateId === 104) {
        this.drawP04Shapes();
      }
    });

    wx.showToast({
      title: '颜色已更新',
      icon: 'success',
      duration: 1000
    });
  },

  // 取消颜色选择
  cancelColorPicker() {
    this.setData({
      showColorPicker: false,
      editingIndex: -1
    });
  },

  // 生成色卡
  generateColorCard() {
    const { colors, templateId, templateName, topBackgroundColor, bottomBackgroundColor, backgroundColor, fontColor, titleColor } = this.data;

    // 验证所有颜色都是有效的
    for (let i = 0; i < colors.length; i++) {
      if (!/^#[0-9A-Fa-f]{6}$/.test(colors[i])) {
        wx.showToast({
          title: `第${i + 1}个颜色格式无效`,
          icon: 'none'
        });
        return;
      }
    }

    let params;

    if (templateId === 102) {
      // P01样式模板
      if (!/^#[0-9A-Fa-f]{6}$/.test(backgroundColor)) {
        wx.showToast({
          title: '背景颜色格式无效',
          icon: 'none'
        });
        return;
      }

      params = {
        colors: JSON.stringify(colors),
        templateId: 8, // 使用新的模板ID
        isCustom: true,
        title: templateName,
        backgroundColor: backgroundColor,
        titleColor: titleColor
      };
    } else if (templateId === 103) {
      // P03样式模板（落日漫旅）
      if (!/^#[0-9A-Fa-f]{6}$/.test(topBackgroundColor)) {
        wx.showToast({
          title: '上半段背景颜色格式无效',
          icon: 'none'
        });
        return;
      }

      if (!/^#[0-9A-Fa-f]{6}$/.test(bottomBackgroundColor)) {
        wx.showToast({
          title: '下半段背景颜色格式无效',
          icon: 'none'
        });
        return;
      }

      params = {
        colors: JSON.stringify(colors),
        templateId: 16, // 新的P03模板ID（使用16，避免与现有模板冲突）
        isCustom: true,
        title: templateName,
        topBackgroundColor: topBackgroundColor,
        bottomBackgroundColor: bottomBackgroundColor,
        fontColor: fontColor // 标题颜色
      };
    } else if (templateId === 104) {
      // P04样式模板（春日樱语）
      if (!/^#[0-9A-Fa-f]{6}$/.test(topBackgroundColor)) {
        wx.showToast({
          title: '上半段背景颜色格式无效',
          icon: 'none'
        });
        return;
      }

      if (!/^#[0-9A-Fa-f]{6}$/.test(bottomBackgroundColor)) {
        wx.showToast({
          title: '下半段背景颜色格式无效',
          icon: 'none'
        });
        return;
      }

      params = {
        colors: JSON.stringify(colors),
        templateId: 17, // 新的P04模板ID（使用17，避免与现有模板冲突）
        isCustom: true,
        title: templateName,
        topBackgroundColor: topBackgroundColor,
        bottomBackgroundColor: bottomBackgroundColor,
        fontColor: fontColor // 标题颜色
      };
    } else {
      // P02样式模板（蜜桃汽水）
      if (!/^#[0-9A-Fa-f]{6}$/.test(topBackgroundColor)) {
        wx.showToast({
          title: '上半段背景颜色格式无效',
          icon: 'none'
        });
        return;
      }

      if (!/^#[0-9A-Fa-f]{6}$/.test(bottomBackgroundColor)) {
        wx.showToast({
          title: '下半段背景颜色格式无效',
          icon: 'none'
        });
        return;
      }

      params = {
        colors: JSON.stringify(colors),
        templateId: 7,
        isCustom: true,
        title: templateName,
        topBackgroundColor: topBackgroundColor,
        bottomBackgroundColor: bottomBackgroundColor,
        fontColor: fontColor
      };
    }

    const queryString = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');

    wx.navigateTo({
      url: `/pages/preview/preview?${queryString}`
    });
  },



  // 编辑标题
  editTitle() {
    this.setData({
      showTitleEditor: true,
      tempTitle: this.data.templateName
    });
  },

  // 标题输入变化
  onTitleInput(e) {
    this.setData({
      tempTitle: e.detail.value
    });
  },

  // 确认标题修改
  confirmTitle() {
    const { tempTitle } = this.data;
    if (tempTitle.trim().length === 0) {
      wx.showToast({
        title: '标题不能为空',
        icon: 'none'
      });
      return;
    }

    this.setData({
      templateName: tempTitle.trim(),
      showTitleEditor: false
    });

    wx.showToast({
      title: '标题已更新',
      icon: 'success'
    });
  },

  // 取消标题编辑
  cancelTitleEdit() {
    this.setData({
      showTitleEditor: false,
      tempTitle: this.data.templateName
    });
  },

  // 编辑上半段背景颜色
  editTopBackgroundColor() {
    this.setData({
      showTopBgColorPicker: true,
      tempTopBgColor: this.data.topBackgroundColor
    });
  },

  // 编辑下半段背景颜色
  editBottomBackgroundColor() {
    this.setData({
      showBottomBgColorPicker: true,
      tempBottomBgColor: this.data.bottomBackgroundColor
    });
  },

  // 上半段背景颜色变化事件（来自公共颜色选择器）
  onTopBgColorChange(e) {
    const color = e.detail.color;
    this.setData({
      tempTopBgColor: color
    });
  },

  // 下半段背景颜色变化事件（来自公共颜色选择器）
  onBottomBgColorChange(e) {
    const color = e.detail.color;
    this.setData({
      tempBottomBgColor: color
    });
  },

  // 确认上半段背景颜色修改（来自公共颜色选择器）
  onTopBgColorConfirm(e) {
    const color = e.detail.color;

    this.setData({
      topBackgroundColor: color.toUpperCase(),
      showTopBgColorPicker: false
    });

    wx.showToast({
      title: '上半段背景颜色已更新',
      icon: 'success'
    });
  },

  // 确认下半段背景颜色修改（来自公共颜色选择器）
  onBottomBgColorConfirm(e) {
    const color = e.detail.color;

    this.setData({
      bottomBackgroundColor: color.toUpperCase(),
      showBottomBgColorPicker: false
    });

    wx.showToast({
      title: '下半段背景颜色已更新',
      icon: 'success'
    });
  },

  // 取消上半段背景颜色编辑
  cancelTopBgColorEdit() {
    this.setData({
      showTopBgColorPicker: false,
      tempTopBgColor: this.data.topBackgroundColor
    });
  },

  // 取消下半段背景颜色编辑
  cancelBottomBgColorEdit() {
    this.setData({
      showBottomBgColorPicker: false,
      tempBottomBgColor: this.data.bottomBackgroundColor
    });
  },

  // 编辑背景颜色（102模板）
  editBackgroundColor() {
    this.setData({
      showBgColorPicker: true,
      tempBgColor: this.data.backgroundColor
    });
  },

  // 背景颜色变化事件（102模板）
  onBgColorChange(e) {
    const color = e.detail.color;
    this.setData({
      tempBgColor: color
    });
  },

  // 确认背景颜色修改（102模板）
  onBgColorConfirm(e) {
    const color = e.detail.color;

    this.setData({
      backgroundColor: color.toUpperCase(),
      showBgColorPicker: false
    });

    wx.showToast({
      title: '背景颜色已更新',
      icon: 'success'
    });
  },

  // 取消背景颜色编辑（102模板）
  cancelBgColorEdit() {
    this.setData({
      showBgColorPicker: false,
      tempBgColor: this.data.backgroundColor
    });
  },

  // 编辑字体颜色（101模板）
  editFontColor() {
    this.setData({
      showFontColorPicker: true,
      tempFontColor: this.data.fontColor
    });
  },

  // 字体颜色变化事件（101模板）
  onFontColorChange(e) {
    const color = e.detail.color;
    this.setData({
      tempFontColor: color
    });
  },

  // 确认字体颜色修改（101模板）
  onFontColorConfirm(e) {
    const color = e.detail.color;

    this.setData({
      fontColor: color.toUpperCase(),
      showFontColorPicker: false
    });

    wx.showToast({
      title: '字体颜色已更新',
      icon: 'success'
    });
  },

  // 取消字体颜色编辑（101模板）
  cancelFontColorEdit() {
    this.setData({
      showFontColorPicker: false,
      tempFontColor: this.data.fontColor
    });
  },

  // 编辑标题颜色（102模板）
  editTitleColor() {
    this.setData({
      showTitleColorPicker: true,
      tempTitleColor: this.data.titleColor
    });
  },

  // 标题颜色变化事件（102模板）
  onTitleColorChange(e) {
    const color = e.detail.color;
    this.setData({
      tempTitleColor: color
    });
  },

  // 确认标题颜色修改（102模板）
  onTitleColorConfirm(e) {
    const color = e.detail.color;

    this.setData({
      titleColor: color.toUpperCase(),
      showTitleColorPicker: false
    });

    wx.showToast({
      title: '标题颜色已更新',
      icon: 'success'
    });
  },

  // 取消标题颜色编辑（102模板）
  cancelTitleColorEdit() {
    this.setData({
      showTitleColorPicker: false,
      tempTitleColor: this.data.titleColor
    });
  },



  // 复制颜色代码
  copyColor(e) {
    const color = e.currentTarget.dataset.color;

    wx.setClipboardData({
      data: color,
      success: () => {
        wx.showToast({
          title: '颜色代码已复制',
          icon: 'success'
        });
      }
    });
  }
});
