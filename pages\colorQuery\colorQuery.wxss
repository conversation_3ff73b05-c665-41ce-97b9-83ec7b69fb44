/* pages/colorQuery/colorQuery.wxss */
.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

/* 分类选择区域样式 */
.category-container {
  background-color: #ffffff;
  padding: 2rpx 0; /* 减少上下内边距 */
  border-bottom: 1rpx solid #e0e0e0;
  position: relative; /* 改为相对定位，因为它已经在fixed-area内 */
  width: 100%;
  /* 使用JS动态计算的top值 */
  margin-top: 0;
  height: 60rpx; /* 减少高度 */
  line-height: 60rpx; /* 垂直居中 */
  box-shadow: none; /* 移除阴影 */
  margin-bottom: -5rpx; /* 添加负底部边距 */
}

.category-scroll {
  width: 100%;
  white-space: nowrap;
}

.category-list {
  display: inline-flex;
  padding: 0 16rpx;
}

.category-item {
  padding: 0 20rpx; /* 保持左右内边距 */
  margin-right: 10rpx; /* 保持右边距 */
  font-size: 28rpx; /* 保持字体大小 */
  color: #666;
  border-radius: 30rpx;
  background-color: #f0f0f0;
  transition: all 0.3s;
  height: 50rpx; /* 减少高度 */
  line-height: 50rpx; /* 垂直居中 */
  display: inline-block;
  margin-top: 5rpx; /* 减少顶部边距 */
  min-width: 40rpx; /* 确保最小宽度 */
  text-align: center; /* 文本居中 */
}

/* 添加滑动动画效果 */
.category-scroll {
  transition: transform 0.3s ease;
}

/* 活动分类项的动画效果 */
.category-item.active {
  transition: all 0.3s ease;
}

/* 选中状态的样式现在通过内联样式动态设置 */
.category-item.active {
  font-weight: 500;
}

/* 颜色列表区域样式 */
.fixed-area {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  margin: 0;
  padding: 0;
}

.color-list-container {
  flex: 1;
  overflow-y: auto;
  padding: 18rpx; /* 增加内边距，与网格间距一致 */
  padding-top: 0; /* 顶部内边距由padding-top样式属性控制 */
  /* 使用动态计算的padding-top，确保内容不被导航栏和分类标签遮挡 */
  margin-top: 0; /* 不再需要固定的margin-top */
  background-color: #f5f5f5;
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 18rpx; /* 增加网格间距到18rpx */
  column-gap: 18rpx; /* 增加列间距到18rpx */
  row-gap: 18rpx; /* 增加行间距到18rpx */
  padding-bottom: 18rpx; /* 底部内边距与间距一致 */
  padding-top: 5rpx; /* 进一步减小顶部内边距 */
  margin-top: -15rpx; /* 保持负边距，使色块靠近分类标签 */
}

.color-item {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 8rpx; /* 保持适当圆角 */
  overflow: hidden;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05); /* 保持适当阴影 */
  margin: 0; /* 确保没有边距，间距由网格控制 */
  padding: 0; /* 确保没有内边距 */
  width: 100%; /* 确保宽度填满网格单元 */
  height: 100%; /* 确保高度填满网格单元 */
}

.color-preview {
  width: 100%;
  height: 130rpx; /* 增加色块高度 */
  border-radius: 0;
  border: none;
  margin: 0; /* 确保没有边距 */
  padding: 0; /* 确保没有内边距 */
}

.color-info {
  padding: 8rpx 10rpx; /* 增加内边距，为文字提供更多空间 */
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  background-color: #ffffff;
  width: 100%;
  box-sizing: border-box;
  margin-top: 0; /* 保持没有负边距 */
  min-height: 60rpx; /* 增加最小高度 */
}

.color-name {
  font-size: 26rpx; /* 稍微增加字体大小 */
  color: #333;
  font-weight: 500;
  margin-bottom: 6rpx; /* 增加底部边距，增加与色值的间距 */
  line-height: 1.3; /* 增加行高 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-top: 2rpx; /* 添加少量顶部内边距 */
}

.color-hex {
  font-size: 22rpx; /* 稍微增加字体大小 */
  color: #666;
  font-weight: normal;
  line-height: 1.2; /* 增加行高 */
  margin-top: 0; /* 保持没有负边距 */
  padding-bottom: 4rpx; /* 增加底部内边距 */
}


