<!--pages/colorQuery/colorQuery.wxml - 优化后的语义化结构-->
<view class="page" bindtouchstart="handleTouchStart" bindtouchend="handleTouchEnd" aria-role="main" aria-label="中国传统色彩查询" data-type="color-collection" data-category="traditional-chinese-colors" data-culture="chinese">

  <!-- 分类选择区域 -->
  <view class="fixed-area" aria-role="navigation" aria-label="色彩分类导航">
    <view class="category-container">
      <scroll-view scroll-x="true" class="category-scroll" scroll-into-view="category-{{activeCategory}}" scroll-with-animation="true" aria-role="tablist">
        <view class="category-list">
          <view
            wx:for="{{categories}}"
            wx:key="id"
            id="category-{{item.id}}"
            class="category-item {{activeCategory === item.id ? 'active' : ''}}"
            style="{{activeCategory === item.id ? 'background-color: ' + activeCategoryColor + '; color: #ffffff;' : ''}}"
            bindtap="switchCategory"
            data-id="{{item.id}}"
            aria-role="tab"
            aria-selected="{{activeCategory === item.id}}"
            aria-label="{{item.name}}色系"
          >
            {{item.name}}
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 颜色列表区域 -->
  <view class="color-list-container" style="padding-top: {{totalTopHeight - 10}}px;" aria-role="region" aria-label="色彩列表">
    <view class="color-grid" aria-role="list">
      <view
        wx:for="{{currentColors}}"
        wx:key="id"
        class="color-item"
        bindtap="goToColorDetail"
        data-id="{{item.id}}"
        aria-role="listitem"
        aria-label="{{item.name}}，色值{{item.hex}}"
      >
        <view class="color-preview" style="background-color: {{item.hex}};" aria-hidden="true"></view>
        <view class="color-info">
          <view class="color-name" aria-role="heading">{{item.name}}</view>
          <view class="color-hex">{{item.hex}}</view>
        </view>
      </view>
    </view>
  </view>
</view>
