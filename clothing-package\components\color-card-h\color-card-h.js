// components/color-card-h/color-card-h.js - 色卡H (9:16)
const colorUtils = require('../../utils/colorUtils');
const logUtils = require('../../utils/logUtils');

Component({
  properties: {
    colors: {
      type: Array,
      value: []
    },
    imagePath: {
      type: String,
      value: ''
    }
  },

  data: {
    canvasWidth: 1600, // 画布宽度
    canvasHeight: 2766 // 画布高度，参考91601.html
  },

  lifetimes: {
    attached() {
      this.drawColorCard();
    }
  },

  methods: {
    async drawColorCard() {
      const { canvasWidth, canvasHeight } = this.data;
      const { colors, imagePath } = this.properties;

      if (!imagePath || colors.length === 0) return;

      try {
        // 获取Canvas节点
        const query = this.createSelectorQuery();
        const canvas = await new Promise((resolve) => {
          query.select('#colorCardCanvas')
            .fields({ node: true, size: true })
            .exec((res) => {
              resolve(res[0]);
            });
        });

        if (!canvas || !canvas.node) {
          logUtils.error('获取Canvas节点失败');
          return;
        }

        const ctx = canvas.node.getContext('2d');

        // 设置Canvas的实际尺寸
        canvas.node.width = canvasWidth;
        canvas.node.height = canvasHeight;

        // 绘制白色背景
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, canvasWidth, canvasHeight);

        try {
          // 获取图片信息
          const res = await new Promise((resolve, reject) => {
            wx.getImageInfo({
              src: imagePath,
              success: resolve,
              fail: reject
            });
          });

          const { width: imgWidth, height: imgHeight } = res;

          // 创建图片对象
          const img = canvas.node.createImage();
          await new Promise((resolve, reject) => {
            img.onload = resolve;
            img.onerror = reject;
            img.src = imagePath;
          });

          // 绘制图片 - 9:16比例
          const imageWidth = 1500;
          const imageHeight = 2666;
          const imageX = 50;
          const imageY = 50;

          this.drawImage(ctx, img, imageX, imageY, imageWidth, imageHeight, imgWidth, imgHeight);

          // 绘制颜色块 - 参考91601.html的样式
          // 颜色块是矩形，垂直排列，带阴影
          const colorBlockWidth = 748; // 颜色块宽度
          const colorBlockHeight = 180; // 颜色块高度
          const colorBlockX = 426; // 颜色块X坐标
          const colorBlockYPositions = [821, 1057, 1293, 1529, 1765]; // 颜色块Y坐标

          // 绘制颜色块
          this.drawColorBlocks(ctx, colors, colorBlockX, colorBlockWidth, colorBlockHeight, colorBlockYPositions);

          // 保存Canvas
          setTimeout(() => {
            this.saveCanvas(canvas.node);
          }, 300);
        } catch (err) {
          logUtils.error('绘制失败', err);
          this.triggerEvent('generated', { path: imagePath });
        }
      } catch (error) {
        logUtils.error('Canvas初始化失败', error);
        this.triggerEvent('generated', { path: imagePath });
      }
    },

    // 绘制图片 (9:16比例)
    drawImage(ctx, img, destX, destY, destWidth, destHeight, imgWidth, imgHeight) {
      // 计算裁剪参数，保持9:16比例
      let sourceX = 0;
      let sourceY = 0;
      let sourceWidth = imgWidth;
      let sourceHeight = imgHeight;

      // 计算目标区域的实际比例
      const destRatio = destWidth / destHeight;

      // 根据原始图片比例进行裁剪
      const imgRatio = imgWidth / imgHeight;

      if (imgRatio > destRatio) {
        // 图片比目标区域更宽，需要裁剪左右
        sourceWidth = imgHeight * destRatio;
        sourceX = (imgWidth - sourceWidth) / 2;
      } else if (imgRatio < destRatio) {
        // 图片比目标区域更高，需要裁剪上下
        sourceHeight = imgWidth / destRatio;
        sourceY = (imgHeight - sourceHeight) / 2;
      }

      // 绘制图片
      ctx.drawImage(
        img,
        sourceX, sourceY, sourceWidth, sourceHeight, // 源图像参数
        destX, destY, destWidth, destHeight // 目标区域参数
      );
    },

    // 绘制颜色块 - 参考91601.html的样式
    drawColorBlocks(ctx, colors, startX, width, height, yPositions) {
      // 确保最多显示5个颜色
      const actualColors = colors.slice(0, 5);

      // 如果颜色不足5个，补充默认颜色
      while (actualColors.length < 5) {
        const defaultColors = ['#4A4130', '#5D2317', '#900407', '#D8DDE1', '#7E96B2'];
        const missingCount = 5 - actualColors.length;
        for (let i = 0; i < missingCount; i++) {
          actualColors.push(defaultColors[i % defaultColors.length]);
        }
      }

      // 绘制每个颜色块
      actualColors.forEach((color, index) => {
        const x = startX;
        const y = yPositions[index];

        // 绘制带阴影的矩形颜色块
        this.drawShadowRect(ctx, x, y, width, height, color);

        // 获取RGB值
        const rgb = colorUtils.hexToRgb(color);

        // 获取适合背景色的文字颜色 - 优先使用白色
        const textColors = colorUtils.getTextColorsForBackground(color, false, true);

        // 绘制HEX值 - 在上方，使用适合背景的颜色
        ctx.fillStyle = textColors.hexTextColor;
        ctx.font = 'bold 40px Arial, sans-serif';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        const colorCode = color.toUpperCase().replace('#', '');
        ctx.fillText(`#${colorCode}`, x + width/2, y + height/2 - 20);

        // 绘制RGB值 - 在下方，使用80%透明度的颜色
        ctx.fillStyle = textColors.rgbTextColor;
        ctx.font = '28px Arial, sans-serif';
        ctx.fillText(`R${rgb.r} G${rgb.g} B${rgb.b}`, x + width/2, y + height/2 + 30);
      });
    },

    // 绘制带阴影的矩形
    drawShadowRect(ctx, x, y, width, height, fillColor) {
      // 保存当前上下文状态
      ctx.save();

      // 设置阴影
      ctx.shadowColor = 'rgba(0, 0, 0, 0.35)';
      ctx.shadowBlur = 5;
      ctx.shadowOffsetX = 5;
      ctx.shadowOffsetY = 5;

      // 绘制矩形
      ctx.fillStyle = fillColor;
      ctx.fillRect(x, y, width, height);

      // 恢复上下文状态
      ctx.restore();
    },

    // 使用工具函数替代

    // 保存Canvas
    async saveCanvas(canvasNode) {
      try {
        const { canvasWidth, canvasHeight } = this.data;

        const res = await new Promise((resolve, reject) => {
          wx.canvasToTempFilePath({
            canvas: canvasNode,
            width: canvasWidth,
            height: canvasHeight,
            destWidth: canvasWidth,
            destHeight: canvasHeight,
            fileType: 'jpg',
            quality: 1,
            success: resolve,
            fail: reject
          }, this);
        });

        logUtils.log('Canvas保存成功', res.tempFilePath);
        this.triggerEvent('generated', { path: res.tempFilePath });
      } catch (err) {
        logUtils.error('Canvas保存失败', err);
        this.triggerEvent('generated', { path: this.properties.imagePath });
      }
    }
  }
})
