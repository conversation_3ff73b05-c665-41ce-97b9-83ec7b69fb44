/**
 * 通用样式文件 - 基于微信小程序设计规范
 * https://developers.weixin.qq.com/miniprogram/design/
 *
 * 包含:
 * - CSS变量定义
 * - 导航栏样式
 * - 基础布局样式
 * - 组件样式
 * - 工具类样式
 */

/* ===== CSS变量定义 ===== */
page {
  /* 主色调 - 使用微信绿 */
  --primary-color: #07c160;
  --primary-light: rgba(7, 193, 96, 0.1);
  --primary-dark: #06ad56;

  /* 辅助色调 */
  --accent-blue: #10aeff;
  --accent-red: #fa5151;
  --accent-orange: #ffc300;
  --accent-purple: #6467f0;

  /* 文本颜色 */
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-tertiary: #888888;
  --text-light: #ffffff;

  /* 背景颜色 */
  --bg-page: #f7f7f7;
  --bg-card: #ffffff;
  --bg-active: #ececec;

  /* 边框和分隔线 */
  --border-color: rgba(0, 0, 0, 0.1);
  --divider-color: rgba(0, 0, 0, 0.05);

  /* 阴影效果 */
  --shadow-sm: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8rpx 16rpx rgba(0, 0, 0, 0.15);

  /* 圆角大小 */
  --radius-sm: 8rpx;
  --radius-md: 12rpx;
  --radius-lg: 16rpx;

  /* 间距 */
  --spacing-xs: 8rpx;
  --spacing-sm: 16rpx;
  --spacing-md: 24rpx;
  --spacing-lg: 32rpx;
  --spacing-xl: 40rpx;

  /* 动画和过渡 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.25s ease;

  /* 字体大小 */
  --font-size-xs: 24rpx;
  --font-size-sm: 28rpx;
  --font-size-md: 32rpx;
  --font-size-lg: 36rpx;
  --font-size-xl: 44rpx;

  /* 字体粗细 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 600;

  /* 行高 */
  --line-height-compact: 1.3;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.7;

  /* 导航栏相关 */
  --nav-title-font-weight: bold;
  --weui-FG-0: #000000;
  --weui-FG-HALF: #888888;
  --weui-NAV-BAR-TEXT-WEIGHT: bold;
}

/* ===== 导航栏样式 ===== */
/* 导航栏标题加粗 - 使用!important确保覆盖原生样式 */
.wx-navigation-bar__title,
.weui-navigation-bar__title,
.navigation-bar__title {
  font-weight: bold !important;
}

/* 确保在各种场景下都能应用加粗效果 */
.weui-navigation-bar__inner .weui-navigation-bar__center,
.navigation-bar__inner .navigation-bar__center {
  font-weight: bold !important;
}

/* 针对不同机型的适配 */
.android .wx-navigation-bar__title,
.ios .wx-navigation-bar__title {
  font-weight: bold !important;
}

/* 页面容器 */
.page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f7f7f7;
  font-size: 28rpx;
  color: #333333;
  box-sizing: border-box;
}

/* 内容容器 */
.container {
  flex: 1;
  padding: 24rpx;
  box-sizing: border-box;
}

/* 卡片样式 */
.card {
  background-color: #ffffff;
  border-radius: 8rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

/* 标题样式 */
.title {
  font-size: 34rpx;
  font-weight: 500;
  margin-bottom: 16rpx;
  color: #000000;
}

.subtitle {
  font-size: 28rpx;
  color: #888888;
  margin-bottom: 24rpx;
}

/* 分割线 */
.divider {
  height: 1rpx;
  background-color: #eeeeee;
  margin: 24rpx 0;
}

/* 按钮样式 */
.btn {
  display: inline-block;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  text-align: center;
  border-radius: 8rpx;
  background-color: #07c160;
  color: #ffffff;
  margin: 16rpx 0;
  border: none;
  line-height: 1.5;
}

.btn-plain {
  background-color: #f7f7f7;
  color: #333333;
  border: 1rpx solid #dddddd;
}

.btn-small {
  padding: 8rpx 16rpx;
  font-size: 24rpx;
}

.btn-large {
  padding: 20rpx 40rpx;
  font-size: 32rpx;
}

/* 表单元素 */
.input {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  border: 1rpx solid #dddddd;
  border-radius: 8rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  background-color: #ffffff;
}

.label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 8rpx;
}

/* 列表样式 */
.list {
  background-color: #ffffff;
  border-radius: 8rpx;
  overflow: hidden;
}

.list-item {
  padding: 24rpx;
  border-bottom: 1rpx solid #eeeeee;
  display: flex;
  align-items: center;
}

.list-item:last-child {
  border-bottom: none;
}

/* 网格布局 */
.grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
}

/* 颜色相关 */
.color-block {
  width: 100%;
  height: 80rpx;
  border-radius: 4rpx;
  margin-bottom: 8rpx;
}

.color-value {
  font-size: 24rpx;
  color: #666666;
  text-align: center;
}

/* 工具图标 */
.tool-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-bottom: 8rpx;
  background-size: 50%;
  background-position: center;
  background-repeat: no-repeat;
}

/* 标签样式 */
.tag {
  display: inline-block;
  padding: 4rpx 16rpx;
  font-size: 24rpx;
  border-radius: 16rpx;
  background-color: #f2f2f2;
  color: #666666;
  margin-right: 16rpx;
}

/* 提示文本 */
.tip {
  font-size: 24rpx;
  color: #888888;
  margin: 8rpx 0;
}

/* 错误文本 */
.error {
  font-size: 24rpx;
  color: #e64340;
  margin: 8rpx 0;
}

/* 成功文本 */
.success {
  font-size: 24rpx;
  color: #07c160;
  margin: 8rpx 0;
}

/* 警告文本 */
.warning {
  font-size: 24rpx;
  color: #ffb63c;
  margin: 8rpx 0;
}

/* 弹性布局 */
.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-around {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-grow {
  flex-grow: 1;
}

/* 间距 */
.margin-top {
  margin-top: 24rpx;
}

.margin-bottom {
  margin-bottom: 24rpx;
}

.margin-left {
  margin-left: 24rpx;
}

.margin-right {
  margin-right: 24rpx;
}

.padding-top {
  padding-top: 24rpx;
}

.padding-bottom {
  padding-bottom: 24rpx;
}

.padding-left {
  padding-left: 24rpx;
}

.padding-right {
  padding-right: 24rpx;
}

/* 文本对齐 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* 文本截断 */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 圆角 */
.radius-small {
  border-radius: 4rpx;
}

.radius-medium {
  border-radius: 8rpx;
}

.radius-large {
  border-radius: 16rpx;
}

.radius-circle {
  border-radius: 50%;
}

/* 阴影 */
.shadow-small {
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.shadow-medium {
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.shadow-large {
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
}
