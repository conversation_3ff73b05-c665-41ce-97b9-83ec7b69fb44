// pages/colorPicker/colorPicker.js
const logUtils = require('../../utils/logUtils');
const loadingUtils = require('../../utils/loadingUtils');
const adFreeUtils = require('../../utils/adFreeUtils');
Page({
  data: {
    imagePath: '',
    templateId: 1,
    colors: [],
    selectedColorIndex: -1,
    imageInfo: null,
    imageLoading: true,
    imageError: false,
    croppedImagePath: '', // 裁剪后的图片路径
    // 激励广告相关
    rewardedVideoAd: null,
    adInitialized: false,
    dailyClickCount: 0, // 当日点击次数
    lastClickDate: '', // 上次点击日期
    // 放大镜相关
    magnifierVisible: false,
    magnifierX: 0,
    magnifierY: 0,
    targetX: 0, // 实际取色目标X坐标
    targetY: 0, // 实际取色目标Y坐标
    currentColor: '#ffffff', // 当前选中的颜色
    magnifierOffsetX: 0, // 放大镜中图片的X偏移
    magnifierOffsetY: 0, // 放大镜中图片的Y偏移
    magnificationRatio: 2, // 放大倍率，确保是2倍
    touchStartTime: 0,
    currentTouchPosition: null,
    imageRect: null,
    isLongPress: false, // 是否处于长按状态
    pageScrollEnabled: true, // 页面是否可滚动
    // 颜色拖拽相关
    dragIndex: -1, // 当前拖拽的颜色索引
    dragStartX: 0, // 拖拽开始的X坐标
    dragOffsetX: 0, // 拖拽的X偏移量
    colorItemRects: [], // 存储每个颜色项的位置信息
    dragLongPressTimer: null, // 长按定时器
    isDragging: false, // 是否处于拖拽状态
    // 算法选择相关
    showAlgorithmModal: false, // 是否显示算法选择弹窗
    currentAlgorithm: 'default', // 当前选择的算法
    tempAlgorithm: 'default', // 临时选择的算法，确认后才会更新currentAlgorithm
    algorithms: [
      {
        id: 'default',
        name: '默认算法',
        description: '基于HSV色彩空间的量化方法，提取图像中最具代表性的颜色。',
        usage: '适用于大多数场景，能够提取出图像中最主要的颜色'
      },
      {
        id: 'histogram',
        name: '颜色直方图算法',
        description: '统计图像中各颜色出现的频率，提取出现频率最高的颜色。',
        usage: '适用于颜色分布明显的图像，能够准确反映图像中的主要颜色'
      },
      {
        id: 'kmeans',
        name: 'K-Means聚类算法',
        description: '将图像中的颜色进行聚类，提取每个聚类的中心颜色。',
        usage: '适用于颜色丰富的图像，能够提取出不同色系的代表色'
      },
      {
        id: 'visual',
        name: '视觉特性算法',
        description: '考虑人眼对颜色的感知特性，提取视觉上最突出的颜色。',
        usage: '适用于需要考虑视觉效果的场景，提取的颜色更符合人眼感知'
      }
    ]
  },

  onLoad(options) {
    // 重置裁剪图片路径，确保每次进入页面都重新裁剪
    this.setData({
      croppedImagePath: ''
    });

    // 初始化每日点击计数
    this.initDailyClickCount();

    // 延迟初始化激励广告，避免阻塞页面加载
    wx.nextTick(() => {
      setTimeout(() => {
        this.initRewardedVideoAd();
      }, 100);
    });

    if (options.imagePath) {
      const imagePath = decodeURIComponent(options.imagePath);
      // 确保templateId是数字类型
      const templateId = parseInt(options.templateId) || 1;

      this.setData({
        imagePath,
        templateId
      });



      // 获取图片信息
      wx.getImageInfo({
        src: imagePath,
        success: (res) => {
          this.setData({
            imageInfo: res
          });

          // 自动提取颜色
          this.extractColors();

          // 初始化放大镜设置
          this.initMagnifier();
        },
        fail: (err) => {
          wx.showToast({
            title: '图片加载失败',
            icon: 'none'
          });
        }
      });
    }

    // 获取导航栏高度，用于调整页面内容的顶部内边距
    this.getNavBarHeight();
  },

  // 获取导航栏高度
  getNavBarHeight() {
    try {
      // 使用新的API获取窗口信息
      const windowInfo = wx.getWindowInfo();
      const systemInfo = windowInfo;

      // 获取胶囊按钮位置信息
      let menuButtonInfo;
      try {
        menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      } catch (err) {
        // 使用默认值
        menuButtonInfo = {
          top: systemInfo.statusBarHeight + 8,
          bottom: systemInfo.statusBarHeight + 8 + 32,
          height: 32
        };
      }

      // 计算导航栏高度 = 胶囊按钮底部位置 + 顶部安全距离
      const navBarHeight = menuButtonInfo.bottom + 20; // 增加额外的间距

      // 获取导航栏组件高度
      const query = wx.createSelectorQuery();
      query.select('navigation-bar').boundingClientRect();
      query.exec((res) => {
        if (res && res[0]) {
          const navBarComponent = res[0];
          // 计算页面内容的顶部内边距和标题的顶部边距
          const pagePadding = navBarComponent.height + 20; // 导航栏高度 + 额外间距

          this.setData({
            pagePaddingTop: pagePadding + 'px',
            titleMarginTop: '20rpx' // 给标题添加一些顶部边距
          });
        } else {
          // 如果无法获取导航栏组件，使用默认值
          this.setData({
            pagePaddingTop: navBarHeight + 'px',
            titleMarginTop: '20rpx'
          });
        }
      });
    } catch (err) {
      // 出错时使用默认值
      this.setData({
        pagePaddingTop: '220rpx',
        titleMarginTop: '20rpx'
      });
    }
  },

  // 初始化放大镜设置
  async initMagnifier() {
    try {
      // 使用新的API获取窗口信息
      const windowInfo = wx.getWindowInfo();

      // 设置放大镜参数
      this.setData({
        magnificationRatio: 3.0, // 增加放大倍率，提高取色精度
        magnifierVisible: false
      });

      // 预加载图片，确保图片信息可用
      try {
        const imageInfo = await new Promise((resolve, reject) => {
          wx.getImageInfo({
            src: this.data.imagePath,
            success: (res) => {
              resolve(res);
            },
            fail: (err) => {
              reject(err);
            }
          });
        });

      } catch (imgErr) {
        // 图片加载失败，忽略错误
      }
    } catch (err) {
      // 初始化放大镜失败，忽略错误
    }
  },

  // 页面滚动事件处理函数 - 这是一个页面生命周期方法
  onPageScroll(e) {
    // 如果处于长按状态，阻止页面滚动
    if (this.data.isLongPress && this.data.magnifierVisible) {
      // 尝试将页面滚回原位置，使用更短的持续时间
      wx.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
    }
  },

  // 显示算法选择器
  showAlgorithmSelector() {
    // 设置临时算法为当前算法
    this.setData({
      showAlgorithmModal: true,
      tempAlgorithm: this.data.currentAlgorithm
    });

    // 延迟一下，确保弹窗显示后再次确认临时算法值
    setTimeout(() => {
      this.setData({
        tempAlgorithm: this.data.currentAlgorithm
      });
    }, 50);
  },

  // 隐藏算法选择器
  hideAlgorithmSelector() {
    this.setData({
      showAlgorithmModal: false
    });
  },

  // 选择算法
  selectAlgorithm(e) {
    const algorithmId = e.currentTarget.dataset.id;
    this.setData({
      tempAlgorithm: algorithmId
    });

    // 添加触感反馈
    if (wx.vibrateShort) {
      wx.vibrateShort({
        type: 'light'
      });
    }
  },

  // 确认算法选择
  confirmAlgorithm() {
    const { tempAlgorithm, currentAlgorithm } = this.data;

    // 如果算法没有变化，直接关闭弹窗
    if (tempAlgorithm === currentAlgorithm) {
      this.hideAlgorithmSelector();
      wx.showToast({
        title: '算法未变更',
        icon: 'none',
        duration: 1000
      });
      return;
    }

    // 先关闭弹窗
    this.setData({
      showAlgorithmModal: false
    });

    // 延迟一下再更新算法，让UI有时间响应
    setTimeout(async () => {
      try {
        // 使用 loadingUtils 安全地管理加载状态
        await loadingUtils.withLoading(async () => {
          // 更新当前算法
          this.setData({
            currentAlgorithm: tempAlgorithm
          });

          // 直接调用颜色提取逻辑，不使用 withLoading 包装
          const colors = await this.extractColorsInternal();

          // 如果颜色提取成功，更新数据
          if (colors && colors.length > 0) {
            this.setData({ colors });
          } else {
            // 如果没有提取到颜色，使用默认颜色
            this.useDefaultColors();
          }
        }, {
          title: '切换算法中...',
          mask: true
        });

        // 显示提示
        wx.showToast({
          title: '算法已切换',
          icon: 'success',
          duration: 1000
        });

        // 添加触感反馈
        if (wx.vibrateShort) {
          wx.vibrateShort({
            type: 'medium'
          });
        }
      } catch (error) {
        // 切换失败，恢复原算法
        this.setData({
          currentAlgorithm: currentAlgorithm
        });

        wx.showToast({
          title: '算法切换失败',
          icon: 'none',
          duration: 1000
        });
      }
    }, 100);
  },

  // 自动提取颜色 - 根据选择的算法（带加载提示）
  async extractColors() {
    try {
      // 使用 loadingUtils 安全地管理加载状态
      const colors = await loadingUtils.withLoading(async () => {
        return await this.extractColorsInternal();
      }, {
        title: '正在分析颜色...',
        mask: true
      });

      // 如果颜色提取成功，更新数据
      if (colors && colors.length > 0) {
        this.setData({ colors });

        // 显示成功提示
        wx.showToast({
          title: '颜色提取完成',
          icon: 'success',
          duration: 1000
        });
      } else {
        // 如果没有提取到颜色，使用默认颜色
        this.useDefaultColors();
      }
    } catch (err) {
      // 失败时使用默认颜色
      this.useDefaultColors();
    }
  },

  // 内部颜色提取逻辑（不带加载提示）
  async extractColorsInternal() {
    const { imagePath, imageInfo, currentAlgorithm } = this.data;
    if (!imagePath || !imageInfo) return [];

    // 根据选择的算法提取颜色
    switch (currentAlgorithm) {
      case 'histogram':
        return await this.extractColorsHistogram(imagePath);
      case 'kmeans':
        return await this.extractColorsKMeans(imagePath);
      case 'visual':
        return await this.extractColorsVisual(imagePath);
      default:
        return await this.extractColorsNative(imagePath);
    }
  },

  // 使用微信原生API提取颜色
  async extractColorsNative(imagePath) {
    // 使用微信的图片信息API获取图片信息
    const imageInfo = await new Promise((resolve, reject) => {
      wx.getImageInfo({
        src: imagePath,
        success: resolve,
        fail: reject
      });
    });

    // 使用微信的文件系统API读取图片
    const fileManager = wx.getFileSystemManager();
    const fileData = await new Promise((resolve, reject) => {
      fileManager.readFile({
        filePath: imagePath,
        success: res => resolve(res.data),
        fail: reject
      });
    });

    // 创建一个临时文件用于处理图片
    const tempFilePath = `${wx.env.USER_DATA_PATH}/temp_image_${Date.now()}.jpg`;

    // 将图片保存为临时文件
    await new Promise((resolve, reject) => {
      fileManager.writeFile({
        filePath: tempFilePath,
        data: fileData,
        encoding: 'binary',
        success: resolve,
        fail: reject
      });
    });

    // 使用微信的图片API获取主色调
    // 注意：微信没有直接提供获取图片主色调的API，我们使用简化的采样方法

    // 采样点数量
    const samplePoints = 100;

    // 采样颜色数组
    const sampledColors = [];

    // 使用临时文件创建一个小尺寸的图片用于采样
    const smallImageInfo = await new Promise((resolve, reject) => {
      wx.getImageInfo({
        src: tempFilePath,
        success: resolve,
        fail: reject
      });
    });

    // 计算采样步长
    const stepX = Math.max(1, Math.floor(smallImageInfo.width / Math.sqrt(samplePoints)));
    const stepY = Math.max(1, Math.floor(smallImageInfo.height / Math.sqrt(samplePoints)));

    // 创建一个小尺寸的临时图片用于采样
    const smallTempFilePath = `${wx.env.USER_DATA_PATH}/small_temp_image_${Date.now()}.jpg`;

    // 使用canvas绘制小尺寸图片
    const canvasId = 'tempCanvas';
    const canvas = await new Promise((resolve) => {
      const query = wx.createSelectorQuery();
      query.select('#' + canvasId)
        .fields({ node: true, size: true })
        .exec((res) => {
          resolve(res[0]);
        });
    });

    if (!canvas || !canvas.node) {
      // 返回默认颜色数组而不是调用 useDefaultColors
      return [
        '#D8DDE1', // 浅灰色
        '#7F96B3', // 蓝灰色
        '#231A12', // 深棕色
        '#3A4250', // 深灰蓝色
        '#6A8CAF'  // 中蓝色
      ];
    }

    const ctx = canvas.node.getContext('2d');
    canvas.node.width = smallImageInfo.width;
    canvas.node.height = smallImageInfo.height;

    // 创建图片对象
    const img = canvas.node.createImage();
    await new Promise((resolve, reject) => {
      img.onload = resolve;
      img.onerror = reject;
      img.src = tempFilePath;
    });

    // 绘制图片到canvas
    ctx.drawImage(img, 0, 0, smallImageInfo.width, smallImageInfo.height);

    // 获取图片数据
    const imageData = ctx.getImageData(0, 0, smallImageInfo.width, smallImageInfo.height);

    // 采样颜色
    for (let y = 0; y < smallImageInfo.height; y += stepY) {
      for (let x = 0; x < smallImageInfo.width; x += stepX) {
        const i = (y * smallImageInfo.width + x) * 4;

        // 获取RGB值
        const r = imageData.data[i];
        const g = imageData.data[i + 1];
        const b = imageData.data[i + 2];
        const a = imageData.data[i + 3];

        // 忽略透明像素
        if (a < 128) continue;

        // 转换为HSV
        const hsv = this.rgbToHsv(r, g, b);

        // 判断是否为背景区域（图像边缘）
        const isBackground =
          x < smallImageInfo.width * 0.1 ||
          x >= smallImageInfo.width * 0.9 ||
          y < smallImageInfo.height * 0.1 ||
          y >= smallImageInfo.height * 0.9;

        // 创建颜色对象
        sampledColors.push({
          r, g, b,
          h: hsv.h,
          s: hsv.s,
          v: hsv.v,
          isBackground
        });
      }
    }

    // 清理临时文件
    // 删除第一个临时文件
    try {
      fileManager.unlinkSync(tempFilePath);
    } catch (e) {
      // 清理临时文件失败，忽略错误
    }

    // 我们不再尝试删除smallTempFilePath，因为它实际上并没有被创建
    // 如果将来需要删除这个文件，应该确保它已经被正确创建

    // 使用简化的颜色提取算法
    // 按色相分组
    const hueGroups = {};
    for (const color of sampledColors) {
      // 忽略饱和度或明度过低的颜色
      if (color.s < 10 || color.v < 10 || color.v > 95) continue;

      // 计算色相组
      const hueGroup = Math.floor(color.h / 30);
      if (!hueGroups[hueGroup]) {
        hueGroups[hueGroup] = [];
      }
      hueGroups[hueGroup].push(color);
    }

    // 找出主要色相组
    const sortedHueGroups = Object.keys(hueGroups)
      .sort((a, b) => hueGroups[b].length - hueGroups[a].length);

    // 提取主要颜色
    const selectedColors = [];

    // 从每个主要色相组中选择一个代表色
    for (const hueGroup of sortedHueGroups) {
      if (selectedColors.length >= 5) break;

      const colors = hueGroups[hueGroup];
      if (colors.length < 3) continue; // 忽略样本太少的组

      // 计算平均RGB
      let sumR = 0, sumG = 0, sumB = 0;
      for (const color of colors) {
        sumR += color.r;
        sumG += color.g;
        sumB += color.b;
      }

      const avgColor = {
        r: Math.round(sumR / colors.length),
        g: Math.round(sumG / colors.length),
        b: Math.round(sumB / colors.length)
      };

      // 检查是否与已选颜色过于相似
      let isTooSimilar = false;
      for (const selected of selectedColors) {
        const distance = Math.sqrt(
          Math.pow(avgColor.r - selected.r, 2) +
          Math.pow(avgColor.g - selected.g, 2) +
          Math.pow(avgColor.b - selected.b, 2)
        );

        if (distance < 40) {
          isTooSimilar = true;
          break;
        }
      }

      if (!isTooSimilar) {
        selectedColors.push(avgColor);
      }
    }

    // 如果颜色不足5个，添加默认颜色
    if (selectedColors.length < 5) {
      const defaultColors = [
        { r: 216, g: 221, b: 225 },   // 浅灰色
        { r: 127, g: 150, b: 179 },   // 蓝灰色
        { r: 35, g: 26, b: 18 },      // 深棕色
        { r: 58, g: 66, b: 80 },      // 深灰蓝色
        { r: 106, g: 140, b: 175 }    // 中蓝色
      ];

      for (const color of defaultColors) {
        if (selectedColors.length >= 5) break;

        // 检查是否与已选颜色过于相似
        let isTooSimilar = false;
        for (const selected of selectedColors) {
          const distance = Math.sqrt(
            Math.pow(color.r - selected.r, 2) +
            Math.pow(color.g - selected.g, 2) +
            Math.pow(color.b - selected.b, 2)
          );

          if (distance < 40) {
            isTooSimilar = true;
            break;
          }
        }

        if (!isTooSimilar) {
          selectedColors.push(color);
        }
      }
    }

    // 转换为十六进制颜色代码
    return selectedColors.map(color => this.rgbToHex(color.r, color.g, color.b));
  },

  // 颜色直方图算法
  async extractColorsHistogram(imagePath) {
    // 获取图片信息和Canvas上下文
    const { canvas, ctx, imageData, smallImageInfo } = await this.prepareImageData(imagePath);
    if (!canvas || !ctx || !imageData || !smallImageInfo) {
      // 返回默认颜色数组而不是调用 useDefaultColors
      return [
        '#D8DDE1', // 浅灰色
        '#7F96B3', // 蓝灰色
        '#231A12', // 深棕色
        '#3A4250', // 深灰蓝色
        '#6A8CAF'  // 中蓝色
      ];
    }

    // 创建颜色直方图
    const colorBins = {}; // 颜色桶
    const colorPrecision = 8; // 颜色精度，值越小，颜色合并越多

    // 遍历图像像素
    for (let y = 0; y < smallImageInfo.height; y++) {
      for (let x = 0; x < smallImageInfo.width; x++) {
        const i = (y * smallImageInfo.width + x) * 4;

        // 获取RGB值
        const r = imageData.data[i];
        const g = imageData.data[i + 1];
        const b = imageData.data[i + 2];
        const a = imageData.data[i + 3];

        // 忽略透明像素
        if (a < 128) continue;

        // 量化颜色，减少颜色数量
        const quantizedR = Math.floor(r / colorPrecision) * colorPrecision;
        const quantizedG = Math.floor(g / colorPrecision) * colorPrecision;
        const quantizedB = Math.floor(b / colorPrecision) * colorPrecision;

        // 创建颜色键
        const colorKey = `${quantizedR},${quantizedG},${quantizedB}`;

        // 更新颜色直方图
        if (!colorBins[colorKey]) {
          colorBins[colorKey] = {
            r: quantizedR,
            g: quantizedG,
            b: quantizedB,
            count: 0
          };
        }
        colorBins[colorKey].count++;
      }
    }

    // 将颜色桶转换为数组并按出现频率排序
    const sortedColors = Object.values(colorBins)
      .sort((a, b) => b.count - a.count);

    // 过滤掉太暗或太亮的颜色
    const filteredColors = sortedColors.filter(color => {
      const hsv = this.rgbToHsv(color.r, color.g, color.b);
      return hsv.s > 10 && hsv.v > 10 && hsv.v < 95;
    });

    // 选择前5个颜色，确保颜色多样性
    const selectedColors = [];
    for (const color of filteredColors) {
      if (selectedColors.length >= 5) break;

      // 检查是否与已选颜色过于相似
      let isTooSimilar = false;
      for (const selected of selectedColors) {
        const distance = Math.sqrt(
          Math.pow(color.r - selected.r, 2) +
          Math.pow(color.g - selected.g, 2) +
          Math.pow(color.b - selected.b, 2)
        );

        if (distance < 40) {
          isTooSimilar = true;
          break;
        }
      }

      if (!isTooSimilar) {
        selectedColors.push(color);
      }
    }

    // 如果颜色不足5个，添加默认颜色
    if (selectedColors.length < 5) {
      return this.addDefaultColors(selectedColors);
    }

    // 转换为十六进制颜色代码
    return selectedColors.map(color => this.rgbToHex(color.r, color.g, color.b));
  },

  // K-Means聚类算法
  async extractColorsKMeans(imagePath) {
    // 获取图片信息和Canvas上下文
    const { canvas, ctx, imageData, smallImageInfo } = await this.prepareImageData(imagePath);
    if (!canvas || !ctx || !imageData || !smallImageInfo) {
      // 返回默认颜色数组而不是调用 useDefaultColors
      return [
        '#D8DDE1', // 浅灰色
        '#7F96B3', // 蓝灰色
        '#231A12', // 深棕色
        '#3A4250', // 深灰蓝色
        '#6A8CAF'  // 中蓝色
      ];
    }

    // 采样像素
    const pixels = [];
    const sampleStep = Math.max(1, Math.floor(Math.sqrt(smallImageInfo.width * smallImageInfo.height) / 20));

    for (let y = 0; y < smallImageInfo.height; y += sampleStep) {
      for (let x = 0; x < smallImageInfo.width; x += sampleStep) {
        const i = (y * smallImageInfo.width + x) * 4;

        // 获取RGB值
        const r = imageData.data[i];
        const g = imageData.data[i + 1];
        const b = imageData.data[i + 2];
        const a = imageData.data[i + 3];

        // 忽略透明像素
        if (a < 128) continue;

        // 添加到像素数组
        pixels.push([r, g, b]);
      }
    }

    // 如果像素太少，使用默认颜色
    if (pixels.length < 10) {
      return [
        '#D8DDE1', // 浅灰色
        '#7F96B3', // 蓝灰色
        '#231A12', // 深棕色
        '#3A4250', // 深灰蓝色
        '#6A8CAF'  // 中蓝色
      ];
    }

    // K-Means聚类
    const k = 5; // 聚类数量
    const maxIterations = 10; // 最大迭代次数

    // 随机初始化聚类中心
    let centroids = [];
    for (let i = 0; i < k; i++) {
      const randomIndex = Math.floor(Math.random() * pixels.length);
      centroids.push(pixels[randomIndex].slice());
    }

    // 迭代聚类
    for (let iter = 0; iter < maxIterations; iter++) {
      // 分配像素到最近的聚类
      const clusters = Array(k).fill().map(() => []);

      for (const pixel of pixels) {
        let minDistance = Infinity;
        let closestCluster = 0;

        for (let j = 0; j < k; j++) {
          const distance = Math.sqrt(
            Math.pow(pixel[0] - centroids[j][0], 2) +
            Math.pow(pixel[1] - centroids[j][1], 2) +
            Math.pow(pixel[2] - centroids[j][2], 2)
          );

          if (distance < minDistance) {
            minDistance = distance;
            closestCluster = j;
          }
        }

        clusters[closestCluster].push(pixel);
      }

      // 更新聚类中心
      const newCentroids = [];
      for (let j = 0; j < k; j++) {
        if (clusters[j].length === 0) {
          // 如果聚类为空，保持原中心
          newCentroids.push(centroids[j]);
          continue;
        }

        // 计算新的聚类中心
        const sum = [0, 0, 0];
        for (const pixel of clusters[j]) {
          sum[0] += pixel[0];
          sum[1] += pixel[1];
          sum[2] += pixel[2];
        }

        newCentroids.push([
          Math.round(sum[0] / clusters[j].length),
          Math.round(sum[1] / clusters[j].length),
          Math.round(sum[2] / clusters[j].length)
        ]);
      }

      // 检查是否收敛
      let converged = true;
      for (let j = 0; j < k; j++) {
        const distance = Math.sqrt(
          Math.pow(newCentroids[j][0] - centroids[j][0], 2) +
          Math.pow(newCentroids[j][1] - centroids[j][1], 2) +
          Math.pow(newCentroids[j][2] - centroids[j][2], 2)
        );

        if (distance > 1) {
          converged = false;
          break;
        }
      }

      centroids = newCentroids;

      if (converged) break;
    }

    // 按聚类大小排序
    const clusterSizes = Array(k).fill(0);
    for (const pixel of pixels) {
      let minDistance = Infinity;
      let closestCluster = 0;

      for (let j = 0; j < k; j++) {
        const distance = Math.sqrt(
          Math.pow(pixel[0] - centroids[j][0], 2) +
          Math.pow(pixel[1] - centroids[j][1], 2) +
          Math.pow(pixel[2] - centroids[j][2], 2)
        );

        if (distance < minDistance) {
          minDistance = distance;
          closestCluster = j;
        }
      }

      clusterSizes[closestCluster]++;
    }

    // 创建颜色对象并排序
    const colors = centroids.map((centroid, index) => ({
      r: centroid[0],
      g: centroid[1],
      b: centroid[2],
      size: clusterSizes[index]
    })).sort((a, b) => b.size - a.size);

    // 转换为十六进制颜色代码
    return colors.map(color => this.rgbToHex(color.r, color.g, color.b));
  },



  // 视觉特性算法
  async extractColorsVisual(imagePath) {
    // 获取图片信息和Canvas上下文
    const { canvas, ctx, imageData, smallImageInfo } = await this.prepareImageData(imagePath);
    if (!canvas || !ctx || !imageData || !smallImageInfo) {
      // 返回默认颜色数组而不是调用 useDefaultColors
      return [
        '#D8DDE1', // 浅灰色
        '#7F96B3', // 蓝灰色
        '#231A12', // 深棕色
        '#3A4250', // 深灰蓝色
        '#6A8CAF'  // 中蓝色
      ];
    }

    // 采样像素并转换为Lab色彩空间
    const pixels = [];
    const sampleStep = Math.max(1, Math.floor(Math.sqrt(smallImageInfo.width * smallImageInfo.height) / 20));

    for (let y = 0; y < smallImageInfo.height; y += sampleStep) {
      for (let x = 0; x < smallImageInfo.width; x += sampleStep) {
        const i = (y * smallImageInfo.width + x) * 4;

        // 获取RGB值
        const r = imageData.data[i];
        const g = imageData.data[i + 1];
        const b = imageData.data[i + 2];
        const a = imageData.data[i + 3];

        // 忽略透明像素
        if (a < 128) continue;

        // 计算视觉显著性
        // 这里使用简化的方法：饱和度和明度的加权和
        const hsv = this.rgbToHsv(r, g, b);
        const saliency = hsv.s * 0.7 + hsv.v * 0.3;

        // 添加到像素数组
        pixels.push({
          r, g, b,
          saliency,
          hsv
        });
      }
    }

    // 按显著性排序
    pixels.sort((a, b) => b.saliency - a.saliency);

    // 选择前20%的像素
    const topPixels = pixels.slice(0, Math.max(5, Math.floor(pixels.length * 0.2)));

    // 按色相分组
    const hueGroups = {};
    for (const pixel of topPixels) {
      const hueGroup = Math.floor(pixel.hsv.h / 30);
      if (!hueGroups[hueGroup]) {
        hueGroups[hueGroup] = [];
      }
      hueGroups[hueGroup].push(pixel);
    }

    // 从每个主要色相组中选择一个代表色
    const selectedColors = [];
    const sortedHueGroups = Object.keys(hueGroups)
      .sort((a, b) => hueGroups[b].length - hueGroups[a].length);

    for (const hueGroup of sortedHueGroups) {
      if (selectedColors.length >= 5) break;

      const colors = hueGroups[hueGroup];
      if (colors.length < 3) continue; // 忽略样本太少的组

      // 计算平均RGB
      let sumR = 0, sumG = 0, sumB = 0;
      for (const color of colors) {
        sumR += color.r;
        sumG += color.g;
        sumB += color.b;
      }

      const avgColor = {
        r: Math.round(sumR / colors.length),
        g: Math.round(sumG / colors.length),
        b: Math.round(sumB / colors.length)
      };

      // 检查是否与已选颜色过于相似
      let isTooSimilar = false;
      for (const selected of selectedColors) {
        const distance = Math.sqrt(
          Math.pow(avgColor.r - selected.r, 2) +
          Math.pow(avgColor.g - selected.g, 2) +
          Math.pow(avgColor.b - selected.b, 2)
        );

        if (distance < 40) {
          isTooSimilar = true;
          break;
        }
      }

      if (!isTooSimilar) {
        selectedColors.push(avgColor);
      }
    }

    // 如果颜色不足5个，添加默认颜色
    if (selectedColors.length < 5) {
      return this.addDefaultColors(selectedColors);
    }

    // 转换为十六进制颜色代码
    return selectedColors.map(color => this.rgbToHex(color.r, color.g, color.b));
  },

  // 准备图像数据
  async prepareImageData(imagePath) {
    try {
      // 使用微信的图片信息API获取图片信息
      const imageInfo = await new Promise((resolve, reject) => {
        wx.getImageInfo({
          src: imagePath,
          success: resolve,
          fail: reject
        });
      });

      // 使用canvas绘制图片
      const canvasId = 'tempCanvas';
      const canvas = await new Promise((resolve) => {
        const query = wx.createSelectorQuery();
        query.select('#' + canvasId)
          .fields({ node: true, size: true })
          .exec((res) => {
            resolve(res[0]);
          });
      });

      if (!canvas || !canvas.node) {
        wx.showToast({
          title: '获取Canvas节点失败',
          icon: 'none'
        });
        return {};
      }

      const ctx = canvas.node.getContext('2d');

      // 设置更小的尺寸以提高性能，根据算法类型调整
      const maxSize = this.data.currentAlgorithm === 'kmeans' ? 150 : 200; // K-Means算法使用更小尺寸
      const scale = Math.min(maxSize / imageInfo.width, maxSize / imageInfo.height);
      const width = Math.round(imageInfo.width * scale);
      const height = Math.round(imageInfo.height * scale);

      canvas.node.width = width;
      canvas.node.height = height;

      // 创建图片对象
      const img = canvas.node.createImage();
      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = imagePath;
      });

      // 绘制图片到canvas
      ctx.drawImage(img, 0, 0, width, height);

      // 获取图片数据
      const imageData = ctx.getImageData(0, 0, width, height);

      return {
        canvas,
        ctx,
        imageData,
        smallImageInfo: { width, height }
      };
    } catch (err) {
      wx.showToast({
        title: '准备图像数据失败',
        icon: 'none'
      });
      return {};
    }
  },

  // 计算两个RGB颜色之间的欧几里得距离
  calculateColorDistance(color1, color2) {
    return Math.sqrt(
      Math.pow(color1.r - color2.r, 2) +
      Math.pow(color1.g - color2.g, 2) +
      Math.pow(color1.b - color2.b, 2)
    );
  },

  // 检查颜色是否与已选颜色过于相似
  isColorTooSimilar(color, selectedColors, threshold = 40) {
    for (const selected of selectedColors) {
      const distance = this.calculateColorDistance(color, selected);
      if (distance < threshold) {
        return true;
      }
    }
    return false;
  },

  // 添加默认颜色
  addDefaultColors(selectedColors) {
    const defaultColors = [
      { r: 216, g: 221, b: 225 },   // 浅灰色
      { r: 127, g: 150, b: 179 },   // 蓝灰色
      { r: 35, g: 26, b: 18 },      // 深棕色
      { r: 58, g: 66, b: 80 },      // 深灰蓝色
      { r: 106, g: 140, b: 175 }    // 中蓝色
    ];

    for (const color of defaultColors) {
      if (selectedColors.length >= 5) break;

      // 使用通用函数检查颜色相似度
      if (!this.isColorTooSimilar(color, selectedColors)) {
        selectedColors.push(color);
      }
    }

    // 转换为十六进制颜色代码
    return selectedColors.map(color => this.rgbToHex(color.r, color.g, color.b));
  },

  // 使用默认颜色
  useDefaultColors() {
    // 失败时使用默认颜色
    const defaultColors = [
      '#D8DDE1', // 浅灰色
      '#7F96B3', // 蓝灰色
      '#231A12', // 深棕色
      '#3A4250', // 深灰蓝色
      '#6A8CAF'  // 中蓝色
    ];
    this.setData({ colors: defaultColors });

    // 显示失败提示
    wx.showToast({
      title: '颜色提取失败，使用默认颜色',
      icon: 'none',
      duration: 2000
    });
    return defaultColors; // 返回数组
  },

  // RGB转十六进制
  rgbToHex(r, g, b) {
    r = Math.max(0, Math.min(255, Math.round(r))).toString(16).padStart(2, '0');
    g = Math.max(0, Math.min(255, Math.round(g))).toString(16).padStart(2, '0');
    b = Math.max(0, Math.min(255, Math.round(b))).toString(16).padStart(2, '0');
    return `#${r}${g}${b}`.toUpperCase();
  },

  // RGB转HSL
  rgbToHsl(r, g, b) {
    r /= 255;
    g /= 255;
    b /= 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h, s, l = (max + min) / 2;

    if (max === min) {
      h = s = 0; // 灰色
    } else {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }

      h /= 6;
    }

    return { h, s, l };
  },

  // 将RGB转换为HSV
  rgbToHsv(r, g, b) {
    r /= 255;
    g /= 255;
    b /= 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h, s, v = max;

    const d = max - min;
    s = max === 0 ? 0 : d / max;

    if (max === min) {
      h = 0; // 灰色
    } else {
      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }
      h /= 6;
    }

    return { h: h * 360, s: s * 100, v: v * 100 };
  },

  // 将HSV转换为RGB
  hsvToRgb(h, s, v) {
    h = h / 360;
    s = s / 100;
    v = v / 100;

    let r, g, b;

    const i = Math.floor(h * 6);
    const f = h * 6 - i;
    const p = v * (1 - s);
    const q = v * (1 - f * s);
    const t = v * (1 - (1 - f) * s);

    switch (i % 6) {
      case 0: r = v; g = t; b = p; break;
      case 1: r = q; g = v; b = p; break;
      case 2: r = p; g = v; b = t; break;
      case 3: r = p; g = q; b = v; break;
      case 4: r = t; g = p; b = v; break;
      case 5: r = v; g = p; b = q; break;
    }

    return {
      r: Math.round(r * 255),
      g: Math.round(g * 255),
      b: Math.round(b * 255)
    };
  },

  // 提取图像中最具视觉效果的颜色 - 基于HSV色彩空间的量化方法
  pixelateAndExtractColors(data, width, height) {
    // 第一步：采样图像像素并转换为HSV色彩空间
    const sampledColors = this.sampleImageColors(data, width, height);

    // 第二步：基于色相(Hue)进行量化
    const { hueHistogram, hueColors } = this.quantizeByHue(sampledColors);

    // 第三步：处理灰度颜色（饱和度低的颜色）
    const { darkGrays, midGrays, lightGrays } = this.categorizeGrayColors(sampledColors);

    // 第四步：找出主要色相区间
    const sortedHueIndices = this.findMainHueRanges(hueHistogram);

    // 第五步：从每个主要色相区间中选择代表色
    let selectedColors = [];

    // 首先尝试添加背景色
    selectedColors = this.addBackgroundColors(sampledColors, selectedColors);

    // 然后从主要色相区间中选择颜色
    selectedColors = this.addMainHueColors(sortedHueIndices, hueHistogram, hueColors, sampledColors, selectedColors);

    // 如果还不足5种颜色，尝试添加灰度颜色
    selectedColors = this.addGrayColors(darkGrays, midGrays, lightGrays, sampledColors, selectedColors);

    // 如果仍然不足5种颜色，添加默认颜色
    selectedColors = this.addDefaultColors(selectedColors);

    // 转换为十六进制颜色代码
    return selectedColors.map(color => this.rgbToHex(color.r, color.g, color.b));
  },

  // 采样图像像素并转换为HSV色彩空间
  sampleImageColors(data, width, height) {
    // 采样步长 - 较小的步长可以捕获更多细节，但会增加计算量
    const sampleStep = Math.max(1, Math.floor(Math.sqrt(width * height) / 100));

    // 存储所有采样的颜色（HSV格式）
    const sampledColors = [];

    // 背景区域的边界宽度（图像边缘多少像素被视为背景）
    const backgroundBorderWidth = Math.max(10, Math.min(width, height) * 0.1);

    // 均匀采样图像
    for (let y = 0; y < height; y += sampleStep) {
      for (let x = 0; x < width; x += sampleStep) {
        const i = (y * width + x) * 4;

        // 获取RGB值
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];
        const a = data[i + 3];

        // 忽略透明像素
        if (a < 128) continue;

        // 转换为HSV
        const hsv = this.rgbToHsv(r, g, b);

        // 判断是否为背景区域（图像边缘）
        const isBackground =
          x < backgroundBorderWidth ||
          x >= width - backgroundBorderWidth ||
          y < backgroundBorderWidth ||
          y >= height - backgroundBorderWidth;

        // 创建颜色对象
        sampledColors.push({
          r: r,
          g: g,
          b: b,
          h: hsv.h,
          s: hsv.s,
          v: hsv.v,
          isBackground: isBackground
        });
      }
    }

    return sampledColors;
  },

  // 基于色相(Hue)进行量化
  quantizeByHue(sampledColors) {
    // 将色相分为36个区间（每10度一个区间）
    const hueHistogram = new Array(36).fill(0);
    const hueColors = new Array(36).fill().map(() => []);

    // 统计每个色相区间的颜色数量
    for (const color of sampledColors) {
      // 忽略饱和度或明度过低的颜色（接近灰色、黑色或白色）
      if (color.s < 10 || color.v < 10 || color.v > 95) continue;

      // 计算色相所在的区间索引
      const hueIndex = Math.floor(color.h / 10) % 36;

      // 增加该区间的计数
      hueHistogram[hueIndex]++;

      // 将颜色添加到对应区间
      hueColors[hueIndex].push(color);
    }

    return { hueHistogram, hueColors };
  },

  // 处理灰度颜色（饱和度低的颜色）
  categorizeGrayColors(sampledColors) {
    const grayColors = sampledColors.filter(color => color.s < 10);

    // 按明度分为3个区间：暗、中、亮
    const darkGrays = grayColors.filter(color => color.v < 30);
    const midGrays = grayColors.filter(color => color.v >= 30 && color.v < 70);
    const lightGrays = grayColors.filter(color => color.v >= 70);

    return { darkGrays, midGrays, lightGrays };
  },

  // 找出主要色相区间
  findMainHueRanges(hueHistogram) {
    // 按颜色数量排序
    return Array.from({ length: 36 }, (_, i) => i)
      .sort((a, b) => hueHistogram[b] - hueHistogram[a]);
  },

  // 计算颜色集合的平均RGB值
  calculateAverageColor(colors) {
    if (!colors || colors.length === 0) return null;

    let sumR = 0, sumG = 0, sumB = 0;
    for (const color of colors) {
      sumR += color.r;
      sumG += color.g;
      sumB += color.b;
    }

    return {
      r: Math.round(sumR / colors.length),
      g: Math.round(sumG / colors.length),
      b: Math.round(sumB / colors.length)
    };
  },

  // 添加背景色
  addBackgroundColors(sampledColors, selectedColors) {
    const backgroundColors = sampledColors.filter(color => color.isBackground);

    if (backgroundColors.length > 0) {
      // 对背景色按HSV进行聚类
      const backgroundHueHistogram = new Array(36).fill(0);
      const backgroundHueColors = new Array(36).fill().map(() => []);

      // 统计背景色的色相直方图
      for (const color of backgroundColors) {
        // 忽略饱和度或明度过低的颜色
        if (color.s < 10 || color.v < 10 || color.v > 95) continue;

        const hueIndex = Math.floor(color.h / 10) % 36;
        backgroundHueHistogram[hueIndex]++;
        backgroundHueColors[hueIndex].push(color);
      }

      // 找出主要背景色相
      const sortedBackgroundHueIndices = Array.from({ length: 36 }, (_, i) => i)
        .sort((a, b) => backgroundHueHistogram[b] - backgroundHueHistogram[a]);

      // 添加最多2个背景色
      for (let i = 0; i < 2; i++) {
        const hueIndex = sortedBackgroundHueIndices[i];
        if (backgroundHueHistogram[hueIndex] > 0) {
          const colors = backgroundHueColors[hueIndex];
          const avgColor = this.calculateAverageColor(colors);
          if (avgColor) {
            selectedColors.push(avgColor);
          }
        }
      }
    }

    return selectedColors;
  },

  // 从主要色相区间中选择颜色
  addMainHueColors(sortedHueIndices, hueHistogram, hueColors, sampledColors, selectedColors) {
    for (let i = 0; i < sortedHueIndices.length; i++) {
      if (selectedColors.length >= 5) break;

      const hueIndex = sortedHueIndices[i];

      // 如果该色相区间有足够的颜色
      if (hueHistogram[hueIndex] > sampledColors.length * 0.01) {
        const colors = hueColors[hueIndex];
        const avgColor = this.calculateAverageColor(colors);

        if (avgColor && !this.isColorTooSimilar(avgColor, selectedColors)) {
          selectedColors.push(avgColor);
        }
      }
    }

    return selectedColors;
  },

  // 添加灰度颜色
  addGrayColors(darkGrays, midGrays, lightGrays, sampledColors, selectedColors) {
    // 添加暗灰色
    if (selectedColors.length < 5 && darkGrays.length > sampledColors.length * 0.05) {
      const avgColor = this.calculateAverageColor(darkGrays);
      if (avgColor) {
        selectedColors.push(avgColor);
      }
    }

    // 添加中灰色
    if (selectedColors.length < 5 && midGrays.length > sampledColors.length * 0.05) {
      const avgColor = this.calculateAverageColor(midGrays);
      if (avgColor) {
        selectedColors.push(avgColor);
      }
    }

    // 添加亮灰色
    if (selectedColors.length < 5 && lightGrays.length > sampledColors.length * 0.05) {
      const avgColor = this.calculateAverageColor(lightGrays);
      if (avgColor) {
        selectedColors.push(avgColor);
      }
    }

    return selectedColors;
  },

  // 选择颜色
  selectColor(e) {
    const index = e.currentTarget.dataset.index;

    // 如果点击的是已选中的颜色，则取消选中
    if (this.data.selectedColorIndex === index) {
      this.setData({
        selectedColorIndex: -1
      });
    } else {
      // 否则选中该颜色
      this.setData({
        selectedColorIndex: index
      });
    }
  },

  // 取色器相关功能已移除

  // 处理容器触摸开始事件
  async handleContainerTouchStart(e) {
    // 如果没有图片信息或没有选中颜色，则不进行操作
    if (!this.data.imageInfo || this.data.selectedColorIndex < 0) {
      // 如果没有选中颜色，提示用户先选择一个颜色
      if (this.data.imageInfo && this.data.selectedColorIndex < 0) {
        wx.showToast({
          title: '请先选择要编辑的颜色',
          icon: 'none',
          duration: 1500
        });
      }
      return;
    }

    try {
      // 确保触摸事件有效
      if (!e.touches || !e.touches[0]) {
        console.error('无效的触摸事件');
        return;
      }

      const touch = e.touches[0];
      const x = touch.pageX || touch.clientX || 0;
      const y = touch.pageY || touch.clientY || 0;

      // 获取图片容器信息
      const query = wx.createSelectorQuery();
      const imageRect = await new Promise((resolve) => {
        query.select('.preview-image').boundingClientRect().exec((res) => {
          if (!res || !res[0]) {
            resolve(null);
          } else {
            resolve(res[0]);
          }
        });
      });

      if (!imageRect) {
        console.error('获取图片位置信息失败');
        return;
      }

      // 检查点击是否在图片内
      if (x < imageRect.left || x > imageRect.right || y < imageRect.top || y > imageRect.bottom) {
        console.log('点击位置在图片外部', x, y, imageRect);
        return;
      }

      // 获取图片的原始尺寸
      const { imageInfo } = this.data;
      const originalWidth = imageInfo.width;
      const originalHeight = imageInfo.height;

      // 计算图片在容器中的实际显示尺寸和位置（考虑aspectFit模式）
      let displayWidth, displayHeight, offsetX, offsetY;

      // 计算图片的宽高比
      const imageRatio = originalWidth / originalHeight;
      // 计算容器的宽高比
      const containerRatio = imageRect.width / imageRect.height;

      if (imageRatio > containerRatio) {
        // 图片较宽，宽度填满容器，高度居中
        displayWidth = imageRect.width;
        displayHeight = displayWidth / imageRatio;
        offsetX = 0;
        offsetY = (imageRect.height - displayHeight) / 2;
      } else {
        // 图片较高，高度填满容器，宽度居中
        displayHeight = imageRect.height;
        displayWidth = displayHeight * imageRatio;
        offsetX = (imageRect.width - displayWidth) / 2;
        offsetY = 0;
      }

      // 计算图片的实际显示区域
      const actualImageRect = {
        left: imageRect.left + offsetX,
        top: imageRect.top + offsetY,
        right: imageRect.left + offsetX + displayWidth,
        bottom: imageRect.top + offsetY + displayHeight,
        width: displayWidth,
        height: displayHeight
      };

      // 检查点击是否在图片的实际显示区域内
      if (x < actualImageRect.left || x > actualImageRect.right ||
          y < actualImageRect.top || y > actualImageRect.bottom) {
        console.log('点击位置在图片实际显示区域外部', x, y, actualImageRect);
        return;
      }

      // 保存图片实际显示区域信息和当前触摸位置
      this.setData({
        imageRect: actualImageRect,
        touchStartTime: Date.now(),
        currentTouchPosition: { x, y }
      });

      // 延迟显示放大镜，模拟长按效果
      this.touchTimer = setTimeout(() => {
        // 显示放大镜
        this.showMagnifier(x, y, actualImageRect);

        // 设置长按状态，禁用页面滚动
        this.setData({
          isLongPress: true,
          pageScrollEnabled: false
        });

        // 震动反馈
        if (wx.vibrateShort) {
          wx.vibrateShort({
            type: 'light'
          });
        }
      }, 150); // 150ms 延迟，使长按更加灵敏
    } catch (err) {
      console.error('触摸开始处理失败:', err);
    }
  },

  // 处理容器触摸移动事件
  handleContainerTouchMove(e) {
    // 阻止默认滚动行为
    if (this.data.isLongPress) {
      e.preventDefault && e.preventDefault();
      e.stopPropagation && e.stopPropagation();
    }

    if (!this.data.magnifierVisible || !this.data.imageRect) return;

    try {
      // 确保触摸事件有效
      if (!e.touches || !e.touches[0]) return;

      const touch = e.touches[0];
      const x = touch.pageX || touch.clientX || 0;
      const y = touch.pageY || touch.clientY || 0;

      // 更新当前触摸位置
      this.setData({
        currentTouchPosition: { x, y }
      });

      // 更新放大镜位置
      this.updateMagnifier(x, y);
    } catch (err) {
      console.error('触摸移动处理失败:', err);
    }
  },

  // 处理容器触摸结束事件
  async handleContainerTouchEnd(e) {
    // 清除定时器
    if (this.touchTimer) {
      clearTimeout(this.touchTimer);
      this.touchTimer = null;
    }

    try {
      // 如果放大镜可见，则取色并隐藏放大镜
      if (this.data.magnifierVisible && this.data.imageRect) {
        const { currentColor } = this.data;

        if (currentColor && currentColor !== '#ffffff') {
          // 更新选中的颜色
          const colors = [...this.data.colors];
          const selectedIndex = this.data.selectedColorIndex;
          colors[selectedIndex] = currentColor.toUpperCase();

          // 更新颜色并取消选中状态
          this.setData({
            colors,
            selectedColorIndex: -1 // 取消选中状态
          });

          // 显示成功提示
          wx.showToast({
            title: '颜色已更新',
            icon: 'success',
            duration: 1500
          });
        } else {
          wx.showToast({
            title: '取色失败',
            icon: 'none'
          });
        }

        // 隐藏放大镜
        this.hideMagnifier();
      }
      // 如果是短按（没有显示放大镜），也执行取色
      else if (Date.now() - this.data.touchStartTime < 200 && this.data.currentTouchPosition && this.data.imageRect) {
        const { x, y } = this.data.currentTouchPosition;
        const imageRect = this.data.imageRect;

        // 检查点击是否在图片内
        if (x < imageRect.left || x > imageRect.right || y < imageRect.top || y > imageRect.bottom) {
          return;
        }

        // 计算点击在图片上的相对位置
        const relativeX = x - imageRect.left;
        const relativeY = y - imageRect.top;

        // 计算点击位置在原始图片上的比例
        const ratioX = relativeX / imageRect.width;
        const ratioY = relativeY / imageRect.height;

        console.log('快速点击取色位置比例:', ratioX, ratioY);

        const hexColor = await this.getColorAtPosition(ratioX, ratioY);

        if (hexColor) {
          // 更新选中的颜色
          const colors = [...this.data.colors];
          const selectedIndex = this.data.selectedColorIndex;
          colors[selectedIndex] = hexColor.toUpperCase();

          // 更新颜色并取消选中状态
          this.setData({
            colors,
            selectedColorIndex: -1 // 取消选中状态
          });

          // 显示成功提示
          wx.showToast({
            title: '颜色已更新',
            icon: 'success',
            duration: 1500
          });
        }
      }
    } catch (err) {
      console.error('触摸结束处理失败:', err);
    } finally {
      // 恢复页面滚动
      this.setData({
        isLongPress: false,
        pageScrollEnabled: true
      });

      // 隐藏放大镜
      this.hideMagnifier();
    }
  },

  // 处理容器触摸取消事件
  handleContainerTouchCancel() {
    // 清除定时器
    if (this.touchTimer) {
      clearTimeout(this.touchTimer);
      this.touchTimer = null;
    }

    // 恢复页面滚动
    this.setData({
      isLongPress: false,
      pageScrollEnabled: true
    });

    // 隐藏放大镜
    this.hideMagnifier();
  },

  // 显示放大镜
  async showMagnifier(x, y, imageRect) {
    if (!imageRect) return;

    // 检查点击是否在图片内
    if (x < imageRect.left || x > imageRect.right || y < imageRect.top || y > imageRect.bottom) {
      return;
    }

    // 计算放大镜位置
    const boundedX = Math.max(imageRect.left, Math.min(x, imageRect.right));
    const boundedY = Math.max(imageRect.top, Math.min(y, imageRect.bottom));

    // 计算放大镜偏移位置（左上方，但不要太远）
    const magnifierSize = 45; // 放大镜半径，单位为px
    const offsetX = boundedX - magnifierSize * 0.8; // 向左偏移0.8倍放大镜半径
    const offsetY = boundedY - magnifierSize * 1.5; // 向上偏移1.5倍放大镜半径

    // 确保放大镜不会超出屏幕边界
    const windowInfo = wx.getWindowInfo(); // 使用新API
    const screenWidth = windowInfo.windowWidth;
    const screenHeight = windowInfo.windowHeight;
    const adjustedX = Math.max(magnifierSize, Math.min(offsetX, screenWidth - magnifierSize));
    const adjustedY = Math.max(magnifierSize, Math.min(offsetY, screenHeight - magnifierSize));

    // 计算点击位置在原始图片上的比例
    const ratioX = (boundedX - imageRect.left) / imageRect.width;
    const ratioY = (boundedY - imageRect.top) / imageRect.height;

    // 获取该位置的颜色
    const hexColor = await this.getColorAtPosition(ratioX, ratioY);

    // 计算放大镜中图片的偏移量
    // 这里的计算逻辑是：
    // 1. 获取放大镜的尺寸（直径）
    // 2. 计算放大后的图片尺寸（原图尺寸 * 放大倍率）
    // 3. 计算触摸点在放大后图片中的位置
    // 4. 调整偏移量，使触摸点在放大镜中居中显示
    const magnifierDiameter = magnifierSize * 2; // 放大镜直径
    const magnificationRatio = this.data.magnificationRatio; // 放大倍率（2倍）

    // 计算放大后的图片尺寸
    const magnifiedImageWidth = imageRect.width * magnificationRatio;
    const magnifiedImageHeight = imageRect.height * magnificationRatio;

    // 计算触摸点在放大后图片中的位置
    const touchPointInMagnifiedImageX = ratioX * magnifiedImageWidth;
    const touchPointInMagnifiedImageY = ratioY * magnifiedImageHeight;

    // 计算图片在放大镜中的偏移量，使触摸点在放大镜中居中显示
    const magnifierOffsetX = touchPointInMagnifiedImageX - magnifierDiameter / 2;
    const magnifierOffsetY = touchPointInMagnifiedImageY - magnifierDiameter / 2;

    this.setData({
      magnifierVisible: true,
      magnifierX: adjustedX,
      magnifierY: adjustedY,
      targetX: boundedX, // 保存实际目标位置
      targetY: boundedY, // 保存实际目标位置
      currentColor: hexColor || '#ffffff', // 设置当前颜色
      magnifierOffsetX: Math.max(0, magnifierOffsetX),
      magnifierOffsetY: Math.max(0, magnifierOffsetY)
    });
  },

  // 更新放大镜位置
  async updateMagnifier(x, y) {
    const imageRect = this.data.imageRect;
    if (!imageRect) return;

    // 限制放大镜在图片范围内
    const boundedX = Math.max(imageRect.left, Math.min(x, imageRect.right));
    const boundedY = Math.max(imageRect.top, Math.min(y, imageRect.bottom));

    // 计算放大镜偏移位置（左上方，但不要太远）
    const magnifierSize = 45; // 放大镜半径，单位为px
    const offsetX = boundedX - magnifierSize * 0.8; // 向左偏移0.8倍放大镜半径
    const offsetY = boundedY - magnifierSize * 1.5; // 向上偏移1.5倍放大镜半径

    // 确保放大镜不会超出屏幕边界
    const windowInfo = wx.getWindowInfo(); // 使用新API
    const screenWidth = windowInfo.windowWidth;
    const screenHeight = windowInfo.windowHeight;
    const adjustedX = Math.max(magnifierSize, Math.min(offsetX, screenWidth - magnifierSize));
    const adjustedY = Math.max(magnifierSize, Math.min(offsetY, screenHeight - magnifierSize));

    // 计算点击位置在原始图片上的比例
    const ratioX = (boundedX - imageRect.left) / imageRect.width;
    const ratioY = (boundedY - imageRect.top) / imageRect.height;

    // 获取该位置的颜色
    const hexColor = await this.getColorAtPosition(ratioX, ratioY);

    // 计算放大镜中图片的偏移量
    // 使用与showMagnifier相同的计算逻辑
    const magnifierDiameter = magnifierSize * 2; // 放大镜直径
    const magnificationRatio = this.data.magnificationRatio; // 放大倍率（2倍）

    // 计算放大后的图片尺寸
    const magnifiedImageWidth = imageRect.width * magnificationRatio;
    const magnifiedImageHeight = imageRect.height * magnificationRatio;

    // 计算触摸点在放大后图片中的位置
    const touchPointInMagnifiedImageX = ratioX * magnifiedImageWidth;
    const touchPointInMagnifiedImageY = ratioY * magnifiedImageHeight;

    // 计算图片在放大镜中的偏移量，使触摸点在放大镜中居中显示
    const magnifierOffsetX = touchPointInMagnifiedImageX - magnifierDiameter / 2;
    const magnifierOffsetY = touchPointInMagnifiedImageY - magnifierDiameter / 2;

    this.setData({
      magnifierX: adjustedX,
      magnifierY: adjustedY,
      targetX: boundedX, // 保存实际目标位置
      targetY: boundedY, // 保存实际目标位置
      currentColor: hexColor || '#ffffff', // 设置当前颜色
      magnifierOffsetX: Math.max(0, magnifierOffsetX),
      magnifierOffsetY: Math.max(0, magnifierOffsetY)
    });
  },

  // 隐藏放大镜
  hideMagnifier() {
    this.setData({
      magnifierVisible: false,
      isLongPress: false,
      pageScrollEnabled: true
    });
  },

  // 取色器相关功能已移除

  // 获取指定位置的颜色 - 使用微信原生API
  async getColorAtPosition(ratioX, ratioY) {
    try {
      // 确保比例值在有效范围内
      if (isNaN(ratioX) || isNaN(ratioY) || ratioX < 0 || ratioX > 1 || ratioY < 0 || ratioY > 1) {
        logUtils.error('无效的坐标比例', { x: ratioX, y: ratioY });
        return null;
      }

      // 创建一个临时文件用于处理图片
      const { croppedImagePath, imagePath } = this.data;
      const imgSrc = croppedImagePath || imagePath;

      logUtils.log('取色使用图片', imgSrc);

      // 使用微信的图片信息API获取图片信息
      const imageInfo = await new Promise((resolve, reject) => {
        wx.getImageInfo({
          src: imgSrc,
          success: resolve,
          fail: reject
        });
      });

      // 获取Canvas节点
      const canvasQuery = wx.createSelectorQuery();
      const canvas = await new Promise((resolve) => {
        canvasQuery.select('#tempCanvas')
          .fields({ node: true, size: true })
          .exec((res) => {
            resolve(res[0]);
          });
      });

      if (!canvas || !canvas.node) {
        console.error('获取Canvas节点失败');
        return null;
      }

      const ctx = canvas.node.getContext('2d');

      // 设置Canvas的实际尺寸为图片的实际尺寸，以提高取色精度
      const canvasWidth = imageInfo.width;
      const canvasHeight = imageInfo.height;
      canvas.node.width = canvasWidth;
      canvas.node.height = canvasHeight;

      // 创建图片对象
      const img = canvas.node.createImage();
      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = imgSrc;
      });

      // 绘制完整图片到canvas，保持原始尺寸
      ctx.drawImage(img, 0, 0, canvasWidth, canvasHeight);

      // 计算在canvas上的坐标，确保是整数
      const canvasX = Math.max(0, Math.min(canvasWidth - 1, Math.floor(ratioX * canvasWidth)));
      const canvasY = Math.max(0, Math.min(canvasHeight - 1, Math.floor(ratioY * canvasHeight)));

      // 获取像素数据
      const width = 1;
      const height = 1;
      const imageData = ctx.getImageData(canvasX, canvasY, width, height);

      // 获取RGB值
      const r = imageData.data[0];
      const g = imageData.data[1];
      const b = imageData.data[2];

      // 转换为十六进制颜色代码
      return this.rgbToHex(r, g, b);
    } catch (err) {
      console.error('获取颜色失败:', err);
      return null;
    }
  },

  // 处理图片加载完成
  handleImageLoad() {
    this.setData({
      imageLoading: false,
      imageError: false
    });

    // 只有当加载的是原始图片且还没有裁剪图片时才进行裁剪
    if (!this.data.croppedImagePath && this.data.imagePath) {
      console.log('开始裁剪图片');
      this.createCroppedImage();
    }
  },

  // 创建裁剪后的图片（根据模板ID设置不同的裁剪比例）- 使用微信原生API
  async createCroppedImage() {
    const { imageInfo, croppedImagePath, templateId } = this.data;

    // 如果已经有裁剪后的图片或没有图片信息，则不进行裁剪
    if (croppedImagePath || !imageInfo) {
      console.log('跳过裁剪：', croppedImagePath ? '已有裁剪图片' : '无图片信息');
      return;
    }

    console.log('开始裁剪图片，图片信息:', imageInfo, '模板ID:', templateId);

    try {
      // 根据模板ID设置不同的裁剪比例
      let targetRatio;
      let cropScaleStr;

      // 确保模板ID是数字类型，并进行比较
      const templateIdNum = parseInt(templateId);

      // 根据模板ID设置裁剪比例
      switch (templateIdNum) {
        case 3: // 色卡A - 1:1
        case 4: // 色卡B - 1:1
          targetRatio = 1 / 1; // 宽:高 = 1:1
          cropScaleStr = "1:1";
          console.log('使用1:1裁剪比例');
          break;
        case 5: // 色卡C - 3:4
        case 6: // 色卡D - 3:4
        case 7: // 色卡E - 3:4
          targetRatio = 3 / 4; // 宽:高 = 3:4
          cropScaleStr = "3:4";
          console.log('使用3:4裁剪比例');
          break;
        case 8: // 色卡F - 16:9
          targetRatio = 16 / 9; // 宽:高 = 16:9
          cropScaleStr = "16:9";
          console.log('使用16:9裁剪比例');
          break;
        case 9: // 色卡G - 4:5
          targetRatio = 4 / 5; // 宽:高 = 4:5
          cropScaleStr = "4:5";
          console.log('使用4:5裁剪比例');
          break;
        case 10: // 色卡H - 9:16
        case 11: // 色卡I - 9:16
          targetRatio = 9 / 16; // 宽:高 = 9:16
          cropScaleStr = "9:16";
          console.log('使用9:16裁剪比例');
          break;
        default:
          // 默认使用1:1比例
          targetRatio = 1 / 1;
          cropScaleStr = "1:1";
          console.log('使用默认1:1裁剪比例');
      }

      // 计算裁剪参数
      const imgWidth = imageInfo.width;
      const imgHeight = imageInfo.height;
      const imgRatio = imgWidth / imgHeight;

      let cropX, cropY, cropWidth, cropHeight;

      if (imgRatio > targetRatio) {
        // 原图较宽，需要裁剪左右两侧
        cropHeight = imgHeight;
        cropWidth = imgHeight * targetRatio;
        cropX = (imgWidth - cropWidth) / 2; // 从中间裁剪
        cropY = 0;
      } else {
        // 原图较高，需要裁剪上下两侧
        cropWidth = imgWidth;
        cropHeight = imgWidth / targetRatio;
        cropX = 0;
        cropY = (imgHeight - cropHeight) / 2; // 从中间裁剪
      }

      // 使用微信的图片裁剪API
      const tempFilePath = await new Promise((resolve, reject) => {
        // 检查是否支持的裁剪比例
        const supportedScales = ["1:1", "3:4", "4:3", "16:9", "9:16", "4:5", "5:4"];

        if (supportedScales.includes(cropScaleStr)) {
          console.log('使用微信裁剪API，裁剪比例:', cropScaleStr);

          wx.cropImage({
            src: imageInfo.path,
            cropScale: cropScaleStr, // 使用正确格式的裁剪比例
            success: (res) => {
              resolve(res.tempFilePath);
            },
            fail: (err) => {
              console.error('微信裁剪API失败，尝试使用Canvas裁剪:', err);
              // 如果微信裁剪API失败，回退到Canvas裁剪
              this.createCroppedImageWithCanvas(targetRatio).then(resolve).catch(reject);
            }
          });
        } else {
          // 如果是其他比例，直接使用Canvas裁剪
          console.log('非标准裁剪比例，直接使用Canvas裁剪');
          this.createCroppedImageWithCanvas(targetRatio).then(resolve).catch(reject);
        }
      });

      // 更新裁剪后的图片路径
      this.setData({
        croppedImagePath: tempFilePath
      });

      console.log('裁剪图片成功:', tempFilePath);
    } catch (err) {
      console.error('裁剪图片失败:', err);
      // 尝试使用Canvas裁剪作为备选方案
      try {
        await this.createCroppedImageWithCanvas();
      } catch (canvasErr) {
        console.error('Canvas裁剪也失败:', canvasErr);
      }
    }
  },

  // 使用Canvas裁剪图片（作为备选方案）
  async createCroppedImageWithCanvas(customRatio) {
    const { imageInfo, templateId } = this.data;

    // 创建Canvas
    const query = wx.createSelectorQuery();
    const canvas = await new Promise((resolve) => {
      query.select('#cropCanvas')
        .fields({ node: true, size: true })
        .exec((res) => {
          if (res && res[0]) {
            resolve(res[0]);
          } else {
            console.error('获取Canvas节点失败');
            resolve(null);
          }
        });
    });

    if (!canvas || !canvas.node) {
      console.error('Canvas节点不存在');
      throw new Error('Canvas节点不存在');
    }

    // 获取Canvas上下文
    const ctx = canvas.node.getContext('2d');

    // 根据模板ID设置不同的裁剪比例和Canvas尺寸
    let targetRatio, canvasWidth, canvasHeight;

    // 如果提供了自定义比例，使用它
    if (customRatio) {
      targetRatio = customRatio;
    } else {
      // 确保模板ID是数字类型，并进行比较
      const templateIdNum = parseInt(templateId);

      // 根据模板ID设置裁剪比例
      switch (templateIdNum) {
        case 3: // 色卡A - 1:1
        case 4: // 色卡B - 1:1
          targetRatio = 1 / 1; // 宽:高 = 1:1
          break;
        case 5: // 色卡C - 3:4
        case 6: // 色卡D - 3:4
        case 7: // 色卡E - 3:4
          targetRatio = 3 / 4; // 宽:高 = 3:4
          break;
        case 8: // 色卡F - 16:9
          targetRatio = 16 / 9; // 宽:高 = 16:9
          break;
        case 9: // 色卡G - 4:5
          targetRatio = 4 / 5; // 宽:高 = 4:5
          break;
        case 10: // 色卡H - 9:16
        case 11: // 色卡I - 9:16
          targetRatio = 9 / 16; // 宽:高 = 9:16
          break;
        default:
          // 默认使用1:1比例
          targetRatio = 1 / 1;
      }
    }

    // 设置Canvas尺寸 - 保持一致的高度，根据比例调整宽度
    const baseHeight = 1200;
    canvasHeight = baseHeight;
    canvasWidth = Math.round(baseHeight * targetRatio);

    console.log(`Canvas裁剪使用比例 ${targetRatio}，尺寸 ${canvasWidth}x${canvasHeight}`);

    // 设置Canvas尺寸
    canvas.node.width = canvasWidth;
    canvas.node.height = canvasHeight;

    // 加载图片
    const img = canvas.node.createImage();
    await new Promise((resolve, reject) => {
      img.onload = resolve;
      img.onerror = reject;
      img.src = imageInfo.path;
    });

    // 计算裁剪参数
    const imgWidth = imageInfo.width;
    const imgHeight = imageInfo.height;
    const imgRatio = imgWidth / imgHeight;

    let sourceX, sourceY, sourceWidth, sourceHeight;

    if (imgRatio > targetRatio) {
      // 原图较宽，需要裁剪左右两侧
      sourceHeight = imgHeight;
      sourceWidth = imgHeight * targetRatio;
      sourceX = (imgWidth - sourceWidth) / 2; // 从中间裁剪
      sourceY = 0;
    } else {
      // 原图较高，需要裁剪上下两侧
      sourceWidth = imgWidth;
      sourceHeight = imgWidth / targetRatio;
      sourceX = 0;
      sourceY = (imgHeight - sourceHeight) / 2; // 从中间裁剪
    }

    // 绘制裁剪后的图片
    ctx.drawImage(
      img,
      sourceX, sourceY, sourceWidth, sourceHeight, // 源图像裁剪参数
      0, 0, canvasWidth, canvasHeight // 目标区域参数
    );

    // 将Canvas转换为临时图片 - 保持高分辨率
    const tempFilePath = await new Promise((resolve, reject) => {
      wx.canvasToTempFilePath({
        canvas: canvas.node,
        width: canvasWidth,
        height: canvasHeight,
        destWidth: canvasWidth,  // 保持原始宽度
        destHeight: canvasHeight, // 保持原始高度
        fileType: 'jpg',
        quality: 1, // 最高质量
        success: (res) => resolve(res.tempFilePath),
        fail: (err) => {
          logUtils.error('Canvas转图片失败', err);
          reject(err);
        }
      });
    });

    // 更新裁剪后的图片路径
    this.setData({
      croppedImagePath: tempFilePath
    });

    logUtils.log('Canvas裁剪图片成功', tempFilePath);
    return tempFilePath;
  },

  // 处理图片加载错误
  handleImageError() {
    this.setData({
      imageLoading: false,
      imageError: true
    });

    wx.showToast({
      title: '图片加载失败',
      icon: 'none',
      duration: 2000
    });
  },

  // 复制颜色代码
  copyColorCode(e) {
    let colorCode;

    // 检查是否有传入的颜色数据
    if (e && e.currentTarget && e.currentTarget.dataset && e.currentTarget.dataset.color) {
      colorCode = e.currentTarget.dataset.color;
    } else {
      // 兼容旧的调用方式
      const { selectedColorIndex, colors } = this.data;
      if (selectedColorIndex < 0 || !colors[selectedColorIndex]) return;
      colorCode = colors[selectedColorIndex];
    }

    wx.setClipboardData({
      data: colorCode,
      success: () => {
        wx.showToast({
          title: '颜色代码已复制',
          icon: 'success',
          duration: 1500
        });
      }
    });
  },

  // 获取所有颜色项的位置信息
  getColorItemRects() {
    return new Promise((resolve) => {
      const query = wx.createSelectorQuery();
      query.selectAll('.color-item').boundingClientRect();
      query.exec((res) => {
        if (res && res[0] && res[0].length > 0) {
          this.setData({
            colorItemRects: res[0]
          });
          resolve(res[0]);
        } else {
          resolve([]);
        }
      });
    });
  },

  // 处理颜色项触摸开始事件
  handleColorTouchStart(e) {
    // 获取触摸的颜色索引
    const index = e.currentTarget.dataset.index;

    // 记录触摸开始位置
    const touch = e.touches[0];
    const startX = touch.clientX;

    // 设置长按定时器，只有长按才能触发拖拽
    this.dragLongPressTimer = setTimeout(async () => {
      // 获取所有颜色项的位置信息
      await this.getColorItemRects();

      // 设置拖拽状态
      this.setData({
        dragIndex: index,
        dragStartX: startX,
        dragOffsetX: 0,
        isDragging: true
      });

      // 震动反馈
      if (wx.vibrateShort) {
        wx.vibrateShort({
          type: 'light'
        });
      }
    }, 300); // 300ms长按触发拖拽
  },

  // 处理颜色项触摸移动事件
  handleColorTouchMove(e) {
    // 如果不是拖拽状态，直接返回
    if (this.data.dragIndex === -1 || !this.data.isDragging) return;

    // 计算拖拽偏移量
    const touch = e.touches[0];
    const currentX = touch.clientX;
    const offsetX = currentX - this.data.dragStartX;

    // 更新拖拽偏移量
    this.setData({
      dragOffsetX: offsetX
    });

    // 检查是否需要交换位置
    this.checkSwapPosition(currentX);
  },

  // 检查是否需要交换位置
  checkSwapPosition(currentX) {
    const { dragIndex, colorItemRects } = this.data;

    // 如果没有位置信息，直接返回
    if (!colorItemRects || colorItemRects.length === 0) return;

    // 获取当前拖拽项的中心位置
    const dragItemRect = colorItemRects[dragIndex];
    const dragItemCenter = dragItemRect.left + this.data.dragOffsetX + (dragItemRect.width / 2);

    // 检查是否与其他颜色项重叠，并决定是否交换位置
    for (let i = 0; i < colorItemRects.length; i++) {
      // 跳过自己
      if (i === dragIndex) continue;

      const rect = colorItemRects[i];
      const rectCenter = rect.left + (rect.width / 2);

      // 如果拖拽项的中心位置在另一个颜色项的范围内，交换位置
      if (dragItemCenter > rect.left && dragItemCenter < rect.right) {
        // 确定交换方向
        const isRightSwap = i > dragIndex;
        const isLeftSwap = i < dragIndex;

        // 只有当拖拽方向与交换方向一致时才交换
        if ((isRightSwap && this.data.dragOffsetX > 0) ||
            (isLeftSwap && this.data.dragOffsetX < 0)) {
          this.swapColors(dragIndex, i);
          break;
        }
      }
    }
  },

  // 交换颜色位置
  swapColors(fromIndex, toIndex) {
    // 获取当前颜色数组
    const colors = [...this.data.colors];

    // 交换颜色
    const temp = colors[fromIndex];
    colors[fromIndex] = colors[toIndex];
    colors[toIndex] = temp;

    // 更新颜色数组和拖拽索引
    this.setData({
      colors,
      dragIndex: toIndex,
      dragStartX: this.data.dragStartX + (toIndex - fromIndex) * this.data.colorItemRects[0].width,
      dragOffsetX: 0
    });

    // 重新获取位置信息
    this.getColorItemRects();

    // 震动反馈
    if (wx.vibrateShort) {
      wx.vibrateShort({
        type: 'light'
      });
    }
  },

  // 处理颜色项触摸结束事件
  handleColorTouchEnd() {
    // 清除长按定时器
    if (this.dragLongPressTimer) {
      clearTimeout(this.dragLongPressTimer);
      this.dragLongPressTimer = null;
    }

    // 重置拖拽状态
    this.setData({
      dragIndex: -1,
      dragOffsetX: 0,
      isDragging: false
    });
  },

  // 处理颜色项触摸取消事件
  handleColorTouchCancel() {
    // 清除长按定时器
    if (this.dragLongPressTimer) {
      clearTimeout(this.dragLongPressTimer);
      this.dragLongPressTimer = null;
    }

    // 重置拖拽状态
    this.setData({
      dragIndex: -1,
      dragOffsetX: 0,
      isDragging: false
    });
  },

  /**
   * 初始化每日点击计数
   */
  initDailyClickCount() {
    try {
      const today = new Date().toDateString();
      const storedData = wx.getStorageSync('colorPicker_dailyClick');

      if (storedData && storedData.date === today) {
        // 今天已有记录，使用存储的点击次数
        this.setData({
          dailyClickCount: storedData.count || 0,
          lastClickDate: storedData.date
        });
      } else {
        // 新的一天，重置计数
        this.setData({
          dailyClickCount: 0,
          lastClickDate: today
        });
        // 保存到本地存储
        wx.setStorageSync('colorPicker_dailyClick', {
          date: today,
          count: 0
        });
      }
    } catch (error) {
      console.error('初始化每日点击计数失败', error);
      // 出错时使用默认值
      this.setData({
        dailyClickCount: 0,
        lastClickDate: new Date().toDateString()
      });
    }
  },

  /**
   * 初始化激励广告
   */
  initRewardedVideoAd() {
    // 防止重复初始化
    if (this.data.adInitialized) {
      return;
    }

    // 检查是否支持激励广告
    if (wx.createRewardedVideoAd) {
      try {
        // 创建激励广告实例
        const rewardedVideoAd = wx.createRewardedVideoAd({
          adUnitId: 'adunit-ff7e770116a50353' // 您的激励广告位ID
        });

        this.setData({
          rewardedVideoAd: rewardedVideoAd,
          adInitialized: true
        });

        // 监听广告加载事件
        rewardedVideoAd.onLoad(() => {
          console.log('色卡制作激励广告加载成功');
        });

        // 监听广告加载失败事件
        rewardedVideoAd.onError(err => {
          console.error('色卡制作激励广告加载失败', err);
        });

        // 监听广告关闭事件
        rewardedVideoAd.onClose(res => {
          if (res && res.isEnded) {
            // 用户完整观看了广告，继续跳转
            this.navigateToPreview();
          } else {
            // 用户中途退出了广告
            wx.showToast({
              title: '请观看完整广告',
              icon: 'none',
              duration: 1500
            });
          }
        });
      } catch (error) {
        console.error('激励广告初始化失败', error);
      }
    } else {
      console.log('当前版本不支持激励广告');
    }
  },

  /**
   * 显示激励广告
   */
  showRewardedVideoAd() {
    const rewardedVideoAd = this.data.rewardedVideoAd;

    // 如果广告还未初始化，先初始化
    if (!rewardedVideoAd) {
      this.initRewardedVideoAd();
      // 给一点时间让广告初始化
      setTimeout(() => {
        this.showRewardedVideoAd();
      }, 1000);
      return;
    }

    // 显示广告
    rewardedVideoAd.show().catch(() => {
      // 广告显示失败，重新加载
      rewardedVideoAd.load()
        .then(() => rewardedVideoAd.show())
        .catch(err => {
          console.error('激励广告显示失败', err);
          // 广告失败时直接跳转
          this.navigateToPreview();
        });
    });
  },

  /**
   * 更新每日点击计数
   */
  updateDailyClickCount() {
    const today = new Date().toDateString();
    let newCount = this.data.dailyClickCount + 1;

    this.setData({
      dailyClickCount: newCount,
      lastClickDate: today
    });

    // 保存到本地存储
    try {
      wx.setStorageSync('colorPicker_dailyClick', {
        date: today,
        count: newCount
      });
    } catch (error) {
      console.error('保存每日点击计数失败', error);
    }
  },

  // 完成颜色选择，跳转到下一步
  goToNext() {
    const { colors, imagePath, templateId, selectedColorIndex, dailyClickCount } = this.data;

    // 检查是否已提取颜色
    if (!colors || colors.length === 0) {
      wx.showToast({
        title: '请先提取颜色',
        icon: 'none'
      });
      return;
    }

    // 更新每日点击计数
    this.updateDailyClickCount();

    // 检查去广告状态
    const isAdFree = adFreeUtils.isAdFreeActivated();

    // 判断是否需要显示激励广告（去广告激活时不显示广告）
    const shouldShowAd = !isAdFree && this.data.dailyClickCount > 1; // 第二次开始显示广告

    // 如果当前有选中的颜色，询问用户是否要取消选中
    if (selectedColorIndex >= 0) {
      wx.showModal({
        title: '提示',
        content: '您当前正在编辑颜色，是否取消选中并继续？',
        confirmText: '继续',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 用户点击继续，取消选中
            this.setData({ selectedColorIndex: -1 }, () => {
              if (shouldShowAd) {
                // 显示激励广告
                this.showRewardedVideoAd();
              } else {
                // 直接跳转
                this.navigateToPreview();
              }
            });
          }
          // 用户点击取消，不做任何操作
        }
      });
    } else {
      // 没有选中的颜色
      if (shouldShowAd) {
        // 显示激励广告
        this.showRewardedVideoAd();
      } else {
        // 直接跳转
        this.navigateToPreview();
      }
    }
  },

  // 跳转到预览页面
  navigateToPreview() {
    const { colors, imagePath, croppedImagePath, templateId } = this.data;

    // 使用裁剪后的图片路径，如果没有则使用原始路径
    const finalImagePath = croppedImagePath || imagePath;

    // 将颜色数组转换为JSON字符串
    const colorsJson = JSON.stringify(colors);

    // 模板ID映射
    let finalTemplateId;

    // 确保模板ID是数字类型，并进行比较
    const templateIdNum = parseInt(templateId);

    // 根据模板ID映射到对应的组件ID
    switch (templateIdNum) {
      case 3: // 色卡A
        finalTemplateId = 7;
        logUtils.log('使用色卡A (1:1)模板');
        break;
      case 4: // 色卡B
        finalTemplateId = 8;
        logUtils.log('使用色卡B (1:1)模板');
        break;
      case 5: // 色卡C
        finalTemplateId = 9;
        logUtils.log('使用色卡C (3:4)模板');
        break;
      case 6: // 色卡D
        finalTemplateId = 10;
        logUtils.log('使用色卡D (3:4)模板');
        break;
      case 7: // 色卡E
        finalTemplateId = 11;
        logUtils.log('使用色卡E (3:4)模板');
        break;
      case 8: // 色卡F
        finalTemplateId = 12;
        logUtils.log('使用色卡F (16:9)模板');
        break;
      case 9: // 色卡G
        finalTemplateId = 13;
        logUtils.log('使用色卡G (4:5)模板');
        break;
      case 10: // 色卡H
        finalTemplateId = 14;
        logUtils.log('使用色卡H (9:16)模板');
        break;
      case 11: // 色卡I
        finalTemplateId = 15;
        logUtils.log('使用色卡I (9:16)模板');
        break;
      default:
        finalTemplateId = 7; // 默认使用色卡A
        logUtils.log('使用默认色卡A (1:1)模板');
    }

    // 跳转到预览页面
    try {
      const url = `/pages/preview/preview?colors=${encodeURIComponent(colorsJson)}&imagePath=${encodeURIComponent(finalImagePath)}&templateId=${finalTemplateId}`;
      logUtils.log('跳转URL', url);

      // 使用redirectTo而不是navigateTo，避免页面栈过深
      wx.redirectTo({
        url: url,
        fail: (err) => {
          logUtils.error('页面跳转失败', err);
          // 如果redirectTo失败，尝试使用navigateTo
          wx.navigateTo({
            url: url,
            fail: (navErr) => {
              logUtils.error('navigateTo也失败了', navErr);
              wx.showToast({
                title: '页面跳转失败',
                icon: 'none'
              });
            }
          });
        }
      });
    } catch (error) {
      logUtils.error('跳转过程出错', error);
    }
  }
})
