<!--pages/preview/preview.wxml-->
<view class="page-wrapper">
  <view class="scroll-area" style="padding-top: {{scrollPaddingTop || '0'}}">
    <view class="card-container">
      <view class="color-card">
        <!-- 加载状态指示器 - 当色卡正在生成时显示 -->
        <view wx:if="{{!cardPath && imagePath}}" class="loading-container">
          <view class="loading-spinner"></view>
          <text class="loading-text">制作中...</text>
        </view>

        <!-- 直接显示生成的配色卡图片 -->
        <image
          wx:if="{{cardPath}}"
          class="card-image"
          src="{{cardPath}}"
          mode="widthFix"
          bindtap="previewImage"
        ></image>
      </view>

    <!-- 使用wx:if和wx:elif确保只渲染一个组件 -->
    <!-- 自定义色卡模板 -->
    <block wx:if="{{isCustom && colors && colors.length > 0}}">
      <!-- P02样式自定义色卡（原有的） -->
      <view wx:if="{{templateId == 7}}" id="cardContainer">
        <color-card-custom
          wx:if="{{colors.length > 0 && pageReady}}"
          colors="{{colors}}"
          title="{{customTitle}}"
          topBackgroundColor="{{customTopBackgroundColor}}"
          bottomBackgroundColor="{{customBottomBackgroundColor}}"
          fontColor="{{customFontColor}}"
          bindgenerated="onCardGenerated"
          style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden;"
        ></color-card-custom>
      </view>

      <!-- P01样式自定义色卡（新增的） -->
      <view wx:elif="{{templateId == 8}}" id="cardContainer">
        <color-card-custom-p01
          wx:if="{{colors.length > 0 && pageReady}}"
          colors="{{colors}}"
          title="{{customTitle}}"
          backgroundColor="{{customBackgroundColor}}"
          titleColor="{{customTitleColor}}"
          bindgenerated="onCardGenerated"
          style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden;"
        ></color-card-custom-p01>
      </view>

      <!-- P03样式自定义色卡（落日漫旅） -->
      <view wx:elif="{{templateId == 16}}" id="cardContainer">
        <color-card-custom-p03
          wx:if="{{colors.length > 0 && pageReady}}"
          colors="{{colors}}"
          title="{{customTitle}}"
          topBackgroundColor="{{customTopBackgroundColor}}"
          bottomBackgroundColor="{{customBottomBackgroundColor}}"
          fontColor="{{customFontColor}}"
          codeColor="{{customCodeColor}}"
          bindgenerated="onCardGenerated"
          style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden;"
        ></color-card-custom-p03>
      </view>

      <!-- P04样式自定义色卡（春日樱语） -->
      <view wx:elif="{{templateId == 17}}" id="cardContainer">
        <color-card-custom-p04
          wx:if="{{colors.length > 0 && pageReady}}"
          colors="{{colors}}"
          title="{{customTitle}}"
          topBackgroundColor="{{customTopBackgroundColor}}"
          bottomBackgroundColor="{{customBottomBackgroundColor}}"
          fontColor="{{customFontColor}}"
          bindgenerated="onCardGenerated"
          style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden;"
        ></color-card-custom-p04>
      </view>
    </block>

    <!-- 图片主题色模板 -->
    <block wx:elif="{{!isCustom && imagePath && colors && colors.length > 0}}">
      <!-- 使用延迟加载，避免组件初始化问题 -->
      <view wx:if="{{templateId == 7}}" id="cardContainer">
        <color-card-a
          wx:if="{{colors.length > 0 && pageReady}}"
          colors="{{colors}}"
          imagePath="{{imagePath}}"
          bindgenerated="onCardGenerated"
          style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden;"
        ></color-card-a>
      </view>

      <!-- 色卡B (1:1)模板 -->
      <view wx:elif="{{templateId == 16}}" id="cardContainer">
        <color-card-b
          wx:if="{{colors.length > 0 && pageReady}}"
          colors="{{colors}}"
          imagePath="{{imagePath}}"
          bindgenerated="onCardGenerated"
          style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden;"
        ></color-card-b>
      </view>

      <!-- 色卡C (3:4)模板 -->
      <view wx:elif="{{templateId == 9}}" id="cardContainer">
        <color-card-c
          wx:if="{{colors.length > 0 && pageReady}}"
          colors="{{colors}}"
          imagePath="{{imagePath}}"
          bindgenerated="onCardGenerated"
          style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden;"
        ></color-card-c>
      </view>

      <!-- 色卡D (3:4)模板 -->
      <view wx:elif="{{templateId == 10}}" id="cardContainer">
        <color-card-d
          wx:if="{{colors.length > 0 && pageReady}}"
          colors="{{colors}}"
          imagePath="{{imagePath}}"
          bindgenerated="onCardGenerated"
          style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden;"
        ></color-card-d>
      </view>

      <!-- 色卡E (3:4)模板 -->
      <view wx:elif="{{templateId == 11}}" id="cardContainer">
        <color-card-e
          wx:if="{{colors.length > 0 && pageReady}}"
          colors="{{colors}}"
          imagePath="{{imagePath}}"
          bindgenerated="onCardGenerated"
          style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden;"
        ></color-card-e>
      </view>

      <!-- 色卡F (16:9)模板 -->
      <view wx:elif="{{templateId == 12}}" id="cardContainer">
        <color-card-f
          wx:if="{{colors.length > 0 && pageReady}}"
          colors="{{colors}}"
          imagePath="{{imagePath}}"
          bindgenerated="onCardGenerated"
          style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden;"
        ></color-card-f>
      </view>

      <!-- 色卡G (4:5)模板 -->
      <view wx:elif="{{templateId == 13}}" id="cardContainer">
        <color-card-g
          wx:if="{{colors.length > 0 && pageReady}}"
          colors="{{colors}}"
          imagePath="{{imagePath}}"
          bindgenerated="onCardGenerated"
          style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden;"
        ></color-card-g>
      </view>

      <!-- 色卡H (9:16)模板 -->
      <view wx:elif="{{templateId == 14}}" id="cardContainer">
        <color-card-h
          wx:if="{{colors.length > 0 && pageReady}}"
          colors="{{colors}}"
          imagePath="{{imagePath}}"
          bindgenerated="onCardGenerated"
          style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden;"
        ></color-card-h>
      </view>

      <!-- 色卡I (9:16)模板 -->
      <view wx:elif="{{templateId == 15}}" id="cardContainer">
        <color-card-i
          wx:if="{{colors.length > 0 && pageReady}}"
          colors="{{colors}}"
          imagePath="{{imagePath}}"
          bindgenerated="onCardGenerated"
          style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden;"
        ></color-card-i>
      </view>
    </block>
  </view>

    <!-- 色卡信息提示 -->
    <view class="card-info" wx:if="{{cardPath}}">
      <view class="info-text-simple">点击图片可查看大图</view>
    </view>
  </view>

  <view class="btn-container">
    <button class="save-btn" bindtap="saveToAlbum">保存到相册</button>
    <button class="home-btn" bindtap="goToHome">返回首页</button>
  </view>

  <!-- 保存成功提示 -->
  <view class="save-success {{showSaveSuccess ? 'show' : ''}}">
    <view class="success-icon"></view>
    <view class="success-text">色卡已保存到相册</view>
  </view>
</view>
