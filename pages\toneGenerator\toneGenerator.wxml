<!--pages/toneGenerator/toneGenerator.wxml-->
<view class="container">
  <!-- 顶部颜色选择区域 -->
  <view class="section input-section">
    <view class="section-title">输入颜色</view>
    <view class="base-color-container">
      <!-- 左侧颜色色块，在中央显示16进制色值 -->
      <view class="color-preview" style="background-color: {{baseColor}};" bindtap="showColorPicker">
        <view class="color-hex-value" style="color: {{textColor}};">{{baseColor}}</view>
      </view>
      <!-- 右侧上下并列的按钮 -->
      <view class="color-actions-column">
        <view class="btn-wrapper">
          <view class="custom-btn random-btn" bindtap="generateRandomColor">随机颜色</view>
        </view>
        <view class="btn-wrapper">
          <view class="custom-btn picker-btn" bindtap="showColorPicker">选择颜色</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 色调展示区域 -->
  <view class="section result-section">
    <view class="section-title">浅色与阴影</view>
    <view class="result-intro">点击任意色块可复制对应的颜色值</view>
    <view class="tones-container">
      <view class="tone-item" wx:for="{{tones}}" wx:key="level" bindtap="copyColor" data-color="{{item.color}}">
        <view class="tone-level">{{item.level}}</view>
        <view class="tone-color" style="background-color: {{item.color}}">
          <view class="tone-color-code" style="color: {{item.textColor}};">{{item.color}}</view>
        </view>
      </view>
    </view>
  </view>



  <!-- 颜色选择器弹窗 -->
  <view class="color-picker-modal" wx:if="{{showColorPicker}}">
    <view class="color-picker-container">
      <view class="color-picker-header">
        <view class="color-picker-title">选择基础颜色</view>
        <view class="color-picker-close" bindtap="hideColorPicker">×</view>
      </view>

      <!-- 集成公共颜色选择器组件 -->
      <color-picker
        color="{{customColor}}"
        bindchange="onColorPickerChange"
        bindconfirm="onColorPickerConfirm"
        bindcancel="hideColorPicker"
      ></color-picker>
    </view>
  </view>
</view>
