<!--pages/ambientLight/ambientLight.wxml-->
<view class="page-wrapper full-screen" style="background-color: {{currentColor}}; opacity: {{brightness/100}};" bindtap="onScreenTap" id="pageWrapper">
  <!-- 摄像头组件 -->
  <view class="camera-container" wx:if="{{showCamera}}" catchtap="preventTap">
    <camera
      id="myCamera"
      device-position="{{devicePosition}}"
      flash="{{cameraFlash}}"
      resolution="{{cameraResolution}}"
      frame-size="{{cameraFrameSize}}"
      binderror="onCameraError"
      bindinitdone="onCameraInitDone"
      class="camera-view"
      poster="/assets/images/camera-poster.png"
      poster-for-crawler="/assets/images/camera-poster.png">
    </camera>
    <!-- 相机加载中提示 -->
    <view class="camera-loading-mask" wx:if="{{showCamera && !cameraContext}}">
      <view class="camera-loading-text">相机加载中...</view>
    </view>
  </view>

  <!-- 顶部导航栏 -->
  <view class="custom-nav-container">
    <!-- 状态栏占位 -->
    <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
    <!-- 导航栏内容 -->
    <view class="custom-nav">
      <view class="nav-left">
        <view class="nav-back-btn" catch:tap="navigateBack">
          <view class="nav-back-arrow"></view>
        </view>
      </view>
      <view class="nav-center">
        <text class="nav-title">补光灯</text>
      </view>
      <view class="nav-right"></view>
    </view>
  </view>

  <!-- 颜色描述 -->
  <view class="color-description">
    <view class="description-text">{{colorDescription}}</view>
  </view>

  <!-- 控制面板 -->
  <view class="controls-panel {{showControls ? 'show' : 'hide'}}" catchtap="preventTap">
    <!-- 标题 -->
    <view class="panel-title">
      <text>灯光颜色调节</text>
    </view>

    <!-- 预设颜色选择 -->
    <view class="preset-colors-section">
      <view class="preset-colors-grid">
        <view
          wx:for="{{presetColors}}"
          wx:key="color"
          class="preset-color-item"
          style="background-color: {{item.color}};"
          bindtap="selectPresetColor"
          data-color="{{item.color}}"
          data-text-color="{{item.textColor}}"
          data-description="{{item.description}}"
          data-name="{{item.name}}"
        >
          <view class="preset-color-name">{{item.name}}</view>
        </view>
      </view>
    </view>

    <!-- 共用的颜色调节滑块 -->
    <view class="color-sliders-section">
      <!-- 色相滑块 -->
      <view class="color-slider-row">
        <view class="color-slider-label">色相</view>
        <view class="color-slider-container"
              bindtouchstart="onHueSliderStart"
              bindtouchmove="onHueSliderMove"
              bindtouchend="onHueSliderEnd"
              data-slider-type="hue">
          <view class="slider-track hue-track"></view>
          <view class="slider-handle {{sliderTouching && currentSliderType === 'hue' ? 'active' : ''}}" style="left: calc({{hue / 360 * 100}}% - 16rpx);"></view>
          <slider
            class="hidden-slider"
            min="0"
            max="360"
            value="{{hue}}"
            bindchange="onHueChange"
            bindchanging="onHueChange"
            step="1"
          />
        </view>
        <view class="slider-value">{{hue}}°</view>
      </view>

      <!-- 饱和度滑块 -->
      <view class="color-slider-row">
        <view class="color-slider-label">饱和度</view>
        <view class="color-slider-container"
              bindtouchstart="onSaturationSliderStart"
              bindtouchmove="onSaturationSliderMove"
              bindtouchend="onSaturationSliderEnd"
              data-slider-type="saturation">
          <view class="slider-track saturation-track" style="background: linear-gradient(to right, #FFFFFF, {{currentHueColor}});"></view>
          <view class="slider-handle {{sliderTouching && currentSliderType === 'saturation' ? 'active' : ''}}" style="left: calc({{saturation}}% - 16rpx);"></view>
          <slider
            class="hidden-slider"
            min="0"
            max="100"
            value="{{saturation}}"
            bindchange="onSaturationChange"
            bindchanging="onSaturationChange"
            step="1"
          />
        </view>
        <view class="slider-value">{{saturation}}%</view>
      </view>

      <!-- 明度滑块 -->
      <view class="color-slider-row">
        <view class="color-slider-label">明度</view>
        <view class="color-slider-container"
              bindtouchstart="onLightnessSliderStart"
              bindtouchmove="onLightnessSliderMove"
              bindtouchend="onLightnessSliderEnd"
              data-slider-type="lightness">
          <view class="slider-track lightness-track"></view>
          <view class="slider-handle {{sliderTouching && currentSliderType === 'lightness' ? 'active' : ''}}" style="left: calc({{lightness}}% - 16rpx);"></view>
          <slider
            class="hidden-slider"
            min="0"
            max="100"
            value="{{lightness}}"
            bindchange="onLightnessChange"
            bindchanging="onLightnessChange"
            step="1"
          />
        </view>
        <view class="slider-value">{{lightness}}%</view>
      </view>

      <!-- 屏幕亮度滑块 -->
      <view class="color-slider-row">
        <view class="color-slider-label">屏幕亮度</view>
        <view class="color-slider-container"
              bindtouchstart="onBrightnessSliderStart"
              bindtouchmove="onBrightnessSliderMove"
              bindtouchend="onBrightnessSliderEnd"
              data-slider-type="brightness">
          <view class="slider-track brightness-track"></view>
          <view class="slider-handle {{sliderTouching && currentSliderType === 'brightness' ? 'active' : ''}}" style="left: calc({{(screenBrightness - 10) / 90 * 100}}% - 16rpx);"></view>
          <slider
            class="hidden-slider"
            min="10"
            max="100"
            value="{{screenBrightness}}"
            bindchange="onScreenBrightnessChange"
            bindchanging="onScreenBrightnessChange"
            step="1"
          />
        </view>
        <view class="slider-value">{{screenBrightness}}%</view>
      </view>
    </view>
  </view>

  <!-- 相机入口按钮 -->
  <view class="camera-entry-button" bindtap="toggleCamera">
    <view class="camera-icon"></view>
  </view>

  <!-- 相机功能按钮 -->
  <view class="camera-function-buttons" wx:if="{{showCamera}}">
    <view class="camera-function-button" catch:tap="switchCamera">
      <view class="camera-switch-icon"></view>
      <text>切换</text>
    </view>
    <view class="camera-function-button" catch:tap="toggleCameraFlash" wx:if="{{devicePosition === 'back'}}">
      <view class="camera-flash-icon {{cameraFlash === 'on' ? 'flash-on' : 'flash-off'}}"></view>
      <text>闪光灯</text>
    </view>
  </view>

  <!-- 拍照按钮 -->
  <view class="shutter-button" wx:if="{{showCamera}}" catch:tap="takePhoto">
    <view class="shutter-button-inner"></view>
  </view>

  <!-- 相机设置按钮 -->
  <view class="camera-settings-button" wx:if="{{showCamera}}" catch:tap="toggleCameraSettings">
    <view class="camera-settings-icon"></view>
  </view>

  <!-- 相机设置弹窗 -->
  <view class="camera-settings-modal" wx:if="{{showCameraSettings}}" catchtap="closeCameraSettings">
    <view class="camera-settings-container" catchtap="preventTap">
      <view class="camera-settings-header">
        <view class="camera-settings-title">相机设置</view>
        <view class="camera-settings-close" bindtap="closeCameraSettings">×</view>
      </view>
      <view class="camera-settings-content">
        <!-- 分辨率设置 -->
        <view class="camera-setting-item">
          <view class="setting-label">分辨率</view>
          <view class="setting-options">
            <view
              class="setting-option {{cameraResolution === 'low' ? 'selected' : ''}}"
              data-value="low"
              bindtap="setCameraResolution"
            >低</view>
            <view
              class="setting-option {{cameraResolution === 'medium' ? 'selected' : ''}}"
              data-value="medium"
              bindtap="setCameraResolution"
            >中</view>
            <view
              class="setting-option {{cameraResolution === 'high' ? 'selected' : ''}}"
              data-value="high"
              bindtap="setCameraResolution"
            >高</view>
          </view>
        </view>

        <!-- 帧大小设置 -->
        <view class="camera-setting-item">
          <view class="setting-label">帧大小</view>
          <view class="setting-options">
            <view
              class="setting-option {{cameraFrameSize === 'small' ? 'selected' : ''}}"
              data-value="small"
              bindtap="setCameraFrameSize"
            >小</view>
            <view
              class="setting-option {{cameraFrameSize === 'medium' ? 'selected' : ''}}"
              data-value="medium"
              bindtap="setCameraFrameSize"
            >中</view>
            <view
              class="setting-option {{cameraFrameSize === 'large' ? 'selected' : ''}}"
              data-value="large"
              bindtap="setCameraFrameSize"
            >大</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 颜色选择器弹窗 -->
  <view class="color-picker-modal" wx:if="{{showColorPicker}}" catchtap="preventTap">
    <view class="color-picker-container">
      <view class="color-picker-header">
        <view class="color-picker-title">灯光颜色调节</view>
        <view class="color-picker-close" bindtap="hideColorPicker">×</view>
      </view>
      <!-- 集成颜色选择器组件 -->
      <color-picker
        color="{{currentColor}}"
        bindchange="onColorPickerChange"
        bindconfirm="onColorPickerConfirm"
        bindcancel="hideColorPicker"
      ></color-picker>
    </view>
  </view>

  <!-- 底部提示文本 -->
  <view class="bottom-tip-text" wx:if="{{!showControls}}">
    点击屏幕空白处可调整屏幕颜色
  </view>
</view>
