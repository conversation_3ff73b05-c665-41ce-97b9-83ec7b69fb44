// components/color-card-custom/color-card-custom.js - 自定义色卡组件
const colorUtils = require('../../../utils/colorUtils');

Component({
  properties: {
    colors: {
      type: Array,
      value: []
    },
    title: {
      type: String,
      value: '蜜桃汽水'
    },
    topBackgroundColor: {
      type: String,
      value: '#FFF2EB'
    },
    bottomBackgroundColor: {
      type: String,
      value: '#FFFFFF'
    },
    fontColor: {
      type: String,
      value: '#EB898E'
    }
  },

  data: {
    canvasWidth: 800, // 按照p02.html的比例，使用1:1.33的比例
    canvasHeight: 1066 // 保持与原设计的比例 (1600:2132 = 800:1066)
  },

  lifetimes: {
    attached() {
      this.drawColorCard();
    }
  },

  methods: {
    async drawColorCard() {
      const { canvasWidth, canvasHeight } = this.data;
      const { colors, title, topBackgroundColor, bottomBackgroundColor, fontColor } = this.properties;

      if (!colors || colors.length === 0) return;

      try {
        // 获取Canvas实例
        const query = wx.createSelectorQuery().in(this);
        const canvas = await new Promise((resolve) => {
          query.select('#customColorCardCanvas')
            .fields({ node: true, size: true })
            .exec((res) => {
              resolve(res[0]);
            });
        });

        if (!canvas || !canvas.node) {
          this.triggerEvent('generated', { path: '' });
          return;
        }

        const ctx = canvas.node.getContext('2d');
        // 不使用设备像素比，避免内存过大
        const dpr = 1;

        // 设置Canvas尺寸
        canvas.node.width = canvasWidth;
        canvas.node.height = canvasHeight;
        // 不进行缩放，使用原始尺寸

        // 按照p02.html设计绘制背景
        // 上半部分背景区域 (对应原设计的1052px高度，按比例缩放)
        const topBgHeight = Math.floor(1052 * canvasHeight / 2132);
        ctx.fillStyle = topBackgroundColor || '#FEF2F4';
        ctx.fillRect(0, 0, canvasWidth, topBgHeight);

        // 下半部分背景区域
        ctx.fillStyle = bottomBackgroundColor || '#FFFFFF';
        ctx.fillRect(0, topBgHeight, canvasWidth, canvasHeight - topBgHeight);

        console.log('Canvas尺寸:', canvasWidth, 'x', canvasHeight);
        console.log('上半段背景高度:', topBgHeight);
        console.log('颜色数据:', colors);

        // 简化绘制流程
        this.drawTitle(ctx, title || '蜜桃汽水', fontColor);
        this.drawColorBlocks(ctx, colors, fontColor);

        // 保存Canvas - 增加延迟确保绘制完成
        setTimeout(() => {
          this.saveCanvas(canvas.node);
        }, 500);

      } catch (err) {
        console.error('绘制自定义色卡失败:', err);
        this.triggerEvent('generated', { path: '' });
      }
    },

    // 绘制标题 - 按照p02.html的设计
    drawTitle(ctx, title, fontColor) {
      const { canvasWidth, canvasHeight } = this.data;

      // 按照原设计比例计算位置和大小
      // 原设计：标题位置 left:500px, top:421px, 字体150px
      const titleX = canvasWidth / 2; // 居中显示
      const titleY = Math.floor(421 * canvasHeight / 2132); // 按比例计算Y位置
      const fontSize = Math.floor(150 * canvasWidth / 1600); // 按比例计算字体大小

      // 设置标题样式 - 使用动态字体颜色
      ctx.fillStyle = fontColor || '#EB898E';
      ctx.font = `bold ${fontSize}px PingFang SC, Arial, sans-serif`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      // 绘制标题
      ctx.fillText(title, titleX, titleY);
    },

    // 绘制颜色块 - 按照p02.html的设计
    drawColorBlocks(ctx, colors, fontColor) {
      const { canvasWidth, canvasHeight } = this.data;

      // 按照原设计比例计算大圆形色块 - 支持可变数量
      // 原设计：4个圆形，直径420px，起始位置left:89px, top:837px
      const colorCount = colors.length;
      const circleSize = Math.floor(420 * canvasWidth / 1600); // 按比例计算圆形大小
      const circleY = Math.floor(837 * canvasHeight / 2132); // 按比例计算Y位置

      // 计算圆形的间距，根据颜色数量动态调整，但设置最大间距限制
      const totalAvailableWidth = canvasWidth - 2 * Math.floor(89 * canvasWidth / 1600); // 减去左右边距
      const maxGap = Math.floor(100 * canvasWidth / 1600); // 设置最大间距，按比例缩放
      let circleGap = colorCount > 1 ? (totalAvailableWidth - colorCount * circleSize) / (colorCount - 1) : 0; // 动态计算间距
      circleGap = Math.min(circleGap, maxGap); // 限制最大间距
      const totalCircleWidth = colorCount * circleSize + (colorCount - 1) * circleGap;
      const circleStartX = (canvasWidth - totalCircleWidth) / 2; // 居中显示

      // 绘制大圆形颜色块 - 支持可变数量
      for (let index = 0; index < colors.length; index++) {
        const color = colors[index];
        const x = circleStartX + index * (circleSize + circleGap);

        // 绘制圆形颜色块
        ctx.fillStyle = color;
        ctx.beginPath();
        ctx.arc(x + circleSize / 2, circleY + circleSize / 2, circleSize / 2, 0, 2 * Math.PI);
        ctx.fill();

        // 不绘制边框，保持原设计的纯色圆形
      }

      // 绘制颜色代码 - 按照原设计位置
      // 原设计：颜色代码位置 top:1337px，字体42px，使用动态字体颜色
      const codeY = Math.floor(1337 * canvasHeight / 2132);
      const codeFontSize = Math.floor(42 * canvasWidth / 1600);
      ctx.fillStyle = fontColor || '#EB898E';
      ctx.font = `bold ${codeFontSize}px PingFang SC, Arial, sans-serif`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      for (let index = 0; index < colors.length; index++) {
        const color = colors[index];
        const x = circleStartX + index * (circleSize + circleGap) + circleSize / 2;
        ctx.fillText(color, x, codeY);
      }

      // 绘制小矩形色块预览 - 紧挨着贴合在一起，支持可变数量
      // 原设计：小矩形位置 top:1654px，尺寸146x146px，紧挨着排列
      const smallBlockY = Math.floor(1654 * canvasHeight / 2132);
      const smallBlockSize = Math.floor(146 * canvasWidth / 1600);
      const smallBlockGap = 0; // 紧挨着贴合，无间距
      const totalSmallWidth = colors.length * smallBlockSize; // 根据颜色数量计算总宽度
      const smallStartX = (canvasWidth - totalSmallWidth) / 2; // 居中显示

      for (let index = 0; index < colors.length; index++) {
        const color = colors[index];
        const x = smallStartX + index * smallBlockSize; // 紧挨着排列，无间距

        // 绘制矩形色块
        ctx.fillStyle = color;
        ctx.fillRect(x, smallBlockY, smallBlockSize, smallBlockSize);

        // 不绘制边框，保持原设计的纯色矩形
      }
    },

    // 保存Canvas
    async saveCanvas(canvasNode) {
      try {
        const { canvasWidth, canvasHeight } = this.data;

        const res = await new Promise((resolve, reject) => {
          wx.canvasToTempFilePath({
            canvas: canvasNode,
            x: 0,
            y: 0,
            width: canvasWidth,
            height: canvasHeight,
            destWidth: canvasWidth,
            destHeight: canvasHeight,
            fileType: 'png', // 使用PNG保持更好的质量
            quality: 1, // 最高质量
            success: resolve,
            fail: reject
          }, this);
        });

        // Canvas保存成功
        this.triggerEvent('generated', { path: res.tempFilePath });
      } catch (err) {
        console.error('保存自定义色卡失败:', err);
        this.triggerEvent('generated', { path: '' });
      }
    }
  }
});
