{"description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "projectname": "KALA", "setting": {"compileHotReLoad": true, "skylineRenderEnable": false, "urlCheck": true, "coverView": true, "lazyloadPlaceholderEnable": true, "preloadBackgroundData": false, "autoAudits": false, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "checkInvalidKey": true, "ignoreDevUnusedFiles": true, "bigPackageSizeSupport": false}, "libVersion": "3.0.2", "packOptions": {"ignore": [{"value": "PP_DISABLED", "type": "folder"}, {"value": "*.html", "type": "file"}, {"value": "*.htm", "type": "file"}], "include": []}, "condition": {}}