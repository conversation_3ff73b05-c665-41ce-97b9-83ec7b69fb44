<!--pages/about/about.wxml-->
<view class="page">
  <view class="container">
    <!-- 作者的话 -->
    <view class="section">
      <view class="section-title">作者的话</view>
      <view class="section-content">
        <text class="description">{{appInfo.authorMessage}}</text>
      </view>
    </view>

    <!-- 去广告按钮 -->
    <button class="ad-free-button" bindtap="showAdFreeModal">
      <view class="ad-free-button-inner">
        <text>{{adFreeStatus ? '已激活去广告' : '去广告'}}</text>
      </view>
    </button>

    <!-- 联系作者按钮 -->
    <button class="contact-button" open-type="contact" show-message-card="true">
      <view class="contact-button-inner">
        <text>联系作者</text>
      </view>
    </button>
  </view>

  <!-- 去广告激活码弹窗 -->
  <view class="ad-free-modal" wx:if="{{showAdFreeModal}}">
    <view class="ad-free-container">
      <view class="ad-free-header">
        <view class="ad-free-title">{{adFreeStatus ? '去广告状态' : '输入验证码'}}</view>
        <view class="ad-free-close" bindtap="hideAdFreeModal">×</view>
      </view>

      <view class="ad-free-content" wx:if="{{!adFreeStatus}}">
        <view class="ad-free-description">
          <text>输入验证码可以移除色卡制作和肤色自测的视频广告，如需激活码可点击页面的联系作者按钮联系作者。</text>
        </view>
        <view class="ad-free-input-container">
          <input
            class="ad-free-input"
            placeholder="请输入验证码"
            value="{{verificationCode}}"
            bindinput="onVerificationCodeInput"
            maxlength="20"
          />
        </view>
      </view>

      <view class="ad-free-content" wx:if="{{adFreeStatus}}">
        <view class="ad-free-success">
          <view class="success-icon">✓</view>
          <view class="success-text">去广告功能已激活</view>
          <view class="success-description">色卡制作和肤色自测将不再显示视频广告</view>
        </view>
      </view>

      <view class="ad-free-footer" wx:if="{{!adFreeStatus}}">
        <view class="ad-free-cancel" bindtap="hideAdFreeModal">取消</view>
        <view class="ad-free-confirm" bindtap="activateAdFree">验证</view>
      </view>

      <view class="ad-free-footer" wx:if="{{adFreeStatus}}">
        <view class="ad-free-deactivate" bindtap="deactivateAdFree">取消激活</view>
        <view class="ad-free-ok" bindtap="hideAdFreeModal">确定</view>
      </view>
    </view>
  </view>
</view>
