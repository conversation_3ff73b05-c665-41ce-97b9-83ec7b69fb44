/* pages/colorDetail/colorDetail.wxss */
.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  padding: 0 20rpx; /* 减小左右内边距 */
}

/* 颜色信息区域样式 */
.color-info-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-bottom: 200rpx; /* 为底部工具栏留出空间 */
}

/* 颜色名称样式 */
.color-name {
  font-size: 60rpx; /* 进一步减小字体大小 */
  font-weight: 700; /* 使用粗体 */
  color: var(--text-color, #ffffff);
  margin-bottom: 30rpx; /* 减小底部间距，为颜色介绍留出空间 */
  font-family: 'Helvetica Neue', Arial, sans-serif;
  letter-spacing: 2px; /* 增加字母间距 */
  padding-left: 20rpx; /* 进一步减小左侧缩进 */
}

/* 颜色介绍样式 */
.color-intro {
  font-size: 28rpx;
  color: var(--text-color, #ffffff);
  opacity: 0.9;
  margin-bottom: 50rpx;
  padding-left: 20rpx;
  padding-right: 20rpx;
  line-height: 1.5;
  font-family: 'Helvetica Neue', Arial, sans-serif;
  font-weight: 400;
  text-align: justify;
}

/* 颜色值列表样式 */
.color-values {
  display: flex;
  flex-direction: column;
  margin-top: 40rpx; /* 减小与颜色名称之间的距离 */
  padding-left: 20rpx; /* 进一步减小左侧缩进，与颜色名称对齐 */
  margin-bottom: 20rpx; /* 增加底部间距 */
}

/* 最后一个颜色值项的额外间距 */
.color-value-item:last-child {
  margin-bottom: 50rpx; /* 为底部工具栏留出更多空间 */
}

.color-value-item {
  display: flex;
  align-items: center;
  margin-bottom: 35rpx; /* 增加行间距 */
  padding-left: 0; /* 移除左侧缩进，确保与颜色名称左对齐 */
}

.color-text {
  font-size: 28rpx; /* 进一步减小字体大小 */
  color: var(--text-color, #ffffff);
  font-family: 'Helvetica Neue', Arial, sans-serif;
  font-weight: 400; /* 使用常规体 */
  letter-spacing: 1px; /* 减少字母间距，使其更紧凑 */
  line-height: 1.5; /* 增加行高，使文字更加舒适 */
}

/* 确保冒号后有适当的间距 */
.color-text::after {
  content: "";
  display: inline-block;
  width: 8rpx; /* 冒号后的间距 */
}

/* 底部工具栏样式 */
.footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  display: flex;
  justify-content: center; /* 居中显示页码指示器 */
  align-items: center;
  padding: 0 40rpx;
  padding-bottom: 40rpx; /* 为底部安全区域留出空间 */
}

.page-indicator {
  font-size: 28rpx; /* 减小字体大小 */
  color: var(--text-color, #ffffff);
  font-family: 'Helvetica Neue', Arial, sans-serif;
  font-weight: 300;
  letter-spacing: 1px;
}

/* 滑动提示样式 */
.swipe-tip {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 24rpx; /* 稍微增大字体大小，使其更加醒目 */
  color: var(--text-color, #ffffff);
  opacity: 0.8; /* 增加不透明度，使其更加醒目 */
  padding: 20rpx 0;
  font-family: 'Helvetica Neue', Arial, sans-serif;
  font-weight: 400; /* 使用常规体，增加可读性 */
  letter-spacing: 1px;
}

.swipe-tip-top {
  top: 120rpx;
}

.swipe-tip-bottom {
  bottom: 160rpx;
}

/* 滑动提示动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 0.7; }
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}
