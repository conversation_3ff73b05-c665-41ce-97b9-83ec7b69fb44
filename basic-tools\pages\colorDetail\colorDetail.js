// pages/colorDetail/colorDetail.js
// 导入颜色工具模块
const colorUtils = require('../../utils/colorUtils');
Page({
  data: {
    colorId: null,
    colorName: '',
    hexValue: '',
    rgbDisplay: '',  // 显示用的RGB值
    hsbDisplay: '',  // 显示用的HSB值
    labDisplay: '',  // 显示用的LAB值
    cmykDisplay: '', // 显示用的CMYK值
    colorIntro: '',  // 颜色介绍
    currentIndex: 0, // 当前颜色索引
    totalColors: 5,  // 颜色总数
    relatedColors: [],
    textColor: '#ffffff', // 文字颜色，默认白色
    touchStartY: 0, // 触摸开始位置
    showPrevTip: false, // 显示上一个颜色提示
    showNextTip: false, // 显示下一个颜色提示
    categoryId: null, // 当前分类ID
    colors: [] // 当前分类的所有颜色
  },

  onLoad: function (options) {
    // 获取传递的颜色数据
    const { id, name, hex, rgb, cmyk, categoryId, colorsData, intro } = options;

    // 解析颜色列表数据
    let colors = [];
    if (colorsData) {
      try {
        colors = JSON.parse(decodeURIComponent(colorsData));
      } catch (e) {
        console.error('解析颜色数据失败:', e);
      }
    }

    // 解码颜色值
    const hexValue = decodeURIComponent(hex);
    const rgbValue = decodeURIComponent(rgb);
    const cmykValue = cmyk ? decodeURIComponent(cmyk) : '';

    // 解析RGB值
    let r, g, b;
    if (rgbValue) {
      const rgbMatch = rgbValue.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
      if (rgbMatch) {
        r = parseInt(rgbMatch[1]);
        g = parseInt(rgbMatch[2]);
        b = parseInt(rgbMatch[3]);
      }
    }

    // 如果没有RGB值，从HEX值解析
    if (!r && hexValue) {
      r = parseInt(hexValue.slice(1, 3), 16);
      g = parseInt(hexValue.slice(3, 5), 16);
      b = parseInt(hexValue.slice(5, 7), 16);
    }

    // 计算HSB值
    const hsb = this.rgbToHsb(r, g, b);
    const hsbDisplay = `${Math.round(hsb.h)}, ${Math.round(hsb.s * 100)}%, ${Math.round(hsb.b * 100)}%`;

    // 计算LAB值
    const lab = this.rgbToLab(r, g, b);
    const labDisplay = `${Math.round(lab.l)}, ${Math.round(lab.a)}, ${Math.round(lab.b)}`;

    // 格式化RGB值显示
    const rgbDisplay = `${r}, ${g}, ${b}`;

    // 格式化CMYK值显示
    let cmykDisplay = '';
    if (cmykValue) {
      // 尝试匹配 cmyk(x,y,z,w) 格式
      const cmykMatch = cmykValue.match(/cmyk\((\d+),\s*(\d+),\s*(\d+),\s*(\d+)\)/);
      if (cmykMatch) {
        cmykDisplay = `${cmykMatch[1]}, ${cmykMatch[2]}, ${cmykMatch[3]}, ${cmykMatch[4]}`;
      }
      // 尝试匹配简单的 x,y,z,w 格式
      else if (cmykValue.match(/^\d+,\s*\d+,\s*\d+,\s*\d+$/)) {
        cmykDisplay = cmykValue;
      }
      // 如果都不匹配，使用原始值
      else {
        cmykDisplay = cmykValue;
      }
    } else {
      // 如果没有CMYK值，从RGB计算
      const cmyk = this.rgbToCmyk(r, g, b);
      cmykDisplay = `${Math.round(cmyk.c)}, ${Math.round(cmyk.m)}, ${Math.round(cmyk.y)}, ${Math.round(cmyk.k)}`;
    }

    // 获取颜色索引和总数
    const index = options.index ? parseInt(options.index) : 0;
    const total = options.total ? parseInt(options.total) : 5;

    // 判断颜色亮度，决定文字颜色
    const isLightColor = this.isLightColor(r, g, b);
    const textColor = isLightColor ? '#000000' : '#ffffff';

    // 设置导航栏样式
    wx.setNavigationBarColor({
      frontColor: isLightColor ? '#000000' : '#ffffff',
      backgroundColor: hexValue,
      animation: {
        duration: 300,
        timingFunc: 'easeIn'
      }
    });

    // 设置导航栏标题，只显示颜色名称
    wx.setNavigationBarTitle({
      title: name
    });

    this.setData({
      colorId: parseInt(id),
      colorName: name,
      hexValue: hexValue,
      rgbDisplay: rgbDisplay,
      hsbDisplay: hsbDisplay,
      labDisplay: labDisplay,
      cmykDisplay: cmykDisplay,
      colorIntro: intro ? decodeURIComponent(intro) : "", // 设置颜色介绍
      currentIndex: index,
      totalColors: total,
      textColor: textColor,
      categoryId: categoryId ? parseInt(categoryId) : null,
      colors: colors
    });

    // 显示滑动提示
    this.showSwipeTips();

    // 生成相关颜色
    this.generateRelatedColors();
  },

  // 用户点击右上角分享或使用分享按钮
  onShareAppMessage: function() {
    return {
      title: this.data.colorName + ' - ' + this.data.hexValue,
      path: '/pages/colorDetail/colorDetail?id=' + this.data.colorId + '&name=' + this.data.colorName + '&hex=' + encodeURIComponent(this.data.hexValue) + '&rgb=' + encodeURIComponent(this.data.rgbDisplay) + '&cmyk=' + encodeURIComponent(this.data.cmykDisplay) + '&categoryId=' + this.data.categoryId + '&intro=' + encodeURIComponent(this.data.colorIntro || ''),
      imageUrl: '/assets/images/share-color-detail.png' // 分享图片
    };
  },

  // 生成相关颜色
  generateRelatedColors: function () {
    const hex = this.data.hexValue;

    // 将十六进制颜色转换为RGB
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);

    // 生成相关颜色
    const relatedColors = [
      {
        name: '浅色版',
        hex: this.lightenColor(r, g, b, 0.3)
      },
      {
        name: '深色版',
        hex: this.darkenColor(r, g, b, 0.3)
      },
      {
        name: '互补色',
        hex: this.complementaryColor(r, g, b)
      },
      {
        name: '类似色1',
        hex: this.analogousColor(r, g, b, 30)
      },
      {
        name: '类似色2',
        hex: this.analogousColor(r, g, b, -30)
      }
    ];

    this.setData({
      relatedColors
    });
  },

  // 复制颜色值到剪贴板
  copyColor: function (e) {
    const type = e.currentTarget.dataset.type;
    let value = '';

    switch (type) {
      case 'hex':
        value = this.data.hexValue;
        break;
      case 'rgb':
        value = this.data.rgbDisplay;
        break;
      case 'hsb':
        value = this.data.hsbDisplay;
        break;
      case 'lab':
        value = this.data.labDisplay;
        break;
      case 'cmyk':
        value = this.data.cmykDisplay;
        break;
      default:
        value = this.data.hexValue;
    }

    wx.setClipboardData({
      data: value,
      success: () => {
        wx.showToast({
          title: '已复制' + type.toUpperCase() + '值',
          icon: 'success',
          duration: 2000
        });
      }
    });
  },

  // 复制相关颜色值
  copyRelatedColor: function (e) {
    const index = e.currentTarget.dataset.index;
    const hex = this.data.relatedColors[index].hex;

    wx.setClipboardData({
      data: hex,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success',
          duration: 2000
        });
      }
    });
  },

  // 颜色处理工具函数
  // 变亮颜色
  lightenColor: function (r, g, b, amount) {
    const hex = this.rgbToHex(r, g, b);
    const hsl = colorUtils.hexToHSL(hex);
    // 增加亮度，但不超过95%
    const newL = Math.min(95, hsl.l + amount * 100);
    return colorUtils.hslToHex(hsl.h, hsl.s, newL);
  },

  // 变暗颜色
  darkenColor: function (r, g, b, amount) {
    const hex = this.rgbToHex(r, g, b);
    const hsl = colorUtils.hexToHSL(hex);
    // 减少亮度，但不低于5%
    const newL = Math.max(5, hsl.l - amount * 100);
    return colorUtils.hslToHex(hsl.h, hsl.s, newL);
  },

  // 互补色
  complementaryColor: function (r, g, b) {
    const hex = this.rgbToHex(r, g, b);
    const hsl = colorUtils.hexToHSL(hex);
    // 互补色的色相相差180度
    const compH = (hsl.h + 180) % 360;
    return colorUtils.hslToHex(compH, hsl.s, hsl.l);
  },

  // 类似色
  analogousColor: function (r, g, b, degrees) {
    // 转换为HEX，然后转换为HSL
    const hex = this.rgbToHex(r, g, b);
    const hsl = colorUtils.hexToHSL(hex);

    // 调整色相
    let newH = (hsl.h + degrees) % 360;
    if (newH < 0) newH += 360;

    // 直接转换为HEX
    return colorUtils.hslToHex(newH, hsl.s, hsl.l);
  },

  // RGB转十六进制
  rgbToHex: function (r, g, b) {
    return colorUtils.rgbToHex(r, g, b);
  },

  // RGB转HSL
  rgbToHsl: function (r, g, b) {
    return colorUtils.rgbToHSL({ r, g, b });
  },

  // HSL转RGB
  hslToRgb: function (h, s, l) {
    return colorUtils.hslToRGB(h, s, l);
  },

  // RGB转HSB (HSV)
  rgbToHsb: function (r, g, b) {
    // 使用colorUtils模块中的HSL转换，然后转换为HSB
    const hsl = colorUtils.rgbToHSL({ r, g, b });

    // HSL到HSB的转换
    const s = hsl.s * (hsl.l < 50 ? hsl.l / 100 : (100 - hsl.l) / 100) * 100;
    const brightness = hsl.l + s * Math.min(hsl.l, 100 - hsl.l) / 100;

    return { h: hsl.h, s: s / 100, b: brightness / 100 };
  },

  // RGB转LAB
  rgbToLab: function (r, g, b) {
    // 简化的RGB到LAB转换
    // 这是一个近似值，足够用于显示

    // 将RGB转换为0-1范围
    r /= 255;
    g /= 255;
    b /= 255;

    // 简化的XYZ转换
    const x = r * 0.4124 + g * 0.3576 + b * 0.1805;
    const y = r * 0.2126 + g * 0.7152 + b * 0.0722;
    const z = r * 0.0193 + g * 0.1192 + b * 0.9505;

    // 简化的LAB转换
    const l = 116 * Math.cbrt(y) - 16;
    const a = 500 * (Math.cbrt(x) - Math.cbrt(y));
    const b_val = 200 * (Math.cbrt(y) - Math.cbrt(z));

    return { l, a, b: b_val };
  },

  // RGB转CMYK
  rgbToCmyk: function (r, g, b) {
    // 简化的RGB到CMYK转换
    r /= 255;
    g /= 255;
    b /= 255;

    const k = 1 - Math.max(r, g, b);
    const c = k === 1 ? 0 : (1 - r - k) / (1 - k) * 100;
    const m = k === 1 ? 0 : (1 - g - k) / (1 - k) * 100;
    const y = k === 1 ? 0 : (1 - b - k) / (1 - k) * 100;

    return { c, m, y, k: k * 100 };
  },

  // 移除了底部工具栏功能

  // 判断颜色是否为亮色
  isLightColor: function (r, g, b) {
    // 使用colorUtils模块中的函数
    const hexColor = colorUtils.rgbToHex(r, g, b);
    return colorUtils.getTextColorForBackground(hexColor) === '#000000';
  },

  // 显示滑动提示
  showSwipeTips: function() {
    // 只有当有多个颜色时才显示提示
    if (this.data.colors.length <= 1) return;

    // 根据当前索引决定显示哪个提示
    // 优先显示下一个颜色的提示，如果没有下一个颜色，则显示上一个颜色的提示
    const hasNext = this.data.currentIndex < this.data.totalColors - 1;
    const hasPrev = this.data.currentIndex > 0;

    this.setData({
      showNextTip: hasNext,
      showPrevTip: !hasNext && hasPrev // 只有当没有下一个颜色且有上一个颜色时才显示上一个颜色的提示
    });

    // 3秒后自动隐藏提示
    setTimeout(() => {
      this.setData({
        showPrevTip: false,
        showNextTip: false
      });
    }, 3000);
  },

  // 处理触摸开始事件
  handleTouchStart: function(e) {
    this.setData({
      touchStartY: e.touches[0].clientY
    });
  },

  // 处理触摸结束事件
  handleTouchEnd: function(e) {
    const touchEndY = e.changedTouches[0].clientY;
    const touchStartY = this.data.touchStartY;

    // 计算滑动距离
    const distance = touchEndY - touchStartY;

    // 如果滑动距离太小，不处理
    if (Math.abs(distance) < 50) return;

    // 向上滑动，显示下一个颜色（方向调整）
    if (distance < 0 && this.data.currentIndex < this.data.totalColors - 1) {
      this.navigateToColor(this.data.currentIndex + 1);
    }
    // 向下滑动，显示上一个颜色（方向调整）
    else if (distance > 0 && this.data.currentIndex > 0) {
      this.navigateToColor(this.data.currentIndex - 1);
    }
  },

  // 导航到指定索引的颜色
  navigateToColor: function(index) {
    // 确保索引在有效范围内
    if (index < 0 || index >= this.data.colors.length) return;

    const color = this.data.colors[index];

    // 构建URL参数
    const params = {
      id: color.id,
      name: color.name,
      hex: encodeURIComponent(color.hex),
      rgb: encodeURIComponent(color.rgb),
      cmyk: encodeURIComponent(color.cmyk),
      index: index,
      total: this.data.totalColors,
      categoryId: this.data.categoryId,
      colorsData: encodeURIComponent(JSON.stringify(this.data.colors)),
      intro: encodeURIComponent(color.intro || '')
    };

    // 构建URL查询字符串
    const queryString = Object.keys(params)
      .map(key => `${key}=${params[key]}`)
      .join('&');

    // 重定向到新的颜色详情页
    wx.redirectTo({
      url: `/basic-tools/pages/colorDetail/colorDetail?${queryString}`
    });
  }
})
