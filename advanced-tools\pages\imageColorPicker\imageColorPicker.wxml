<!--pages/imageColorPicker/imageColorPicker.wxml-->
<view class="page">
  <!-- 加载状态指示器 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 错误提示 -->
  <view class="error-container" wx:if="{{hasError}}">
    <view class="error-icon">!</view>
    <view class="error-text">{{errorMessage || '加载失败，请重试'}}</view>
    <view class="error-btn" bindtap="reloadPage">重新加载</view>
  </view>

  <!-- 图片上传按钮 - 当没有图片且没有错误时显示 -->
  <view class="image-upload-section" wx:if="{{!imagePath && !isLoading && !hasError}}">
    <view class="upload-button" bindtap="chooseImage">
      <view class="upload-icon">
        <view class="icon-image"></view>
      </view>
      <view class="upload-text">点击选择图片</view>
    </view>
    <view class="upload-description">
      提取图片中的颜色，获取HEX和RGB色值
    </view>
  </view>

  <!-- 图片预览和取色区域 -->
  <view class="image-preview-section" wx:if="{{imagePath}}">
    <!-- 操作指引提示 - 更紧凑的版本 -->
    <view class="operation-guide">
      <view class="guide-item">
        <text class="guide-icon">👆</text>
        <text class="guide-text">点击取色</text>
      </view>
      <view class="guide-item">
        <text class="guide-icon">👇👇</text>
        <text class="guide-text">双击居中</text>
      </view>
      <view class="guide-item">
        <text class="guide-icon">✌️</text>
        <text class="guide-text">双指缩放</text>
      </view>
      <view class="guide-item">
        <text class="guide-icon">👆⏱️</text>
        <text class="guide-text">长按平移</text>
      </view>
    </view>

    <!-- 图片容器 - 固定在顶部不随滚动 -->
    <view class="image-container" catchtouchmove="onCatchTouchMove">
      <image
        class="picker-image"
        src="{{imagePath}}"
        mode="aspectFit"
        style="transform: scale({{scale}}) translate({{offsetX}}px, {{offsetY}}px);"
        bindtouchstart="onImageTouchStart"
        bindtouchmove="onImageTouchMove"
        bindtouchend="onImageTouchEnd"
        bindload="onImageLoad"
        bindtap="onImageTap"
        id="picker-image"
      ></image>

      <!-- 平移提示 - 仅在进入平移模式时显示 -->
      <view class="panning-tip" wx:if="{{showPanningTip}}">
        拖动图片以移动到需要取色的位置
      </view>
    </view>

    <!-- 可滚动的内容区域 -->
    <scroll-view class="scrollable-content" scroll-y="true">
      <!-- 取色结果显示 -->
      <view class="picked-color-info">
        <view class="color-result-grid">
          <!-- 新布局：放大器和右侧色块色值 -->
          <view class="magnifier-color-container">
            <!-- 左侧：放大镜区域 - 使用Canvas 2D API -->
            <view class="color-preview-thumbnail magnifier-thumbnail">
              <canvas type="2d" id="magnifierCanvas" class="magnifier-canvas" disable-scroll="true"></canvas>
            </view>

            <!-- 右侧：色块和色值 -->
            <view class="color-info-container">
              <!-- 上：颜色预览方块 -->
              <view class="color-preview-block" style="background-color: {{pickedColor || 'transparent'}}"></view>

              <!-- 中：HEX值 -->
              <view class="color-value-item">
                <text class="color-value-text color-value-hex">{{pickedColor || ''}}</text>
                <view class="copy-button" bindtap="copyColorValue" wx:if="{{pickedColor}}">复制</view>
              </view>

              <!-- 下：RGB值 -->
              <view class="color-value-item" wx:if="{{pickedRgb}}">
                <text class="color-value-text color-value-rgb">{{pickedRgb}}</text>
                <view class="copy-button" bindtap="copyRgbValue">复制</view>
              </view>
            </view>
          </view>
        </view>
      </view>



      <!-- 底部占位，防止内容被固定按钮遮挡 -->
      <view class="bottom-placeholder"></view>
    </scroll-view>
  </view>

  <!-- 离屏Canvas用于图片取色 - 使用Canvas 2D API -->
  <canvas type="2d" id="offscreenCanvas" disable-scroll="true" style="position: absolute; left: -9999px; width: 300px; height: 300px;"></canvas>

  <!-- 固定在底部的按钮容器 -->
  <view class="btn-container" wx:if="{{imagePath}}">
    <view class="action-btn reset" bindtap="resetScale" wx:if="{{scale > 1}}">重置缩放</view>
    <view class="action-btn reset" bindtap="chooseImage" wx:else>选择图片</view>
    <!-- 只有从颜色选择器组件进入时才显示确认按钮 -->
    <view class="action-btn confirm" bindtap="confirmColor" wx:if="{{fromColorPicker}}">确认</view>
  </view>
</view>
