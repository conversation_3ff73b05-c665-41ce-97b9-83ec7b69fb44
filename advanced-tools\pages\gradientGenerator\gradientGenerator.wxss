/* pages/gradientGenerator/gradientGenerator.wxss */
.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
}

.container {
  flex: 1;
  padding: 30rpx 24rpx;
  overflow-y: auto;
  box-sizing: border-box;
}

/* 通用部分样式 */
.section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.06);
  box-sizing: border-box;
  width: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #191919;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 28rpx;
  background-color: #07c160;
  border-radius: 3rpx;
}

/* 渐变预览部分 */
.preview-section {
  display: flex;
  flex-direction: column;
}

.gradient-preview-container {
  width: 100%;
  position: relative;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.gradient-preview {
  width: 70%;
  aspect-ratio: 1;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 1rpx solid #eeeeee;
}

.gradient-direction-indicator {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.direction-arrow {
  position: relative;
  width: 140rpx;
  height: 140rpx;
  opacity: 0.8;
  display: flex;
  align-items: center;
  justify-content: center;
  transform-origin: center center;
}

.arrow-head {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 14rpx solid transparent;
  border-right: 14rpx solid transparent;
  border-bottom: 22rpx solid rgba(255, 255, 255, 0.9);
  filter: drop-shadow(0 0 2rpx rgba(0, 0, 0, 0.2));
}

.arrow-line {
  position: absolute;
  top: 22rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 5rpx;
  height: 100rpx;
  background-color: rgba(255, 255, 255, 0.9);
  filter: drop-shadow(0 0 2rpx rgba(0, 0, 0, 0.2));
}

.gradient-center-indicator {
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 0 4rpx rgba(0, 0, 0, 0.1);
  pointer-events: none;
  transition: transform 0.3s ease;
}

.gradient-center-indicator::before {
  content: "";
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx dashed rgba(255, 255, 255, 0.6);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.gradient-code-container {
  width: 100%;
  margin-bottom: 20rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 16rpx;
  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

.gradient-code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.gradient-code-label {
  font-size: 26rpx;
  color: #191919;
  font-weight: 600;
}

.gradient-code-copy {
  font-size: 22rpx;
  color: #666666;
  padding: 4rpx 10rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  position: relative;
  transition: all 0.3s ease;
}

.gradient-code-copy:active {
  background-color: #e8e8e8;
  transform: scale(0.95);
}

.gradient-code {
  width: 100%;
  padding: 16rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #333333;
  font-family: monospace;
  word-break: break-all;
  box-sizing: border-box;
  border: 1rpx solid #eeeeee;
  line-height: 1.5;
  max-height: 200rpx;
  overflow-y: auto;
}

/* 保存为壁纸按钮样式 */
.save-wallpaper-container {
  margin-top: 30rpx;
  display: flex;
  justify-content: center;
}

.save-wallpaper-btn {
  background-color: #07c160;
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 500;
  padding: 0;
  height: 80rpx;
  line-height: 80rpx;
  width: 60%;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3);
  border: none;
  transition: all 0.3s ease;
  letter-spacing: 1rpx;
}

.save-wallpaper-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(7, 193, 96, 0.2);
  opacity: 0.9;
}

/* 保存成功提示 */
.save-success {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.8);
  background-color: rgba(0, 0, 0, 0.7);
  padding: 30rpx 40rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0;
  transition: all 0.3s ease;
  pointer-events: none;
  z-index: 100;
}

.save-success.show {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

.success-icon {
  width: 80rpx;
  height: 80rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2307c160'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  margin-bottom: 16rpx;
}

.success-text {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 500;
}

/* 复制提示 */
.copy-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 10;
  border-radius: 12rpx;
}

.copy-indicator.show {
  opacity: 1;
}

.copy-indicator-text {
  color: #ffffff;
  font-size: 20rpx;
  font-weight: 500;
  background-color: rgba(7, 193, 96, 0.9);
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

/* 渐变类型选择 */
.type-selector {
  display: flex;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
  background-color: #f5f5f5;
}

.type-item {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 16rpx 0;
  font-size: 26rpx;
  color: #666666;
  transition: all 0.2s ease;
  position: relative;
  gap: 8rpx;
}

.type-item.active {
  background-color: #07c160;
  color: #ffffff;
}

.type-item:active {
  opacity: 0.8;
}

.type-icon {
  width: 28rpx;
  height: 28rpx;
  border-radius: 4rpx;
}

.linear-icon {
  background: linear-gradient(to right, #07c160, #09e374);
}

.radial-icon {
  background: radial-gradient(circle, #07c160, #09e374);
}

/* 渐变方向控制 */
.direction-control-layout {
  display: flex;
  margin-bottom: 20rpx;
  gap: 20rpx;
}

.direction-visual-container {
  flex: 0 0 auto;
  width: 220rpx;
}

.direction-visual-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 2rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  overflow: hidden;
  aspect-ratio: 1;
  width: 100%;
}

.direction-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  transition: all 0.2s ease;
  position: relative;
  aspect-ratio: 1;
}

.direction-btn.active {
  background-color: #07c160;
}

.direction-btn-center {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  box-shadow: inset 0 0 3rpx rgba(0, 0, 0, 0.1);
}

.direction-btn:active {
  opacity: 0.8;
}

.direction-controls-right {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.direction-arrow {
  width: 16rpx;
  height: 16rpx;
  position: relative;
}

.direction-arrow::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 10rpx;
  height: 10rpx;
  border-width: 0 2rpx 2rpx 0;
  border-style: solid;
  border-color: #999999;
  transform: translate(-50%, -50%) rotate(45deg);
}

.direction-btn.active .direction-arrow::before {
  border-color: #ffffff;
}

.direction-btn-center .direction-arrow::before {
  border-color: #07c160;
  width: 12rpx;
  height: 12rpx;
}

.top-arrow::before {
  transform: translate(-50%, -50%) rotate(-135deg);
}

.right-top-arrow::before {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.right-arrow::before {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.right-bottom-arrow::before {
  transform: translate(-50%, -50%) rotate(45deg);
}

.bottom-arrow::before {
  transform: translate(-50%, -50%) rotate(45deg);
}

.left-bottom-arrow::before {
  transform: translate(-50%, -50%) rotate(135deg);
}

.left-arrow::before {
  transform: translate(-50%, -50%) rotate(135deg);
}

.left-top-arrow::before {
  transform: translate(-50%, -50%) rotate(-135deg);
}

.angle-control {
  background-color: #f8f8f8;
  padding: 16rpx;
  border-radius: 8rpx;
  position: relative;
}

.angle-direction-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.angle-label {
  font-size: 24rpx;
  color: #666666;
}

.angle-direction-text {
  color: #07c160;
  font-weight: 500;
}

.angle-value {
  font-size: 24rpx;
  color: #07c160;
  font-weight: 500;
}

.angle-pointer-container {
  display: flex;
  justify-content: center;
  margin-bottom: 20rpx;
}

.angle-pointer {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #ffffff;
  border: 1rpx solid #eeeeee;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.angle-pointer::before {
  content: "";
  position: absolute;
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  border: 1rpx dashed #eeeeee;
}

.angle-pointer::after {
  content: "";
  position: absolute;
  width: 6rpx;
  height: 6rpx;
  background-color: #999999;
  border-radius: 50%;
}

/* 添加刻度线 */
.angle-pointer .angle-pointer-indicator::before {
  content: "";
  position: absolute;
  top: -4rpx;
  left: -2rpx;
  width: 6rpx;
  height: 6rpx;
  background-color: #07c160;
  border-radius: 50%;
}

.angle-pointer-indicator {
  position: absolute;
  width: 2rpx;
  height: 32rpx;
  background-color: #07c160;
  transform-origin: bottom center;
  bottom: 50%;
}

/* 径向渐变形状控制 */
.shape-selector {
  display: flex;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.shape-item {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 16rpx 0;
  font-size: 26rpx;
  color: #666666;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  transition: all 0.2s ease;
  gap: 8rpx;
}

.shape-item.active {
  background-color: #07c160;
  color: #ffffff;
}

.shape-item:active {
  opacity: 0.8;
}

.shape-icon {
  width: 28rpx;
  height: 28rpx;
  border-radius: 4rpx;
  background-color: #dddddd;
}

.circle-icon {
  border-radius: 50%;
}

.ellipse-icon {
  border-radius: 50%;
  transform: scaleX(1.5);
}

.shape-item.active .shape-icon {
  background-color: #ffffff;
}

.position-label {
  font-size: 24rpx;
  color: #666666;
  margin: 16rpx 0 12rpx;
  font-weight: 500;
}

/* 可视化位置选择器 */
.position-visual-container {
  width: 100%;
  margin-bottom: 16rpx;
}

.position-visual-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 2rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  overflow: hidden;
  aspect-ratio: 1;
  width: 100%;
  max-width: 300rpx;
  margin: 0 auto;
}

.position-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  transition: all 0.2s ease;
  position: relative;
  aspect-ratio: 1;
}

.position-btn.active {
  background-color: #07c160;
}

.position-btn:active {
  opacity: 0.8;
}

.position-indicator {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #666666;
  position: relative;
}

.position-btn.active .position-indicator {
  background-color: #ffffff;
  box-shadow: 0 0 4rpx rgba(255, 255, 255, 0.8);
}

/* 不同位置的指示器 */
.center-indicator {
  /* 中心点不需要特殊处理 */
  width: 14rpx;
  height: 14rpx;
}

.top-indicator {
  transform: translateY(-8rpx);
}

.right-top-indicator {
  transform: translate(8rpx, -8rpx);
}

.right-indicator {
  transform: translateX(8rpx);
}

.right-bottom-indicator {
  transform: translate(8rpx, 8rpx);
}

.bottom-indicator {
  transform: translateY(8rpx);
}

.left-bottom-indicator {
  transform: translate(-8rpx, 8rpx);
}

.left-indicator {
  transform: translateX(-8rpx);
}

.left-top-indicator {
  transform: translate(-8rpx, -8rpx);
}

/* 位置显示 */
.position-display {
  text-align: center;
  margin-top: 12rpx;
}

.position-display-label {
  font-size: 24rpx;
  color: #666666;
}

.position-display-text {
  color: #07c160;
  font-weight: 500;
}

/* 颜色管理 */
.color-stops-container {
  background-color: #f9f9f9;
  padding: 16rpx;
  border-radius: 12rpx;
  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 24rpx;
}

.color-stops {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  padding: 8rpx 0;
}

.color-stop {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.color-stop.active {
  transform: scale(1.15);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  border-color: #07c160;
}

.color-stop-position {
  position: absolute;
  bottom: -28rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 20rpx;
  color: #666666;
  white-space: nowrap;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 2rpx 6rpx;
  border-radius: 10rpx;
}

.color-stop-delete {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 24rpx;
  height: 24rpx;
  background-color: #ff3b30;
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18rpx;
  z-index: 1;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.add-color-stop {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #ffffff;
  color: #07c160;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2rpx dashed #07c160;
}

.add-icon {
  font-size: 32rpx;
  font-weight: bold;
}

.add-color-stop:active {
  transform: scale(0.95);
  background-color: #f0f0f0;
}

.color-stop-controls {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-top: 30rpx;
}

/* 颜色卡片样式 */
.color-card {
  background-color: #ffffff;
  border-radius: 10rpx;
  padding: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
  position: relative;
}

.color-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.color-card-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #191919;
}

.color-card-copy {
  font-size: 22rpx;
  color: #666666;
  padding: 4rpx 10rpx;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  position: relative;
  transition: all 0.3s ease;
}

.color-card-copy:active {
  background-color: #e8e8e8;
  transform: scale(0.95);
}

.color-card-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

/* 参考颜色转换器的样式 */
.base-color-container {
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
  padding: 4rpx 0 0;
  flex-wrap: nowrap;
  justify-content: space-between;
  gap: 0;
  overflow: hidden;
}

.color-preview {
  width: 63%;
  height: 134rpx;
  border-radius: 10rpx;
  margin-right: 0;
  border: none;
  flex: 0.63;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 0;
  margin-top: -2rpx;
  box-sizing: border-box;
}

.color-hex-value {
  font-size: 28rpx;
  font-family: 'Courier New', monospace;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  text-align: center;
  max-width: 90%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  letter-spacing: 0.5rpx;
  font-weight: 500;
  /* 文字颜色通过内联样式动态设置 */
  /* 半透明背景，使文字更易读 */
  background-color: rgba(0, 0, 0, 0.15);
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

/* 右侧上下并列的按钮容器 */
.color-actions-column {
  width: 38%;
  flex: 0.38;
  display: flex;
  flex-direction: column;
  gap: 14rpx;
  min-width: 180rpx;
  max-width: 220rpx;
  margin-left: 0;
}

/* 按钮包装器 */
.btn-wrapper {
  width: 100%;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: -1rpx;
  box-sizing: border-box;
  position: relative;
}

.custom-btn {
  width: 100%;
  font-size: 22rpx;
  padding: 0 4rpx;
  background-color: #f5f5f5;
  color: #333333;
  border-radius: 6rpx;
  border: none;
  line-height: 60rpx;
  height: 60rpx;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
  font-weight: 500;
  position: relative;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  letter-spacing: -0.8rpx;
  text-align: center;
  margin: 0;
  display: block;
  box-sizing: border-box;
  cursor: pointer;
}

/* 随机颜色按钮 */
.random-btn {
  background-color: #f0f0f0;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 选择颜色按钮 */
.picker-btn {
  background-color: #07c160;
  color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.3);
}

/* 位置控制样式 */
.position-control-container {
  background-color: #ffffff;
  border-radius: 10rpx;
  padding: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.position-control-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.position-control-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #191919;
}

.position-value {
  font-size: 24rpx;
  color: #07c160;
  font-weight: 500;
}

/* 预设渐变 */
.presets-description {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 16rpx;
  text-align: center;
}

.presets-scroll {
  width: 100%;
  white-space: nowrap;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch; /* 提供iOS的滚动惯性 */
  scrollbar-width: none; /* 隐藏Firefox滚动条 */
  margin: 0 -8rpx;
  padding: 0 8rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 16rpx 8rpx;
  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

.presets-scroll::-webkit-scrollbar {
  display: none; /* 隐藏WebKit滚动条 */
}

.presets-container {
  display: inline-flex;
  padding: 8rpx 0;
  gap: 20rpx;
}

.preset-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 120rpx;
  transition: all 0.3s ease;
}

.preset-preview {
  width: 100%;
  height: 80rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 8rpx;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
  background-color: #ffffff;
}

.preset-name {
  font-size: 20rpx;
  color: #666666;
  text-align: center;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}

.preset-item:active .preset-preview {
  transform: scale(0.95);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
  border-color: #07c160;
}

/* 颜色选择器弹窗 */
.color-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.color-picker-container {
  width: 92%;
  max-width: 650rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
  transform: scale(1);
  transition: transform 0.3s ease;
  animation: scaleIn 0.3s ease;
}

@keyframes scaleIn {
  from { transform: scale(0.9); }
  to { transform: scale(1); }
}

.color-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 28rpx;
  border-bottom: 1rpx solid #eeeeee;
  background-color: #f9f9f9;
}

.color-picker-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #191919;
  position: relative;
  padding-left: 20rpx;
}

.color-picker-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 28rpx;
  background-color: #07c160;
  border-radius: 3rpx;
}

.color-picker-close {
  font-size: 36rpx;
  color: #666666;
  line-height: 1;
  padding: 10rpx;
  margin: -10rpx;
  transition: all 0.3s ease;
}

.color-picker-close:active {
  transform: scale(0.9);
  color: #333333;
}
