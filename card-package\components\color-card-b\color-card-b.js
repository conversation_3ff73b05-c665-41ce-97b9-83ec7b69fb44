// components/color-card-b/color-card-b.js
const colorUtils = require('../../../utils/colorUtils');

Component({
  properties: {
    colors: {
      type: Array,
      value: []
    },
    imagePath: {
      type: String,
      value: ''
    }
  },

  data: {
    canvasWidth: 1600, // 画布宽度
    canvasHeight: 2232 // 画布高度，参考1103.html
  },

  lifetimes: {
    attached() {
      this.drawColorCard();
    }
  },

  methods: {
    async drawColorCard() {
      const { canvasWidth, canvasHeight } = this.data;
      const { colors, imagePath } = this.properties;

      if (!imagePath || colors.length === 0) return;

      try {
        // 获取Canvas节点
        const query = this.createSelectorQuery();
        const canvas = await new Promise((resolve) => {
          query.select('#colorCardCanvas')
            .fields({ node: true, size: true })
            .exec((res) => {
              resolve(res[0]);
            });
        });

        if (!canvas || !canvas.node) {
          // 获取Canvas节点失败，静默处理
          return;
        }

        const ctx = canvas.node.getContext('2d');

        // 设置Canvas的实际尺寸
        canvas.node.width = canvasWidth;
        canvas.node.height = canvasHeight;

        // 绘制白色背景
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, canvasWidth, canvasHeight);

        try {
          // 获取图片信息
          const res = await new Promise((resolve, reject) => {
            wx.getImageInfo({
              src: imagePath,
              success: resolve,
              fail: reject
            });
          });

          const { width: imgWidth, height: imgHeight } = res;

          // 创建图片对象
          const img = canvas.node.createImage();
          await new Promise((resolve, reject) => {
            img.onload = resolve;
            img.onerror = reject;
            img.src = imagePath;
          });

          // 绘制标题
          ctx.fillStyle = '#333333';
          ctx.font = 'bold 48px PingFang SC, sans-serif';
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText('COLOR ｜ KALA配色', canvasWidth / 2, 80 + 33.5);

          // 绘制图片 - 1:1比例
          const imageWidth = 1500;
          const imageHeight = 1500;
          const imageX = 49;
          const imageY = 192;

          this.drawSquareImage(ctx, img, imageX, imageY, imageWidth, imageHeight, imgWidth, imgHeight);

          // 绘制颜色块 - 胶囊形状
          // 颜色块是胶囊形状，水平排列
          const colorBlockWidth = 180; // 颜色块宽度（适当增加）
          const colorBlockHeight = 368; // 颜色块高度
          const colorBlockY = 1778; // 颜色块Y坐标

          // 计算水平居中的X坐标
          const totalWidth = 5 * colorBlockWidth + 4 * 50; // 5个色块 + 4个间距(每个50px)
          const startX = (canvasWidth - totalWidth) / 2 + colorBlockWidth / 2; // 第一个色块的中心点X坐标

          // 计算每个色块的X坐标中心点，使整体水平居中
          const colorBlockXPositions = [
            startX,
            startX + colorBlockWidth + 50,
            startX + 2 * (colorBlockWidth + 50),
            startX + 3 * (colorBlockWidth + 50),
            startX + 4 * (colorBlockWidth + 50)
          ];

          // 绘制颜色块
          this.drawColorBlocks(ctx, colors, colorBlockWidth, colorBlockY, colorBlockHeight, colorBlockXPositions);

          // 保存Canvas
          setTimeout(() => {
            this.saveCanvas(canvas.node);
          }, 300);
        } catch (err) {
          // 绘制失败，触发生成事件
          this.triggerEvent('generated', { path: imagePath });
        }
      } catch (error) {
        // Canvas初始化失败，触发生成事件
        this.triggerEvent('generated', { path: imagePath });
      }
    },

    // 绘制图片 (1:1比例)，带圆角效果
    drawSquareImage(ctx, img, destX, destY, destWidth, destHeight, imgWidth, imgHeight) {
      // 计算裁剪参数，保持1:1比例
      let sourceX = 0;
      let sourceY = 0;
      let sourceWidth = imgWidth;
      let sourceHeight = imgHeight;

      // 计算目标区域的实际比例
      const destRatio = destWidth / destHeight;

      // 根据原始图片比例进行裁剪
      const imgRatio = imgWidth / imgHeight;

      if (imgRatio > destRatio) {
        // 图片比目标区域更宽，需要裁剪左右
        sourceWidth = imgHeight * destRatio;
        sourceX = (imgWidth - sourceWidth) / 2;
      } else if (imgRatio < destRatio) {
        // 图片比目标区域更高，需要裁剪上下
        sourceHeight = imgWidth / destRatio;
        sourceY = (imgHeight - sourceHeight) / 2;
      }

      // 设置圆角半径
      const radius = 40; // 圆角半径，根据需求设置为40像素

      // 创建圆角矩形路径
      ctx.save(); // 保存当前状态
      this.createRoundedRectPath(ctx, destX, destY, destWidth, destHeight, radius);
      ctx.clip(); // 裁剪为圆角矩形

      // 绘制图片
      ctx.drawImage(
        img,
        sourceX, sourceY, sourceWidth, sourceHeight, // 源图像参数
        destX, destY, destWidth, destHeight // 目标区域参数
      );

      ctx.restore(); // 恢复状态，移除裁剪
    },

    // 创建圆角矩形路径
    createRoundedRectPath(ctx, x, y, width, height, radius) {
      // 确保半径不超过宽度和高度的一半
      const r = Math.min(radius, Math.min(width, height) / 2);

      ctx.beginPath();
      // 从左上角开始，顺时针绘制
      ctx.moveTo(x + r, y); // 左上角圆弧的起点
      ctx.lineTo(x + width - r, y); // 上边
      ctx.arcTo(x + width, y, x + width, y + r, r); // 右上角圆弧
      ctx.lineTo(x + width, y + height - r); // 右边
      ctx.arcTo(x + width, y + height, x + width - r, y + height, r); // 右下角圆弧
      ctx.lineTo(x + r, y + height); // 下边
      ctx.arcTo(x, y + height, x, y + height - r, r); // 左下角圆弧
      ctx.lineTo(x, y + r); // 左边
      ctx.arcTo(x, y, x + r, y, r); // 左上角圆弧
      ctx.closePath();
    },

    // 绘制颜色块 - 胶囊形状
    drawColorBlocks(ctx, colors, width, startY, height, xPositions) {
      // 确保最多显示5个颜色
      const actualColors = colors.slice(0, 5);

      // 如果颜色不足5个，补充默认颜色
      while (actualColors.length < 5) {
        const defaultColors = ['#4A4130', '#5D2317', '#900407', '#D8DDE1', '#7E96B2'];
        const missingCount = 5 - actualColors.length;
        for (let i = 0; i < missingCount; i++) {
          actualColors.push(defaultColors[i % defaultColors.length]);
        }
      }

      // 绘制每个颜色块
      actualColors.forEach((color, index) => {
        const x = xPositions[index] - width/2; // 中心点转换为左上角坐标
        const y = startY;

        // 绘制椭圆形颜色块（胶囊形状）
        this.drawEllipse(ctx, x, y, width, height, color);

        // 获取RGB值
        const rgb = colorUtils.hexToRgb(color);

        // 获取适合背景色的文字颜色 - 优先使用白色
        const textColors = colorUtils.getTextColorsForBackground(color, false, true);

        // 文本位置 - 优化后的样式
        const textX = x + width/2;
        const textY = y + height;

        // 绘制HEX值 - 在下方，位置上移，使用适合背景的颜色
        ctx.fillStyle = textColors.hexTextColor;
        ctx.font = 'bold 30px Arial, sans-serif';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        const colorCode = color.toUpperCase().replace('#', '');
        ctx.fillText(`# ${colorCode}`, textX, textY - 85);

        // 绘制RGB值 - 在上方，字体调小，位置上移，使用80%透明度的颜色
        ctx.fillStyle = textColors.rgbTextColor;
        ctx.font = '20px Arial, sans-serif';
        ctx.fillText(`R${rgb.r} G${rgb.g} B${rgb.b}`, textX, textY - 130);
      });
    },

    // 绘制胶囊形状色块（圆角矩形，圆角半径等于宽度的一半）
    drawEllipse(ctx, x, y, width, height, fillColor) {
      // 胶囊形状实际上是一个圆角矩形，其中圆角半径等于矩形宽度的一半
      const radius = width / 2;

      ctx.beginPath();

      // 绘制圆角矩形路径
      ctx.moveTo(x + radius, y); // 从左上角的圆弧右侧开始
      ctx.lineTo(x + width - radius, y); // 上边
      ctx.arcTo(x + width, y, x + width, y + radius, radius); // 右上角圆弧
      ctx.lineTo(x + width, y + height - radius); // 右边
      ctx.arcTo(x + width, y + height, x + width - radius, y + height, radius); // 右下角圆弧
      ctx.lineTo(x + radius, y + height); // 下边
      ctx.arcTo(x, y + height, x, y + height - radius, radius); // 左下角圆弧
      ctx.lineTo(x, y + radius); // 左边
      ctx.arcTo(x, y, x + radius, y, radius); // 左上角圆弧
      ctx.closePath();

      // 填充颜色
      ctx.fillStyle = fillColor;
      ctx.fill();

      // 添加细微的边框，增强视觉效果
      ctx.strokeStyle = 'rgba(0,0,0,0.05)';
      ctx.lineWidth = 1;
      ctx.stroke();
    },

    // 使用工具函数替代

    // 保存Canvas
    async saveCanvas(canvasNode) {
      try {
        const { canvasWidth, canvasHeight } = this.data;

        const res = await new Promise((resolve, reject) => {
          wx.canvasToTempFilePath({
            canvas: canvasNode,
            width: canvasWidth,
            height: canvasHeight,
            destWidth: canvasWidth,
            destHeight: canvasHeight,
            fileType: 'png', // 使用PNG格式以保留透明度和圆角效果
            quality: 1,
            success: resolve,
            fail: reject
          }, this);
        });

        // Canvas保存成功
        this.triggerEvent('generated', { path: res.tempFilePath });
      } catch (err) {
        // Canvas保存失败，使用原始图片路径
        this.triggerEvent('generated', { path: this.properties.imagePath });
      }
    }
  }
})
