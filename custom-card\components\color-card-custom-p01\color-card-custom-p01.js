// components/color-card-custom-p01/color-card-custom-p01.js
Component({
  properties: {
    colors: {
      type: Array,
      value: ['#21115E', '#1533AC']
    },
    title: {
      type: String,
      value: '海盐气泡'
    },
    backgroundColor: {
      type: String,
      value: '#FFFFFF'
    },
    titleColor: {
      type: String,
      value: '#5865B1'
    }
  },

  data: {
    canvasWidth: 1200, // 增加Canvas宽度以容纳5个颜色，原设计1600px
    canvasHeight: 1378, // 按比例缩放，原设计1838px
  },

  lifetimes: {
    attached() {
      // 组件实例进入页面节点树时执行
      console.log('P01自定义色卡组件已加载');
    },

    ready() {
      // 在组件在视图层布局完成后执行
      console.log('P01自定义色卡组件布局完成，开始生成色卡');
      // 延迟一段时间后开始生成，确保Canvas已准备好
      setTimeout(() => {
        this.generateColorCard();
      }, 100);
    }
  },

  methods: {
    // 获取设备像素比
    getDevicePixelRatio() {
      try {
        const deviceInfo = wx.getDeviceInfo();
        return deviceInfo.pixelRatio || 2;
      } catch (error) {
        console.warn('获取设备像素比失败，使用默认值:', error);
        return 2; // 默认值
      }
    },

    // 生成自定义色卡
    async generateColorCard() {
      try {
        const { colors, title, backgroundColor, titleColor } = this.properties;
        const { canvasWidth, canvasHeight } = this.data;

        console.log('开始生成P01样式自定义色卡:', { colors, title, backgroundColor });
        console.log('颜色数组长度:', colors ? colors.length : 'undefined');
        console.log('颜色数组内容:', colors);

        // 验证颜色数据
        if (!colors || !Array.isArray(colors) || colors.length === 0) {
          console.error('P01色卡生成失败：颜色数据无效');
          this.triggerEvent('generated', { path: '' });
          return;
        }

        // 创建Canvas上下文
        const query = wx.createSelectorQuery().in(this);
        const canvas = await new Promise((resolve, reject) => {
          query.select('#colorCardCanvas')
            .fields({ node: true, size: true })
            .exec((res) => {
              console.log('Canvas查询结果:', res);
              if (res && res[0] && res[0].node) {
                resolve(res[0]);
              } else {
                reject(new Error('Canvas节点获取失败'));
              }
            });
        });

        const ctx = canvas.node.getContext('2d');
        const dpr = this.getDevicePixelRatio();

        // 设置Canvas尺寸
        canvas.node.width = canvasWidth * dpr;
        canvas.node.height = canvasHeight * dpr;
        ctx.scale(dpr, dpr);

        console.log('Canvas设置完成，开始绘制');

        // 绘制背景
        ctx.fillStyle = backgroundColor || '#FFFFFF';
        ctx.fillRect(0, 0, canvasWidth, canvasHeight);

        // 绘制内容
        this.drawTitle(ctx, title || '海盐气泡', titleColor);
        this.drawColorBlocks(ctx, colors);

        console.log('绘制完成，准备保存');

        // 保存Canvas
        setTimeout(() => {
          this.saveCanvas(canvas.node);
        }, 300);

      } catch (err) {
        console.error('绘制P01样式自定义色卡失败:', err);
        this.triggerEvent('generated', { path: '' });
      }
    },

    // 绘制标题 - 按照p01.html的设计
    drawTitle(ctx, title, titleColor) {
      const { canvasWidth, canvasHeight } = this.data;

      // 按照原设计比例计算位置和大小，标题向下移动
      // 原设计：标题位置 left:605px, top:177px, 字体130px
      const titleX = canvasWidth / 2; // 居中显示
      const titleY = Math.floor(280 * canvasHeight / 1838); // 进一步向下移动，从220调整到280
      const fontSize = Math.floor(130 * canvasWidth / 1600); // 按比例计算字体大小

      // 设置标题样式 - 使用动态标题颜色
      ctx.fillStyle = titleColor || '#5865B1';
      ctx.font = `bold ${fontSize}px PingFang SC, Arial, sans-serif`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      // 绘制标题
      ctx.fillText(title, titleX, titleY);
    },

    // 绘制颜色块 - 按照p01.html的设计
    drawColorBlocks(ctx, colors) {
      const { canvasWidth, canvasHeight } = this.data;

      // 绘制长条形色块
      this.drawLongBlocks(ctx, colors);
      
      // 绘制颜色代码
      this.drawColorCodes(ctx, colors);
      
      // 绘制圆形色块
      this.drawCircleBlocks(ctx, colors);
    },

    // 绘制长条形色块
    drawLongBlocks(ctx, colors) {
      const { canvasWidth, canvasHeight } = this.data;

      console.log('绘制长条形色块，颜色数量:', colors.length);
      console.log('绘制长条形色块，颜色数据:', colors);

      // 固定色块尺寸，根据颜色数量调整间距
      const blockWidth = Math.floor(168 * canvasWidth / 1600); // 固定宽度，按比例缩放
      const blockHeight = Math.floor(612 * canvasHeight / 1838); // 固定高度
      const borderRadius = Math.floor(84 * canvasWidth / 1600); // 固定圆角半径
      const colorCount = colors.length;

      // 计算可用宽度和动态间距，设置最大间距限制
      const maxWidth = canvasWidth * 0.9; // 使用Canvas宽度的90%
      const totalBlockWidth = colorCount * blockWidth;
      const availableGapWidth = maxWidth - totalBlockWidth;
      const maxGap = Math.floor(200 * canvasWidth / 1600); // 设置最大间距限制，按比例缩放
      let gap = colorCount > 1 ? Math.floor(availableGapWidth / (colorCount - 1)) : 0;
      gap = Math.min(gap, maxGap); // 限制最大间距

      // 计算总宽度并居中
      const totalWidth = totalBlockWidth + (colorCount - 1) * gap;
      const startX = (canvasWidth - totalWidth) / 2;
      const startY = Math.floor(579 * canvasHeight / 1838);

      console.log('计算结果 - 固定色块宽度:', blockWidth, '动态间距:', gap, '总宽度:', totalWidth, 'Canvas宽度:', canvasWidth);

      for (let i = 0; i < colors.length; i++) {
        const x = startX + i * (blockWidth + gap);
        const y = startY;

        console.log(`绘制第${i + 1}个长条形色块，颜色: ${colors[i]}, 位置: (${x}, ${y}), 尺寸: ${blockWidth}x${blockHeight}`);

        // 绘制圆角矩形 - 使用兼容性更好的方法
        ctx.fillStyle = colors[i];
        this.drawRoundedRect(ctx, x, y, blockWidth, blockHeight, borderRadius);
      }
    },

    // 绘制圆角矩形的兼容方法
    drawRoundedRect(ctx, x, y, width, height, radius) {
      ctx.beginPath();
      ctx.moveTo(x + radius, y);
      ctx.lineTo(x + width - radius, y);
      ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
      ctx.lineTo(x + width, y + height - radius);
      ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
      ctx.lineTo(x + radius, y + height);
      ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
      ctx.lineTo(x, y + radius);
      ctx.quadraticCurveTo(x, y, x + radius, y);
      ctx.closePath();
      ctx.fill();
    },

    // 绘制颜色代码
    drawColorCodes(ctx, colors) {
      const { canvasWidth, canvasHeight } = this.data;

      console.log('绘制颜色代码，颜色数量:', colors.length);

      // 使用与长条形色块相同的布局计算（固定宽度，动态间距）
      const blockWidth = Math.floor(168 * canvasWidth / 1600); // 与长条形色块相同的固定宽度
      const colorCount = colors.length;

      // 计算动态间距，设置最大间距限制
      const maxWidth = canvasWidth * 0.9;
      const totalBlockWidth = colorCount * blockWidth;
      const availableGapWidth = maxWidth - totalBlockWidth;
      const maxGap = Math.floor(200 * canvasWidth / 1600); // 设置最大间距限制
      let gap = colorCount > 1 ? Math.floor(availableGapWidth / (colorCount - 1)) : 0;
      gap = Math.min(gap, maxGap); // 限制最大间距

      const codeY = Math.floor(1281 * canvasHeight / 1838);
      const fontSize = Math.floor(48 * canvasWidth / 1600);
      const totalWidth = totalBlockWidth + (colorCount - 1) * gap;
      const startX = (canvasWidth - totalWidth) / 2;

      ctx.font = `${fontSize}px Arial, sans-serif`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      for (let i = 0; i < colors.length; i++) {
        const x = startX + i * (blockWidth + gap) + blockWidth / 2;
        // 使用对应色块的颜色作为文字颜色
        ctx.fillStyle = colors[i];
        console.log(`绘制第${i + 1}个颜色代码: ${colors[i]}, 位置: (${x}, ${codeY}), 颜色: ${colors[i]}`);
        ctx.fillText(colors[i], x, codeY);
      }
    },

    // 绘制圆形色块
    drawCircleBlocks(ctx, colors) {
      const { canvasWidth, canvasHeight } = this.data;

      console.log('绘制圆形色块，颜色数量:', colors.length);

      // 使用与长条形色块相同的布局计算（固定大小，动态间距）
      const circleSize = Math.floor(168 * canvasWidth / 1600); // 与长条形色块相同的固定宽度
      const colorCount = colors.length;

      // 计算动态间距，设置最大间距限制
      const maxWidth = canvasWidth * 0.9;
      const totalCircleWidth = colorCount * circleSize;
      const availableGapWidth = maxWidth - totalCircleWidth;
      const maxGap = Math.floor(200 * canvasWidth / 1600); // 设置最大间距限制
      let gap = colorCount > 1 ? Math.floor(availableGapWidth / (colorCount - 1)) : 0;
      gap = Math.min(gap, maxGap); // 限制最大间距

      const startY = Math.floor(1432 * canvasHeight / 1838);
      const totalWidth = totalCircleWidth + (colorCount - 1) * gap;
      const startX = (canvasWidth - totalWidth) / 2;

      for (let i = 0; i < colors.length; i++) {
        const x = startX + i * (circleSize + gap);
        const y = startY;

        console.log(`绘制第${i + 1}个圆形色块，颜色: ${colors[i]}, 位置: (${x}, ${y}), 大小: ${circleSize}`);

        // 绘制圆形
        ctx.fillStyle = colors[i];
        ctx.beginPath();
        ctx.arc(x + circleSize / 2, y + circleSize / 2, circleSize / 2, 0, 2 * Math.PI);
        ctx.fill();
      }
    },

    // 保存Canvas
    async saveCanvas(canvasNode) {
      try {
        const { canvasWidth, canvasHeight } = this.data;

        const res = await new Promise((resolve, reject) => {
          wx.canvasToTempFilePath({
            canvas: canvasNode,
            x: 0,
            y: 0,
            width: canvasWidth,
            height: canvasHeight,
            destWidth: canvasWidth,
            destHeight: canvasHeight,
            fileType: 'png',
            quality: 1,
            success: resolve,
            fail: reject
          }, this);
        });

        // Canvas保存成功
        this.triggerEvent('generated', { path: res.tempFilePath });
      } catch (err) {
        console.error('保存P01样式自定义色卡失败:', err);
        this.triggerEvent('generated', { path: '' });
      }
    }
  }
});
