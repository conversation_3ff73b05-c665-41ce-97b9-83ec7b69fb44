// 渐变色数据
// 从FY.csv提取，共167条数据
const GRADIENT_DATA = [
  {
    "id": "1",
    "name": "Above_The_Sky",
    "chineseName": "天空之上",
    "cssStyle": "linear-gradient(to top, lightgrey 0%, lightgrey 1%, #e0e0e0 26%, #efefef 48%, #d9d9d9 75%, #bcbcbc 100%)"
  },
  {
    "id": "2",
    "name": "African_Field",
    "chineseName": "非洲田野",
    "cssStyle": "linear-gradient(to top, #65bd60 0%, #5ac1a8 25%, #3ec6ed 50%, #b7ddb7 75%, #fef381 100%)"
  },
  {
    "id": "3",
    "name": "Alchemist_Lab",
    "chineseName": "炼金术实验室",
    "cssStyle": "linear-gradient(-20deg, #d558c8 0%, #24d292 100%)"
  },
  {
    "id": "4",
    "name": "<PERSON>our_Amour",
    "chineseName": "爱之爱",
    "cssStyle": "linear-gradient(to top, #f77062 0%, #fe5196 100%)"
  },
  {
    "id": "5",
    "name": "Amy_Crisp",
    "chineseName": "艾米脆饼",
    "cssStyle": "linear-gradient(120deg, #a6c0fe 0%, #f68084 100%)"
  },
  {
    "id": "6",
    "name": "Angel_Care",
    "chineseName": "天使关怀",
    "cssStyle": "linear-gradient(-225deg, #FFE29F 0%, #FFA99F 48%, #FF719A 100%)"
  },
  {
    "id": "7",
    "name": "Aqua_Guidance",
    "chineseName": "水之引导",
    "cssStyle": "linear-gradient(to top, #007adf 0%, #00ecbc 100%)"
  },
  {
    "id": "8",
    "name": "Aqua_Splash",
    "chineseName": "水花飞溅",
    "cssStyle": "linear-gradient(15deg, #13547a 0%, #80d0c7 100%)"
  },
  {
    "id": "9",
    "name": "Arielles_Smile",
    "chineseName": "艾瑞尔的微笑",
    "cssStyle": "radial-gradient(circle 248px at center, #16d9e3 0%, #30c7ec 47%, #46aef7 100%)"
  },
  {
    "id": "10",
    "name": "Awesome_Pine",
    "chineseName": "出色的松树",
    "cssStyle": "linear-gradient(to top, #ebbba7 0%, #cfc7f8 100%)"
  },
  {
    "id": "11",
    "name": "Big_Mango",
    "chineseName": "大芒果",
    "cssStyle": "linear-gradient(to top, #c71d6f 0%, #d09693 100%)"
  },
  {
    "id": "12",
    "name": "Black_Sea",
    "chineseName": "黑海",
    "cssStyle": "linear-gradient(-225deg, #2CD8D5 0%, #6B8DD6 48%, #8E37D7 100%)"
  },
  {
    "id": "13",
    "name": "Blessing",
    "chineseName": "祝福",
    "cssStyle": "linear-gradient(to top, #fddb92 0%, #d1fdff 100%)"
  },
  {
    "id": "14",
    "name": "Burning_Spring",
    "chineseName": "燃烧的春天",
    "cssStyle": "linear-gradient(to top, #4fb576 0%, #44c489 30%, #28a9ae 46%, #28a2b7 59%, #4c7788 71%, #6c4f63 86%, #432c39 100%)"
  },
  {
    "id": "15",
    "name": "Cheerful_Caramel",
    "chineseName": "快乐焦糖",
    "cssStyle": "linear-gradient(to top, #e6b980 0%, #eacda3 100%)"
  },
  {
    "id": "16",
    "name": "Child_Care",
    "chineseName": "儿童护理",
    "cssStyle": "linear-gradient(-20deg, #f794a4 0%, #fdd6bd 100%)"
  },
  {
    "id": "17",
    "name": "Clean_Mirror",
    "chineseName": "清洁镜子",
    "cssStyle": "linear-gradient(45deg, #93a5cf 0%, #e4efe9 100%)"
  },
  {
    "id": "18",
    "name": "Cloudy_Apple",
    "chineseName": "多云的苹果",
    "cssStyle": "linear-gradient(to top, #f3e7e9 0%, #e3eeff 99%, #e3eeff 100%)"
  },
  {
    "id": "19",
    "name": "Cloudy_Knoxville",
    "chineseName": "多云的诺克斯维尔",
    "cssStyle": "linear-gradient(120deg, #fdfbfb 0%, #ebedee 100%)"
  },
  {
    "id": "20",
    "name": "Cold_Evening",
    "chineseName": "寒冷的夜晚",
    "cssStyle": "linear-gradient(to top, #0c3483 0%, #a2b6df 100%, #6b8cce 100%, #a2b6df 100%)"
  },
  {
    "id": "21",
    "name": "Colorful_Peach",
    "chineseName": "多彩的桃子",
    "cssStyle": "linear-gradient(to right, #ed6ea0 0%, #ec8c69 100%)"
  },
  {
    "id": "22",
    "name": "Confident_Cloud",
    "chineseName": "自信的云",
    "cssStyle": "linear-gradient(to top, #dad4ec 0%, #dad4ec 1%, #f3e7e9 100%)"
  },
  {
    "id": "23",
    "name": "Crystal_River",
    "chineseName": "水晶河",
    "cssStyle": "linear-gradient(-225deg, #22E1FF 0%, #1D8FE1 48%, #625EB1 100%)"
  },
  {
    "id": "24",
    "name": "Crystalline",
    "chineseName": "水晶质",
    "cssStyle": "linear-gradient(-20deg, #00cdac 0%, #8ddad5 100%)"
  },
  {
    "id": "25",
    "name": "Deep_Blue",
    "chineseName": "深蓝",
    "cssStyle": "linear-gradient(120deg, #e0c3fc 0%, #8ec5fc 100%)"
  },
  {
    "id": "26",
    "name": "Deep_Blue",
    "chineseName": "深蓝",
    "cssStyle": "linear-gradient(to right, #6a11cb 0%, #2575fc 100%)"
  },
  {
    "id": "27",
    "name": "Deep_Relief",
    "chineseName": "深层舒缓",
    "cssStyle": "linear-gradient(-225deg, #7085B6 0%, #87A7D9 50%, #DEF3F8 100%)"
  },
  {
    "id": "28",
    "name": "Dense_Water",
    "chineseName": "密集的水",
    "cssStyle": "linear-gradient(to right, #3ab5b0 0%, #3d99be 31%, #56317a 100%)"
  },
  {
    "id": "29",
    "name": "Desert_Hump",
    "chineseName": "沙漠驼峰",
    "cssStyle": "linear-gradient(to top, #c79081 0%, #dfa579 100%)"
  },
  {
    "id": "30",
    "name": "Dirty_Beauty",
    "chineseName": "脏污的美丽",
    "cssStyle": "linear-gradient(to top, #6a85b6 0%, #bac8e0 100%)"
  },
  {
    "id": "31",
    "name": "Dusty_Grass",
    "chineseName": "尘土飞扬的草地",
    "cssStyle": "linear-gradient(120deg, #d4fc79 0%, #96e6a1 100%)"
  },
  {
    "id": "32",
    "name": "Eternal_Constance",
    "chineseName": "永恒的恒心",
    "cssStyle": "linear-gradient(to top, #09203f 0%, #537895 100%)"
  },
  {
    "id": "33",
    "name": "Everlasting_Sky",
    "chineseName": "永恒的天空",
    "cssStyle": "linear-gradient(135deg, #fdfcfb 0%, #e2d1c3 100%)"
  },
  {
    "id": "34",
    "name": "Fabled_Sunset",
    "chineseName": "传说中的日落",
    "cssStyle": "linear-gradient(-225deg, #231557 0%, #44107A 29%, #FF1361 67%, #FFF800 100%)"
  },
  {
    "id": "35",
    "name": "Faraway_River",
    "chineseName": "遥远的河流",
    "cssStyle": "linear-gradient(-20deg, #6e45e2 0%, #88d3ce 100%)"
  },
  {
    "id": "36",
    "name": "February_Ink",
    "chineseName": "二月墨水",
    "cssStyle": "linear-gradient(to top, #accbee 0%, #e7f0fd 100%)"
  },
  {
    "id": "37",
    "name": "Fly_High",
    "chineseName": "高飞",
    "cssStyle": "linear-gradient(to top, #48c6ef 0%, #6f86d6 100%)"
  },
  {
    "id": "38",
    "name": "Flying_Lemon",
    "chineseName": "飞翔的柠檬",
    "cssStyle": "linear-gradient(60deg, #64b3f4 0%, #c2e59c 100%)"
  },
  {
    "id": "39",
    "name": "Forest_Inei",
    "chineseName": "森林伊内",
    "cssStyle": "linear-gradient(to top, #df89b5 0%, #bfd9fe 100%)"
  },
  {
    "id": "40",
    "name": "Fresh_Milk",
    "chineseName": "新鲜牛奶",
    "cssStyle": "linear-gradient(to top, #feada6 0%, #f5efef 100%)"
  },
  {
    "id": "41",
    "name": "Fresh_Oasis",
    "chineseName": "新鲜绿洲",
    "cssStyle": "linear-gradient(-225deg, #7DE2FC 0%, #B9B6E5 100%)"
  },
  {
    "id": "42",
    "name": "Frozen_Berry",
    "chineseName": "冷冻浆果",
    "cssStyle": "linear-gradient(to top, #e8198b 0%, #c7eafd 100%)"
  },
  {
    "id": "43",
    "name": "Frozen_Dreams",
    "chineseName": "冻结的梦想",
    "cssStyle": "linear-gradient(to top, #fdcbf1 0%, #fdcbf1 1%, #e6dee9 100%)"
  },
  {
    "id": "44",
    "name": "Frozen_Heat",
    "chineseName": "冻结的热量",
    "cssStyle": "linear-gradient(-225deg, #FF057C 0%, #7C64D5 48%, #4CC3FF 100%)"
  },
  {
    "id": "45",
    "name": "Fruit_Blend",
    "chineseName": "水果混合",
    "cssStyle": "linear-gradient(to right, #f9d423 0%, #ff4e50 100%)"
  },
  {
    "id": "46",
    "name": "Gagarin_View",
    "chineseName": "加加林视角",
    "cssStyle": "linear-gradient(-225deg, #69EACB 0%, #EACCF8 48%, #6654F1 100%)"
  },
  {
    "id": "47",
    "name": "Gentle_Care",
    "chineseName": "温柔的护理",
    "cssStyle": "linear-gradient(to right, #ffc3a0 0%, #ffafbd 100%)"
  },
  {
    "id": "48",
    "name": "Glass_Water",
    "chineseName": "玻璃水",
    "cssStyle": "linear-gradient(to top, #dfe9f3 0%, white 100%)"
  },
  {
    "id": "49",
    "name": "Grass_Shampoo",
    "chineseName": "草本洗发水",
    "cssStyle": "linear-gradient(-225deg, #DFFFCD 0%, #90F9C4 48%, #39F3BB 100%)"
  },
  {
    "id": "50",
    "name": "Great_Whale",
    "chineseName": "伟大的鲸鱼",
    "cssStyle": "linear-gradient(to top, #a3bded 0%, #6991c7 100%)"
  },
  {
    "id": "51",
    "name": "Grown_Early",
    "chineseName": "早熟",
    "cssStyle": "linear-gradient(to top, #0ba360 0%, #3cba92 100%)"
  },
  {
    "id": "52",
    "name": "Happy_Acid",
    "chineseName": "快乐酸",
    "cssStyle": "linear-gradient(to top, #37ecba 0%, #72afd3 100%)"
  },
  {
    "id": "53",
    "name": "Happy_Fisher",
    "chineseName": "快乐的渔夫",
    "cssStyle": "linear-gradient(120deg, #89f7fe 0%, #66a6ff 100%)"
  },
  {
    "id": "54",
    "name": "Happy_Memories",
    "chineseName": "快乐的回忆",
    "cssStyle": "linear-gradient(-60deg, #ff5858 0%, #f09819 100%)"
  },
  {
    "id": "55",
    "name": "Happy_Unicorn",
    "chineseName": "快乐的独角兽",
    "cssStyle": "linear-gradient(to top, #b3ffab 0%, #12fff7 100%)"
  },
  {
    "id": "56",
    "name": "Healthy_Water",
    "chineseName": "健康的水",
    "cssStyle": "linear-gradient(60deg, #96deda 0%, #50c9c3 100%)"
  },
  {
    "id": "57",
    "name": "Heaven_Peach",
    "chineseName": "天堂桃子",
    "cssStyle": "linear-gradient(to top, #d9afd9 0%, #97d9e1 100%)"
  },
  {
    "id": "58",
    "name": "Heavy_Rain",
    "chineseName": "大雨",
    "cssStyle": "linear-gradient(to top, #cfd9df 0%, #e2ebf0 100%)"
  },
  {
    "id": "59",
    "name": "Hidden_Jaguar",
    "chineseName": "隐藏的美洲虎",
    "cssStyle": "linear-gradient(to top, #0fd850 0%, #f9f047 100%)"
  },
  {
    "id": "60",
    "name": "High_Flight",
    "chineseName": "高飞",
    "cssStyle": "linear-gradient(to right, #0acffe 0%, #495aff 100%)"
  },
  {
    "id": "61",
    "name": "Itmeo_Branding",
    "chineseName": "Itmeo 品牌",
    "cssStyle": "linear-gradient(180deg, #2af598 0%, #009efd 100%)"
  },
  {
    "id": "62",
    "name": "Japan_Blush",
    "chineseName": "日本腮红",
    "cssStyle": "linear-gradient(-20deg, #ddd6f3 0%, #faaca8 100%, #faaca8 100%)"
  },
  {
    "id": "63",
    "name": "Juicy_Cake",
    "chineseName": "多汁的蛋糕",
    "cssStyle": "linear-gradient(to top, #e14fad 0%, #f9d423 100%)"
  },
  {
    "id": "64",
    "name": "Juicy_Peach",
    "chineseName": "多汁的桃子",
    "cssStyle": "linear-gradient(to right, #ffecd2 0%, #fcb69f 100%)"
  },
  {
    "id": "65",
    "name": "Jungle_Day",
    "chineseName": "丛林日",
    "cssStyle": "linear-gradient(45deg, #8baaaa 0%, #ae8b9c 100%)"
  },
  {
    "id": "66",
    "name": "Kind_Steel",
    "chineseName": "亲切的钢铁",
    "cssStyle": "linear-gradient(-20deg, #e9defa 0%, #fbfcdb 100%)"
  },
  {
    "id": "67",
    "name": "Ladoga_Bottom",
    "chineseName": "拉多加底部",
    "cssStyle": "linear-gradient(to top, #ebc0fd 0%, #d9ded8 100%)"
  },
  {
    "id": "68",
    "name": "Lady_Lips",
    "chineseName": "女士嘴唇",
    "cssStyle": "linear-gradient(to top, #ff9a9e 0%, #fecfef 99%, #fecfef 100%)"
  },
  {
    "id": "69",
    "name": "Landing_Aircraft",
    "chineseName": "着陆飞机",
    "cssStyle": "linear-gradient(-225deg, #5D9FFF 0%, #B8DCFF 48%, #6BBBFF 100%)"
  },
  {
    "id": "70",
    "name": "Le_Cocktail",
    "chineseName": "Le 鸡尾酒",
    "cssStyle": "linear-gradient(45deg, #874da2 0%, #c43a30 100%)"
  },
  {
    "id": "71",
    "name": "Lemon_Gate",
    "chineseName": "柠檬门",
    "cssStyle": "linear-gradient(to top, #96fbc4 0%, #f9f586 100%)"
  },
  {
    "id": "72",
    "name": "Light_Blue",
    "chineseName": "浅蓝",
    "cssStyle": "linear-gradient(-225deg, #9EFBD3 0%, #57E9F2 48%, #45D4FB 100%)"
  },
  {
    "id": "73",
    "name": "Lily_Meadow",
    "chineseName": "百合草地",
    "cssStyle": "linear-gradient(-225deg, #65379B 0%, #886AEA 53%, #6457C6 100%)"
  },
  {
    "id": "74",
    "name": "Love_Kiss",
    "chineseName": "爱的吻",
    "cssStyle": "linear-gradient(to top, #ff0844 0%, #ffb199 100%)"
  },
  {
    "id": "75",
    "name": "Magic_Lake",
    "chineseName": "魔法湖",
    "cssStyle": "linear-gradient(to top, #d5dee7 0%, #ffafbd 0%, #c9ffbf 100%)"
  },
  {
    "id": "76",
    "name": "Magic_Ray",
    "chineseName": "魔法射线",
    "cssStyle": "linear-gradient(-225deg, #FF3CAC 0%, #562B7C 52%, #2B86C5 100%)"
  },
  {
    "id": "77",
    "name": "Malibu_Beach",
    "chineseName": "马里布海滩",
    "cssStyle": "linear-gradient(to right, #4facfe 0%, #00f2fe 100%)"
  },
  {
    "id": "78",
    "name": "Marble_Wall",
    "chineseName": "大理石墙",
    "cssStyle": "linear-gradient(to top, #bdc2e8 0%, #bdc2e8 1%, #e6dee9 100%)"
  },
  {
    "id": "79",
    "name": "Mars_Party",
    "chineseName": "火星派对",
    "cssStyle": "linear-gradient(to top, #5f72bd 0%, #9b23ea 100%)"
  },
  {
    "id": "80",
    "name": "Mean_Fruit",
    "chineseName": "恶意水果",
    "cssStyle": "linear-gradient(120deg, #fccb90 0%, #d57eeb 100%)"
  },
  {
    "id": "81",
    "name": "Midnight_Bloom",
    "chineseName": "午夜绽放",
    "cssStyle": "linear-gradient(-20deg, #2b5876 0%, #4e4376 100%)"
  },
  {
    "id": "82",
    "name": "Millennium_Pine",
    "chineseName": "千禧松",
    "cssStyle": "linear-gradient(to top, #50cc7f 0%, #f5d100 100%)"
  },
  {
    "id": "83",
    "name": "Mind_Crawl",
    "chineseName": "心灵爬行",
    "cssStyle": "linear-gradient(-225deg, #473B7B 0%, #3584A7 51%, #30D2BE 100%)"
  },
  {
    "id": "84",
    "name": "Mixed_Hopes",
    "chineseName": "混合希望",
    "cssStyle": "linear-gradient(to top, #c471f5 0%, #fa71cd 100%)"
  },
  {
    "id": "85",
    "name": "Mole_Hall",
    "chineseName": "鼹鼠大厅",
    "cssStyle": "linear-gradient(-20deg, #616161 0%, #9bc5c3 100%)"
  },
  {
    "id": "86",
    "name": "Morning_Salad",
    "chineseName": "早晨沙拉",
    "cssStyle": "linear-gradient(-225deg, #B7F8DB 0%, #50A7C2 100%)"
  },
  {
    "id": "87",
    "name": "Morpheus_Den",
    "chineseName": "摩尔普斯的巢穴",
    "cssStyle": "linear-gradient(to top, #30cfd0 0%, #330867 100%)"
  },
  {
    "id": "88",
    "name": "Mountain_Rock",
    "chineseName": "山岩",
    "cssStyle": "linear-gradient(to right, #868f96 0%, #596164 100%)"
  },
  {
    "id": "89",
    "name": "Near_Moon",
    "chineseName": "近月",
    "cssStyle": "linear-gradient(to top, #5ee7df 0%, #b490ca 100%)"
  },
  {
    "id": "90",
    "name": "Nega",
    "chineseName": "Nega",
    "cssStyle": "linear-gradient(45deg, #ee9ca7 0%, #ffdde1 100%)"
  },
  {
    "id": "91",
    "name": "New_Life",
    "chineseName": "新生活",
    "cssStyle": "linear-gradient(to right, #43e97b 0%, #38f9d7 100%)"
  },
  {
    "id": "92",
    "name": "New_Retrowave",
    "chineseName": "新复古波",
    "cssStyle": "linear-gradient(to top, #3b41c5 0%, #a981bb 49%, #ffc8a9 100%)"
  },
  {
    "id": "93",
    "name": "New_York",
    "chineseName": "纽约",
    "cssStyle": "linear-gradient(to top, #fff1eb 0%, #ace0f9 100%)"
  },
  {
    "id": "94",
    "name": "Night_Call",
    "chineseName": "夜晚呼叫",
    "cssStyle": "linear-gradient(-225deg, #AC32E4 0%, #7918F2 48%, #4801FF 100%)"
  },
  {
    "id": "95",
    "name": "Night_Fade",
    "chineseName": "夜晚褪色",
    "cssStyle": "linear-gradient(to top, #a18cd1 0%, #fbc2eb 100%)"
  },
  {
    "id": "96",
    "name": "Night_Party",
    "chineseName": "夜晚派对",
    "cssStyle": "linear-gradient(to top, #0250c5 0%, #d43f8d 100%)"
  },
  {
    "id": "97",
    "name": "Night_Sky",
    "chineseName": "夜晚天空",
    "cssStyle": "linear-gradient(to top, #1e3c72 0%, #1e3c72 1%, #2a5298 100%)"
  },
  {
    "id": "98",
    "name": "Norse_Beauty",
    "chineseName": "北欧美人",
    "cssStyle": "linear-gradient(to right, #ec77ab 0%, #7873f5 100%)"
  },
  {
    "id": "99",
    "name": "North_Miracle",
    "chineseName": "北方奇迹",
    "cssStyle": "linear-gradient(to right, #00dbde 0%, #fc00ff 100%)"
  },
  {
    "id": "100",
    "name": "October_Silence",
    "chineseName": "十月寂静",
    "cssStyle": "linear-gradient(-20deg, #b721ff 0%, #21d4fd 100%)"
  },
  {
    "id": "101",
    "name": "Old_Hat",
    "chineseName": "旧帽子",
    "cssStyle": "linear-gradient(to right, #e4afcb 0%, #b8cbb8 0%, #b8cbb8 0%, #e2c58b 30%, #c2ce9c 64%, #7edbdc 100%)"
  },
  {
    "id": "102",
    "name": "Orange_Juice",
    "chineseName": "橙汁",
    "cssStyle": "linear-gradient(-20deg, #fc6076 0%, #ff9a44 100%)"
  },
  {
    "id": "103",
    "name": "Over_Sun",
    "chineseName": "阳光普照",
    "cssStyle": "linear-gradient(60deg, #abecd6 0%, #fbed96 100%)"
  },
  {
    "id": "104",
    "name": "Palo_Alto",
    "chineseName": "帕洛阿尔托",
    "cssStyle": "linear-gradient(-60deg, #16a085 0%, #f4d03f 100%)"
  },
  {
    "id": "105",
    "name": "Party_Bliss",
    "chineseName": "派对极乐",
    "cssStyle": "linear-gradient(to top, #4481eb 0%, #04befe 100%)"
  },
  {
    "id": "106",
    "name": "Passionate_Bed",
    "chineseName": "热情的床",
    "cssStyle": "linear-gradient(to right, #ff758c 0%, #ff7eb3 100%)"
  },
  {
    "id": "107",
    "name": "Perfect_Blue",
    "chineseName": "完美蓝色",
    "cssStyle": "linear-gradient(-225deg, #3D4E81 0%, #5753C9 48%, #6E7FF3 100%)"
  },
  {
    "id": "108",
    "name": "Perfect_White",
    "chineseName": "完美白色",
    "cssStyle": "linear-gradient(-225deg, #E3FDF5 0%, #FFE6FA 100%)"
  },
  {
    "id": "109",
    "name": "Phoenix_Start",
    "chineseName": "凤凰起始",
    "cssStyle": "linear-gradient(to right, #f83600 0%, #f9d423 100%)"
  },
  {
    "id": "110",
    "name": "Plum_Bath",
    "chineseName": "李子浴",
    "cssStyle": "linear-gradient(to top, #cc208e 0%, #6713d2 100%)"
  },
  {
    "id": "111",
    "name": "Plum_Plate",
    "chineseName": "李子盘",
    "cssStyle": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
  },
  {
    "id": "112",
    "name": "Polite_Rumors",
    "chineseName": "礼貌的谣言",
    "cssStyle": "linear-gradient(to top, #a7a6cb 0%, #8989ba 52%, #8989ba 100%)"
  },
  {
    "id": "113",
    "name": "Premium_Dark",
    "chineseName": "高级暗色",
    "cssStyle": "linear-gradient(to right, #434343 0%, black 100%)"
  },
  {
    "id": "114",
    "name": "Premium_White",
    "chineseName": "高级白色",
    "cssStyle": "linear-gradient(to top, #d5d4d0 0%, #d5d4d0 1%, #eeeeec 31%, #efeeec 75%, #e9e9e7 100%)"
  },
  {
    "id": "115",
    "name": "Purple_Division",
    "chineseName": "紫色分部",
    "cssStyle": "linear-gradient(to top, #7028e4 0%, #e5b2ca 100%)"
  },
  {
    "id": "116",
    "name": "Rainy_Ashville",
    "chineseName": "雨天阿什维尔",
    "cssStyle": "linear-gradient(to top, #fbc2eb 0%, #a6c1ee 100%)"
  },
  {
    "id": "117",
    "name": "Rare_Wind",
    "chineseName": "稀有风",
    "cssStyle": "linear-gradient(to top, #a8edea 0%, #fed6e3 100%)"
  },
  {
    "id": "118",
    "name": "Red_Salvation",
    "chineseName": "红色救赎",
    "cssStyle": "linear-gradient(to top, #f43b47 0%, #453a94 100%)"
  },
  {
    "id": "119",
    "name": "Rich_Metal",
    "chineseName": "丰富金属",
    "cssStyle": "linear-gradient(to right, #d7d2cc 0%, #304352 100%)"
  },
  {
    "id": "120",
    "name": "Ripe_Malinka",
    "chineseName": "成熟玛琳卡",
    "cssStyle": "linear-gradient(120deg, #f093fb 0%, #f5576c 100%)"
  },
  {
    "id": "121",
    "name": "Risky_Concrete",
    "chineseName": "风险混凝土",
    "cssStyle": "linear-gradient(to top, #c4c5c7 0%, #dcdddf 52%, #ebebeb 100%)"
  },
  {
    "id": "122",
    "name": "Saint_Petersburg",
    "chineseName": "圣彼得堡",
    "cssStyle": "linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)"
  },
  {
    "id": "123",
    "name": "Salt_Mountain",
    "chineseName": "盐山",
    "cssStyle": "linear-gradient(-225deg, #FFFEFF 0%, #D7FFFE 100%)"
  },
  {
    "id": "124",
    "name": "Sand_Strike",
    "chineseName": "沙击",
    "cssStyle": "linear-gradient(to right, #c1c161 0%, #c1c161 0%, #d4d4b1 100%)"
  },
  {
    "id": "125",
    "name": "Sea_Lord",
    "chineseName": "海王",
    "cssStyle": "linear-gradient(-225deg, #2CD8D5 0%, #C5C1FF 56%, #FFBAC3 100%)"
  },
  {
    "id": "126",
    "name": "Sea_Strike",
    "chineseName": "海击",
    "cssStyle": "linear-gradient(-225deg, #77FFD2 0%, #6297DB 48%, #1EECFF 100%)"
  },
  {
    "id": "127",
    "name": "Seashore",
    "chineseName": "海滨",
    "cssStyle": "linear-gradient(to top, #209cff 0%, #68e0cf 100%)"
  },
  {
    "id": "128",
    "name": "Shady_Water",
    "chineseName": "阴暗的水",
    "cssStyle": "linear-gradient(to right, #74ebd5 0%, #9face6 100%)"
  },
  {
    "id": "129",
    "name": "Sharp_Blues",
    "chineseName": "锐利的蓝色",
    "cssStyle": "linear-gradient(to top, #00c6fb 0%, #005bea 100%)"
  },
  {
    "id": "130",
    "name": "Sharpeye_Eagle",
    "chineseName": "锐利眼鹰",
    "cssStyle": "linear-gradient(to top, #9890e3 0%, #b1f4cf 100%)"
  },
  {
    "id": "131",
    "name": "Shy_Rainbow",
    "chineseName": "害羞的彩虹",
    "cssStyle": "linear-gradient(to right, #eea2a2 0%, #bbc1bf 19%, #57c6e1 42%, #b49fda 79%, #7ac5d8 100%)"
  },
  {
    "id": "132",
    "name": "Sky_Glider",
    "chineseName": "天空滑翔机",
    "cssStyle": "linear-gradient(to top, #88d3ce 0%, #6e45e2 100%)"
  },
  {
    "id": "133",
    "name": "Sleepless_Night",
    "chineseName": "无眠之夜",
    "cssStyle": "linear-gradient(-225deg, #5271C4 0%, #B19FFF 48%, #ECA1FE 100%)"
  },
  {
    "id": "134",
    "name": "Smart_Indigo",
    "chineseName": "智能靛蓝",
    "cssStyle": "linear-gradient(to top, #b224ef 0%, #7579ff 100%)"
  },
  {
    "id": "135",
    "name": "Smiling_Rain",
    "chineseName": "微笑的雨",
    "cssStyle": "linear-gradient(-20deg, #dcb0ed 0%, #99c99c 100%)"
  },
  {
    "id": "136",
    "name": "Snow_Again",
    "chineseName": "再次下雪",
    "cssStyle": "linear-gradient(to top, #e6e9f0 0%, #eef1f5 100%)"
  },
  {
    "id": "137",
    "name": "Soft_Cherish",
    "chineseName": "柔软的珍爱",
    "cssStyle": "linear-gradient(to top, #dbdcd7 0%, #dddcd7 24%, #e2c9cc 30%, #e7627d 46%, #b8235a 59%, #801357 71%, #3d1635 84%, #1c1a27 100%)"
  },
  {
    "id": "138",
    "name": "Soft_Grass",
    "chineseName": "柔软的草地",
    "cssStyle": "linear-gradient(to top, #c1dfc4 0%, #deecdd 100%)"
  },
  {
    "id": "139",
    "name": "Soft_Lipstick",
    "chineseName": "柔软的口红",
    "cssStyle": "linear-gradient(-225deg, #B6CEE8 0%, #F578DC 100%)"
  },
  {
    "id": "140",
    "name": "Solid_Stone",
    "chineseName": "坚固的石头",
    "cssStyle": "linear-gradient(to right, #243949 0%, #517fa4 100%)"
  },
  {
    "id": "141",
    "name": "Space_Shift",
    "chineseName": "空间移位",
    "cssStyle": "linear-gradient(60deg, #3d3393 0%, #2b76b9 37%, #2cacd1 65%, #35eb93 100%)"
  },
  {
    "id": "142",
    "name": "Spiky_Naga",
    "chineseName": "尖锐的娜迦",
    "cssStyle": "linear-gradient(to top, #505285 0%, #585e92 12%, #65689f 25%, #7474b0 37%, #7e7ebb 50%, #8389c7 62%, #9795d4 75%, #a2a1dc 87%, #b5aee4 100%)"
  },
  {
    "id": "143",
    "name": "Spring_Warmth",
    "chineseName": "春天的温暖",
    "cssStyle": "linear-gradient(to top, #fad0c4 0%, #fad0c4 1%, #ffd1ff 100%)"
  },
  {
    "id": "144",
    "name": "Star_Wine",
    "chineseName": "星星酒",
    "cssStyle": "linear-gradient(to right, #b8cbb8 0%, #b8cbb8 0%, #b465da 0%, #cf6cc9 33%, #ee609c 66%, #ee609c 100%)"
  },
  {
    "id": "145",
    "name": "Strict_November",
    "chineseName": "严格的十一月",
    "cssStyle": "linear-gradient(-225deg, #CBBACC 0%, #2580B3 100%)"
  },
  {
    "id": "146",
    "name": "Strong_Bliss",
    "chineseName": "强烈的幸福",
    "cssStyle": "linear-gradient(to right, #f78ca0 0%, #f9748f 19%, #fd868c 60%, #fe9a8b 100%)"
  },
  {
    "id": "147",
    "name": "Strong_Stick",
    "chineseName": "坚固的棒",
    "cssStyle": "linear-gradient(to right, #a8caba 0%, #5d4157 100%)"
  },
  {
    "id": "148",
    "name": "Sugar_Lollipop",
    "chineseName": "糖果棒棒糖",
    "cssStyle": "linear-gradient(-225deg, #A445B2 0%, #D41872 52%, #FF0066 100%)"
  },
  {
    "id": "149",
    "name": "Summer_Games",
    "chineseName": "夏季游戏",
    "cssStyle": "linear-gradient(to right, #92fe9d 0%, #00c9ff 100%)"
  },
  {
    "id": "150",
    "name": "Sun_Veggie",
    "chineseName": "阳光蔬菜",
    "cssStyle": "linear-gradient(-225deg, #20E2D7 0%, #F9FEA5 100%)"
  },
  {
    "id": "151",
    "name": "Sunny_Morning",
    "chineseName": "阳光早晨",
    "cssStyle": "linear-gradient(120deg, #f6d365 0%, #fda085 100%)"
  },
  {
    "id": "152",
    "name": "Supreme_Sky",
    "chineseName": "至高无上的天空",
    "cssStyle": "linear-gradient(-225deg, #D4FFEC 0%, #57F2CC 48%, #4596FB 100%)"
  },
  {
    "id": "153",
    "name": "Sweet_Dessert",
    "chineseName": "甜蜜的甜点",
    "cssStyle": "linear-gradient(-225deg, #7742B2 0%, #F180FF 52%, #FD8BD9 100%)"
  },
  {
    "id": "154",
    "name": "Sweet_Period",
    "chineseName": "甜蜜时期",
    "cssStyle": "linear-gradient(to top, #3f51b1 0%, #5a55ae 13%, #7b5fac 25%, #8f6aae 38%, #a86aa4 50%, #cc6b8e 62%, #f18271 75%, #f3a469 87%, #f7c978 100%)"
  },
  {
    "id": "155",
    "name": "Teen_Notebook",
    "chineseName": "青少年笔记本",
    "cssStyle": "linear-gradient(to top, #9795f0 0%, #fbc8d4 100%)"
  },
  {
    "id": "156",
    "name": "Teen_Party",
    "chineseName": "青少年派对",
    "cssStyle": "linear-gradient(-225deg, #FF057C 0%, #8D0B93 50%, #321575 100%)"
  },
  {
    "id": "157",
    "name": "Tempting_Azure",
    "chineseName": "诱人的天蓝",
    "cssStyle": "linear-gradient(120deg, #84fab0 0%, #8fd3f4 100%)"
  },
  {
    "id": "158",
    "name": "True_Sunset",
    "chineseName": "真正的日落",
    "cssStyle": "linear-gradient(to right, #fa709a 0%, #fee140 100%)"
  },
  {
    "id": "159",
    "name": "Vicious_Stance",
    "chineseName": "凶猛的姿态",
    "cssStyle": "linear-gradient(60deg, #29323c 0%, #485563 100%)"
  },
  {
    "id": "160",
    "name": "Warm_Flame",
    "chineseName": "温暖的火焰",
    "cssStyle": "linear-gradient(45deg, #ff9a9e 0%, #fad0c4 99%, #fad0c4 100%)"
  },
  {
    "id": "161",
    "name": "Wide_Matrix",
    "chineseName": "宽广矩阵",
    "cssStyle": "linear-gradient(to top, #fcc5e4 0%, #fda34b 15%, #ff7882 35%, #c8699e 52%, #7046aa 71%, #0c1db8 87%, #020f75 100%)"
  },
  {
    "id": "162",
    "name": "Wild_Apple",
    "chineseName": "野苹果",
    "cssStyle": "linear-gradient(to top, #d299c2 0%, #fef9d7 100%)"
  },
  {
    "id": "163",
    "name": "Winter_Neva",
    "chineseName": "冬季涅瓦",
    "cssStyle": "linear-gradient(120deg, #a1c4fd 0%, #c2e9fb 100%)"
  },
  {
    "id": "164",
    "name": "Witch_Dance",
    "chineseName": "女巫舞蹈",
    "cssStyle": "linear-gradient(-225deg, #A8BFFF 0%, #884D80 100%)"
  },
  {
    "id": "165",
    "name": "Young_Grass",
    "chineseName": "年轻的草地",
    "cssStyle": "linear-gradient(to top, #9be15d 0%, #00e3ae 100%)"
  },
  {
    "id": "166",
    "name": "Young_Passion",
    "chineseName": "年轻的激情",
    "cssStyle": "linear-gradient(to right, #ff8177 0%, #ff867a 0%, #ff8c7f 21%, #f99185 52%, #cf556c 78%, #b12a5b 100%)"
  },
  {
    "id": "167",
    "name": "Zeus_Miracle",
    "chineseName": "宙斯奇迹",
    "cssStyle": "linear-gradient(to top, #cd9cf2 0%, #f6f3ff 100%)"
  }
];

// 处理渐变色数据，提取颜色点
const processGradientData = (data) => {
  return data.map(item => {
    // 提取CSS样式中的颜色点
    const cssStyle = item.cssStyle;

    // 使用更精确的正则表达式来匹配颜色值和百分比
    const colorStops = cssStyle.match(/(#[0-9A-Fa-f]{6}|#[0-9A-Fa-f]{3}|rgba?\([^)]+\)|[a-zA-Z]+)\s+(\d+%)/g);

    // 如果找到了颜色停止点，提取颜色值
    let colors = [];
    if (colorStops) {
      colors = colorStops.map(stop => {
        const parts = stop.match(/(#[0-9A-Fa-f]{6}|#[0-9A-Fa-f]{3}|rgba?\([^)]+\)|[a-zA-Z]+)\s+(\d+)%/);
        if (parts) {
          return parts[1];
        }
        return null;
      }).filter(color => color !== null);
    } else {
      // 如果没有找到颜色停止点，尝试匹配所有颜色值
      const allColors = cssStyle.match(/(#[0-9A-Fa-f]{6}|#[0-9A-Fa-f]{3}|rgba?\([^)]+\))/g);
      if (allColors) {
        colors = allColors;
      } else {
        // 如果仍然没有找到颜色值，尝试匹配颜色关键字
        const colorKeywords = cssStyle.match(/\b(red|green|blue|yellow|purple|cyan|magenta|lime|olive|navy|teal|aqua|fuchsia|silver|gray|maroon|white|black|orange|pink|brown|violet|indigo|gold|turquoise|lavender|beige|ivory|khaki|coral|salmon|tan|plum|orchid|thistle)\b/g);
        if (colorKeywords) {
          // 过滤掉可能的方向关键字
          colors = colorKeywords.filter(keyword =>
            !['to', 'top', 'bottom', 'left', 'right', 'at', 'center', 'linear', 'radial', 'gradient'].includes(keyword.toLowerCase())
          );
        }
      }
    }

    // 去重颜色点
    const uniqueColors = [...new Set(colors)];

    // 返回处理后的数据
    return {
      ...item,
      colors: uniqueColors,
      englishName: item.name.replace(/_/g, ' ')
    };
  });
};

// 导出处理后的渐变色数据
module.exports = {
  rawData: GRADIENT_DATA,
  processedData: processGradientData(GRADIENT_DATA)
};
