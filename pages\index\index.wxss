/**index.wxss**/
.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 32rpx 32rpx;
  box-sizing: border-box;
  height: 100%;
}

/* 顶部主要功能卡片 */
.main-cards {
  width: 100%;
  display: flex;
  gap: 24rpx;
  margin-top: 8rpx;
  margin-bottom: 24rpx;
  box-sizing: border-box;
}

.main-card {
  flex: 1;
  height: 180rpx;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.create-card {
  background-color: #00CC66;
  color: white;
}

.clothing-card {
  background-color: #4080FF;
  color: white;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.card-subtitle {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 工具部分 */
.tools-section {
  width: 100%;
  margin-top: 16rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-indicator {
  width: 6rpx;
  height: 28rpx;
  background-color: #00CC66;
  border-radius: 3rpx;
  margin-right: 12rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #191919;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  margin-bottom: 16rpx;
}

.tool-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.tool-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  margin-bottom: 12rpx;
  background-size: 50% 50%;
  background-position: center;
  background-repeat: no-repeat;
  transition: transform 0.2s ease;
}

.tool-item:active .tool-icon,
.navigator-hover .tool-icon {
  transform: scale(0.95);
}

/* 导航组件的hover样式 */
.navigator-hover {
  background-color: transparent;
  opacity: 0.9;
}

.palette-icon {
  background-color: #00CC66;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 100-16 8 8 0 000 16zm-5-8h2a3 3 0 016 0h2a5 5 0 00-10 0z'/%3E%3C/svg%3E");
}

.colorblind-icon {
  background-color: #4080FF;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z'/%3E%3C/svg%3E");
}

.contrast-icon {
  background-color: #f94346;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5-9h10v2H7z'/%3E%3C/svg%3E");
}

.gradient-icon {
  background-color: #a67cf3;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M2 12h2v9H2v-9zm18 0h2v9h-2v-9zm-4-9h2v18h-2V3zm-4 0h2v18h-2V3zM6 3h2v18H6V3z'/%3E%3C/svg%3E");
}

.wallpaper-icon {
  background-color: #FF9500;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2zm0 2v12h16V6H4zm7 9l-3 4h8l-2.5-3-1.5 2-1-3z'/%3E%3Ccircle cx='6.5' cy='8.5' r='1.5'/%3E%3C/svg%3E");
}

.converter-icon {
  background-color: #FF9500;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7z'/%3E%3Cpath d='M13 7h-2v5.414l3.293 3.293 1.414-1.414L13 11.586z'/%3E%3C/svg%3E");
}

.traditional-color-icon {
  background-color: #f94346;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z'/%3E%3Cpath d='M12 6l-6 6h12l-6-6zm0 2.83L14.17 11H9.83L12 8.83zM7 14h10v2H7z'/%3E%3C/svg%3E");
}

.ambient-light-icon {
  background-color: #FF6B8B;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 7c-2.76 0-5 2.24-5 5s2.24 5 5 5 5-2.24 5-5-2.24-5-5-5zm0 8c-1.65 0-3-1.35-3-3s1.35-3 3-3 3 1.35 3 3-1.35 3-3 3zm0-13C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z'/%3E%3C/svg%3E");
}

.about-icon {
  background-color: #4080FF;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z'/%3E%3C/svg%3E");
}

.tone-generator-icon {
  background-color: #FF9500;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9c.83 0 1.5-.67 1.5-1.5 0-.39-.15-.74-.39-1.01-.23-.26-.38-.61-.38-.99 0-.83.67-1.5 1.5-1.5H16c2.76 0 5-2.24 5-5 0-4.42-4.03-8-9-8zm-5.5 9c-.83 0-1.5-.67-1.5-1.5S5.67 9 6.5 9 8 9.67 8 10.5 7.33 12 6.5 12zm3-4C8.67 8 8 7.33 8 6.5S8.67 5 9.5 5s1.5.67 1.5 1.5S10.33 8 9.5 8zm5 0c-.83 0-1.5-.67-1.5-1.5S13.67 5 14.5 5s1.5.67 1.5 1.5S15.33 8 14.5 8zm3 4c-.83 0-1.5-.67-1.5-1.5S16.67 9 17.5 9s1.5.67 1.5 1.5-.67 1.5-1.5 1.5z'/%3E%3C/svg%3E");
}

.image-picker-icon {
  background-color: #00CC66;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7zm3-8h-2V7h-2v4H7v2h4v4h2v-4h4v-2z'/%3E%3C/svg%3E");
}

.skin-tone-icon {
  background-color: #00CC66;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Ccircle cx='12' cy='12' r='10'/%3E%3Ccircle cx='9' cy='10' r='1.5' fill='%2300CC66'/%3E%3Ccircle cx='15' cy='10' r='1.5' fill='%2300CC66'/%3E%3Cpath d='M8 15c0 2.21 1.79 4 4 4s4-1.79 4-4' stroke='%2300CC66' stroke-width='2' fill='none' stroke-linecap='round'/%3E%3C/svg%3E");
}

.tool-name {
  font-size: 24rpx;
  color: #333333;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

/* Hot角标容器 */
.tool-icon-container {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto 12rpx;
}

.tool-icon-container .tool-icon {
  width: 100%;
  height: 100%;
  margin: 0;
}

/* Hot角标样式 */
.hot-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: linear-gradient(135deg, #FF4757 0%, #FF3742 100%);
  color: white;
  font-size: 18rpx;
  font-weight: bold;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.4);
  z-index: 10;
  transform: scale(0.9);
  animation: hotPulse 2s ease-in-out infinite;
}

@keyframes hotPulse {
  0%, 100% {
    transform: scale(0.9);
  }
  50% {
    transform: scale(1);
  }
}






