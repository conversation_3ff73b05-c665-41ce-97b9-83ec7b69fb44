/* P04春日樱语色卡样式 - 按照PP\p04.html 1:1复刻 */

.p04-card-container {
  width: 800rpx;
  height: 1066rpx;
  position: relative;
  background-color: #FFFFFF;
  overflow: hidden;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

/* 上半部分 - 高度526rpx (1052/2132 * 1066) */
.p04-top-section {
  width: 100%;
  height: 526rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}

/* 标题样式 - 按P04位置 (424, 421) */
.p04-title {
  position: absolute;
  top: 188rpx; /* 421/2132 * 1066 */
  left: 212rpx; /* 424/1600 * 800 */
  font-size: 94rpx; /* 188/1600 * 800 */
  font-weight: 650;
  line-height: 1;
  white-space: nowrap;
}

/* 木片容器 - 按P04位置 (847) */
.p04-wood-chips {
  position: absolute;
  top: 424rpx; /* 847/2132 * 1066 */
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 155rpx; /* 310/1600 * 800 */
  transform: translateY(-50%); /* 垂直居中 */
}

/* 单个P04样式色块 - 按P04尺寸 406x406，旋转45度 */
.p04-wood-chip {
  width: 203rpx; /* 406/1600 * 800 */
  height: 203rpx;
  position: relative;
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  /* 按照P04样式旋转45度 */
  transform: rotate(45deg);
  /* 使用复杂的clip-path模拟P04的SVG形状 */
  clip-path: polygon(
    50% 0%,   /* 顶部 */
    70% 15%,  /* 右上 */
    85% 30%,
    100% 50%, /* 右侧 */
    85% 70%,
    70% 85%,  /* 右下 */
    50% 100%, /* 底部 */
    30% 85%,  /* 左下 */
    15% 70%,
    0% 50%,   /* 左侧 */
    15% 30%,
    30% 15%   /* 左上 */
  );
}

/* 备用方案：如果clip-path不支持，使用多个圆形重叠 */
.p04-wood-chip-fallback {
  width: 203rpx;
  height: 203rpx;
  position: relative;
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.p04-wood-chip-fallback::before,
.p04-wood-chip-fallback::after {
  content: '';
  position: absolute;
  width: 60%;
  height: 60%;
  border-radius: 50%;
  background-color: inherit;
}

.p04-wood-chip-fallback::before {
  top: -20%;
  left: 20%;
}

.p04-wood-chip-fallback::after {
  bottom: -20%;
  right: 20%;
}

/* 下半部分 - 高度540rpx */
.p04-bottom-section {
  width: 100%;
  height: 540rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* 颜色代码容器 - 按P04位置 (1337) */
.p04-color-codes {
  position: absolute;
  top: 144rpx; /* (1337-1066)/2132 * 1066 */
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 156rpx; /* 312/1600 * 800 */
}

/* 单个颜色代码 - 按P04样式 */
.p04-color-code {
  font-size: 21rpx; /* 42/1600 * 800 */
  color: #7D7D7D;
  font-weight: 650;
  font-family: 'Courier New', monospace;
  text-align: center;
}

/* 圆形色块容器 - 按P04位置 (1546) */
.p04-circles {
  position: absolute;
  top: 254rpx; /* (1546-1066)/2132 * 1066 */
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 156rpx; /* 312/1600 * 800 */
}

/* 单个P04样式圆形色块 - 按P04尺寸 202x202 */
.p04-circle {
  width: 101rpx; /* 202/1600 * 800 */
  height: 101rpx;
  position: relative;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  /* 使用clip-path模拟P04的复杂圆形SVG形状 */
  clip-path: polygon(
    50% 10%,  /* 顶部突出 */
    65% 20%,
    80% 35%,
    90% 50%,  /* 右侧突出 */
    80% 65%,
    65% 80%,
    50% 90%,  /* 底部突出 */
    35% 80%,
    20% 65%,
    10% 50%,  /* 左侧突出 */
    20% 35%,
    35% 20%
  );
}
