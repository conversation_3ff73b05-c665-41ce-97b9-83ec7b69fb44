/* pages/about/about.wxss */
.page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.container {
  display: flex;
  flex-direction: column;
  padding: 32rpx;
  box-sizing: border-box;
  flex: 1;
}

/* 内容部分 */
.section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #191919;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 6rpx;
  height: 28rpx;
  background-color: #00CC66;
  border-radius: 3rpx;
}

.section-content {
  padding: 16rpx 8rpx;
}

.description {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.8;
  text-align: justify;
  white-space: pre-wrap;
  letter-spacing: 0.5rpx;
}

/* 功能特点列表 */
.feature-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.feature-item {
  display: flex;
  align-items: flex-start;
}

.feature-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #00CC66;
  margin-top: 12rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.feature-text {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.6;
  flex: 1;
}



/* 去广告按钮 */
.ad-free-button {
  width: 100%;
  height: 88rpx;
  background-color: #ffffff;
  color: #00CC66;
  font-size: 28rpx;
  font-weight: 500;
  border: 2rpx solid #00CC66;
  border-radius: 44rpx;
  margin: 32rpx 0 16rpx 0;
  padding: 0;
  line-height: 88rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 204, 102, 0.1);
  transition: all 0.3s ease;
}

.ad-free-button::after {
  border: none;
}

.ad-free-button:active {
  background-color: #f0fdf4;
  transform: scale(0.98);
}

.ad-free-button-inner {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 联系作者按钮 */
.contact-button {
  width: 100%;
  height: 88rpx;
  background-color: #00CC66;
  color: white;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  border-radius: 44rpx;
  margin: 16rpx 0 32rpx 0;
  padding: 0;
  line-height: 88rpx;
}

.contact-button::after {
  border: none;
}

.contact-button-inner {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 去广告弹窗样式 */
.ad-free-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.25s ease-out;
  backdrop-filter: blur(2rpx);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.ad-free-container {
  width: 85%;
  max-width: 600rpx;
  background-color: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  animation: scaleIn 0.25s ease-out;
}

@keyframes scaleIn {
  from {
    transform: scale(0.95) translateY(20rpx);
    opacity: 0;
  }
  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

.ad-free-header {
  padding: 40rpx 40rpx 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
  position: relative;
}

.ad-free-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  flex: 1;
  text-align: center;
  letter-spacing: 0.5rpx;
}

.ad-free-close {
  font-size: 32rpx;
  color: #8a8a8a;
  line-height: 1;
  padding: 12rpx;
  margin: -12rpx;
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  background-color: transparent;
}

.ad-free-close:active {
  background-color: #f5f5f5;
  color: #666;
}

.ad-free-content {
  padding: 20rpx 40rpx 40rpx;
  background-color: #ffffff;
}

.ad-free-description {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 32rpx;
  text-align: left;
  letter-spacing: 0.3rpx;
}

.ad-free-input-container {
  margin-bottom: 32rpx;
}

.ad-free-input {
  width: 100%;
  height: 96rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 30rpx;
  color: #1a1a1a;
  background-color: #fafafa;
  box-sizing: border-box;
  transition: all 0.2s ease;
}

.ad-free-input:focus {
  border-color: #00CC66;
  background-color: #ffffff;
  box-shadow: 0 0 0 4rpx rgba(0, 204, 102, 0.1);
}

.ad-free-success {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 20rpx 0;
}

.success-icon {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #00CC66 0%, #00B359 100%);
  color: white;
  font-size: 52rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 204, 102, 0.3);
}

.success-text {
  font-size: 34rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16rpx;
  letter-spacing: 0.5rpx;
}

.success-description {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.6;
  letter-spacing: 0.3rpx;
}

.ad-free-footer {
  padding: 32rpx 40rpx 40rpx;
  display: flex;
  justify-content: space-between;
  background-color: #ffffff;
  gap: 24rpx;
}

.ad-free-cancel, .ad-free-confirm, .ad-free-deactivate, .ad-free-ok {
  flex: 1;
  padding: 24rpx 0;
  font-size: 30rpx;
  font-weight: 500;
  border-radius: 16rpx;
  text-align: center;
  transition: all 0.2s ease;
  line-height: 1.2;
  letter-spacing: 0.5rpx;
  border: none;
}

.ad-free-cancel, .ad-free-deactivate {
  background-color: #f5f5f5;
  color: #666666;
}

.ad-free-cancel:active, .ad-free-deactivate:active {
  background-color: #e8e8e8;
  transform: scale(0.98);
}

.ad-free-confirm, .ad-free-ok {
  background: linear-gradient(135deg, #00CC66 0%, #00B359 100%);
  color: #ffffff;
  box-shadow: 0 4rpx 16rpx rgba(0, 204, 102, 0.3);
}

.ad-free-confirm:active, .ad-free-ok:active {
  background: linear-gradient(135deg, #00B359 0%, #009A4D 100%);
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 204, 102, 0.4);
}


