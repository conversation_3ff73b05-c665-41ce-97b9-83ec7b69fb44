<!--pages/gradientWallpaper/gradientWallpaper.wxml-->
<view class="page">
  <swiper class="wallpaper-swiper" bindchange="onSwiperChange" vertical="true" duration="400" circular="true">
    <swiper-item wx:for="{{wallpapers}}" wx:key="id">
      <view class="wallpaper-item" style="background: {{item.cssStyle}}; padding-top: {{navHeight}}px;">
        <!-- 自定义导航栏 -->
        <view class="custom-nav-container">
          <!-- 状态栏占位 -->
          <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
          <!-- 导航栏内容 -->
          <view class="custom-nav">
            <view class="nav-left">
              <view class="nav-back-btn" catch:tap="navigateBack">
                <view class="nav-back-arrow"></view>
              </view>
            </view>
            <view class="nav-center">
              <text class="nav-title">渐变色彩壁纸</text>
            </view>
            <view class="nav-right"></view>
          </view>
        </view>

        <view class="wallpaper-info">
          <!-- 左下靠上布局 -->
          <view class="left-bottom-top-layout">
            <!-- 名称区域 -->
            <view class="wallpaper-title">
              <view class="wallpaper-name">{{item.chineseName}}</view>
              <view class="wallpaper-english-name">{{item.englishName}}</view>
            </view>

            <!-- 颜色点放在名称下方 -->
            <view class="wallpaper-colors">
              <!-- 使用wx:for循环，但在每个颜色点后添加箭头，最后一个除外 -->
              <block wx:for="{{item.colors}}" wx:for-index="colorIndex" wx:for-item="color" wx:key="*this">
                <view class="color-item">
                  <view class="color-dot" style="background-color: {{color}};"></view>
                </view>
                <!-- 如果不是最后一个颜色点，则添加箭头 -->
                <view wx:if="{{colorIndex < item.colors.length - 1}}" class="color-arrow">→</view>
              </block>
            </view>
          </view>

          <!-- CSS样式区域已移除 -->
        </view>
      </view>
    </swiper-item>
  </swiper>

  <view class="controls">
    <!-- 上下滑动提示已移除 -->
    <view class="control-buttons">
      <button class="control-btn refresh-btn" bindtap="generateNewWallpaper">
        <view class="btn-icon refresh-icon"></view>
        <text>随机生成</text>
      </button>
      <button class="control-btn save-btn" bindtap="saveGradient">
        <view class="btn-icon save-icon"></view>
        <text>保存壁纸</text>
      </button>
    </view>
  </view>

  <!-- 保存成功提示 -->
  <view class="save-success {{showSaveSuccess ? 'show' : ''}}">
    <view class="success-icon"></view>
    <view class="success-text">壁纸已保存到相册</view>
  </view>

  <!-- 用于生成图片的canvas，设置为不可见 -->
  <canvas type="2d" id="gradient-canvas" class="hidden-canvas"></canvas>
</view>
