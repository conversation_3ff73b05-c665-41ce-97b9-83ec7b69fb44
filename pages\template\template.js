// pages/template/template.js
Page({
  data: {
    // 模式切换相关
    currentMode: 'image', // 'image' 图片主题色, 'custom' 自定义色卡
    currentCustomTemplateIndex: 0, // 当前自定义模板索引

    templates: [
      {
        id: 3,
        name: '色卡A (1:1)',
        selected: true,
        cropScale: '1:1',
        imagePath: '/images/templates/color_a.jpg'
      },
      {
        id: 4,
        name: '色卡B (1:1)',
        selected: false,
        cropScale: '1:1',
        imagePath: '/images/templates/color_b.jpg'
      },
      {
        id: 5,
        name: '色卡C (3:4)',
        selected: false,
        cropScale: '3:4',
        imagePath: '/images/templates/color_c.jpg'
      },
      {
        id: 6,
        name: '色卡D (3:4)',
        selected: false,
        cropScale: '3:4',
        imagePath: '/images/templates/color_d.jpg'
      },
      {
        id: 7,
        name: '色卡E (3:4)',
        selected: false,
        cropScale: '3:4',
        imagePath: '/images/templates/color_e.jpg'
      },
      {
        id: 8,
        name: '色卡F (16:9)',
        selected: false,
        cropScale: '16:9',
        imagePath: '/images/templates/color_f.jpg'
      },
      {
        id: 9,
        name: '色卡G (4:5)',
        selected: false,
        cropScale: '4:5',
        imagePath: '/images/templates/color_g.jpg'
      },
      {
        id: 10,
        name: '色卡H (9:16)',
        selected: false,
        cropScale: '9:16',
        imagePath: '/images/templates/color_h.jpg'
      },
      {
        id: 11,
        name: '色卡I (9:16)',
        selected: false,
        cropScale: '9:16',
        imagePath: '/images/templates/color_i.jpg'
      },
      {
        id: 999, // 使用一个特殊ID，确保不会与其他模板冲突
        name: '其他模板',
        selected: false,
        disabled: true, // 标记为禁用
        isComingSoon: true, // 标记为即将推出
        // 使用一个简单的灰色背景作为占位图
        imagePath: '/images/templates/color_a.jpg' // 使用现有图片作为占位
      }
    ],

    // 自定义色卡模板
    customTemplates: [
      {
        id: 101,
        name: '蜜桃汽水',
        selected: true,
        colors: ['#FFD9C5', '#FFB398', '#FF947A', '#FF708D'], // 新的默认4个颜色
        imagePath: '/images/templates/custom_template_1.jpg' // 需要创建预览图
      },
      {
        id: 102,
        name: '海盐气泡',
        selected: false,
        colors: ['#0B1F5E', '#1B3CBA', '#3A66E8', '#7AA3FF', '#BED8FF'], // 新的默认5个颜色
        imagePath: '/images/templates/custom_template_2.jpg' // 需要创建预览图
      },
      {
        id: 103,
        name: '落日漫旅',
        selected: false,
        colors: ['#FFC371', '#F9E0B7', '#C66767', '#8E7F3C'], // P03模板的4个颜色
        imagePath: '/images/templates/custom_template_3.jpg' // 需要创建预览图
      },
      {
        id: 104,
        name: '春日樱语',
        selected: false,
        colors: ['#F9DFE7', '#C2E0F5', '#E8D1F2', '#F8C8DC'], // P04模板的4个颜色
        imagePath: '/images/templates/custom_template_4.jpg' // 需要创建预览图
      }
    ]
  },

  onReady() {
    // 页面渲染完成
    console.log('onReady: 页面渲染完成');
  },






  // 切换模式
  switchMode: function(e) {
    const mode = e.currentTarget.dataset.mode;
    this.setData({
      currentMode: mode
    });

    // 重置选择状态
    if (mode === 'image') {
      // 重置图片模板选择状态
      const templates = this.data.templates.map(item => ({
        ...item,
        selected: item.id === 3 // 默认选择第一个模板
      }));
      this.setData({ templates });
    } else {
      // 重置自定义模板选择状态和滑动索引
      const customTemplates = this.data.customTemplates.map((item, index) => ({
        ...item,
        selected: index === 0 // 默认选择第一个自定义模板
      }));
      this.setData({
        customTemplates,
        currentCustomTemplateIndex: 0
      });
    }
  },

  // 自定义模板滑动切换
  onCustomTemplateChange: function(e) {
    const current = e.detail.current;
    const customTemplates = this.data.customTemplates.map((item, index) => ({
      ...item,
      selected: index === current
    }));

    this.setData({
      currentCustomTemplateIndex: current,
      customTemplates
    });
  },

  // 选择模板
  selectTemplate: function(e) {
    const id = parseInt(e.currentTarget.dataset.id);
    const mode = this.data.currentMode;

    if (mode === 'image') {
      // 图片主题色模式
      const clickedTemplate = this.data.templates.find(item => item.id == id);
      if (clickedTemplate && clickedTemplate.isComingSoon) {
        wx.showToast({
          title: '更多模板开发中，敬请期待...',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      const templates = this.data.templates.map(function(item) {
        return {
          id: item.id,
          name: item.name,
          cropScale: item.cropScale,
          imagePath: item.imagePath,
          selected: item.id === id,
          disabled: item.disabled,
          isComingSoon: item.isComingSoon
        };
      });
      this.setData({ templates: templates });
    } else {
      // 自定义色卡模式
      const customTemplates = this.data.customTemplates.map(function(item) {
        return {
          id: item.id,
          name: item.name,
          colors: item.colors,
          imagePath: item.imagePath,
          selected: item.id === id
        };
      });
      this.setData({ customTemplates: customTemplates });
    }
  },

  // 下一步操作
  goToNext: function() {
    const mode = this.data.currentMode;

    if (mode === 'image') {
      this.goToCrop();
    } else {
      this.goToCustomEditor();
    }
  },

  // 选择图片并直接裁剪
  goToCrop: function() {
    // 获取选中的模板
    var that = this;
    var selectedTemplate = null;

    // 使用循环代替find方法
    for (var i = 0; i < this.data.templates.length; i++) {
      if (this.data.templates[i].selected) {
        selectedTemplate = this.data.templates[i];
        break;
      }
    }

    if (!selectedTemplate) {
      wx.showToast({
        title: '请选择模板',
        icon: 'none'
      });
      return;
    }

    // 调用微信选择图片API
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: function(res) {
        var tempFilePath = res.tempFiles[0].tempFilePath;

        // 设置裁剪比例
        var cropScale = selectedTemplate.cropScale || '1:1'; // 使用模板中定义的裁剪比例

        console.log('选择模板:', selectedTemplate.name, '裁剪比例:', cropScale);

        // 直接调用裁剪接口
        wx.cropImage({
          src: tempFilePath,
          cropScale: cropScale, // 根据模板设置裁剪比例
          success: function(cropRes) {
            // 裁剪成功后直接跳转到取色页面
            // 使用 navigator 组件的方式跳转，但由于这里是 JS 代码，仍需使用 wx.navigateTo
            wx.navigateTo({
              url: '/pages/colorPicker/colorPicker?imagePath=' + encodeURIComponent(cropRes.tempFilePath) + '&templateId=' + selectedTemplate.id,
            });
          },
          fail: function(err) {
            wx.showToast({
              title: '裁剪失败',
              icon: 'none'
            });
            console.error('裁剪失败:', err);

            // 裁剪失败时，尝试直接使用原图
            wx.showModal({
              title: '提示',
              content: '图片裁剪失败，是否直接使用原图？',
              success: function(res) {
                if (res.confirm) {
                  // 用户点击确定，直接使用原图
                  // 使用 navigator 组件的方式跳转，但由于这里是 JS 代码，仍需使用 wx.navigateTo
                  wx.navigateTo({
                    url: '/pages/colorPicker/colorPicker?imagePath=' + encodeURIComponent(tempFilePath) + '&templateId=' + selectedTemplate.id,
                  });
                }
              }
            });
          }
        });
      }
    });
  },

  // 跳转到自定义色卡编辑页面
  goToCustomEditor: function() {
    // 获取当前滑动位置的模板
    const currentIndex = this.data.currentCustomTemplateIndex;
    const selectedTemplate = this.data.customTemplates[currentIndex];

    if (!selectedTemplate) {
      wx.showToast({
        title: '请选择模板',
        icon: 'none'
      });
      return;
    }

    // 跳转到自定义色卡编辑页面
    wx.navigateTo({
      url: '/pages/customColorEditor/customColorEditor?templateId=' + selectedTemplate.id + '&colors=' + encodeURIComponent(JSON.stringify(selectedTemplate.colors))
    });
  }
})
