<!--pages/contrastChecker/contrastChecker.wxml-->
<view class="page">

  <view class="container">
    <!-- 文本预览 -->
    <view class="section preview-section">
      <view class="section-title">对比度预览</view>
      <!-- 移除字体粗细控制 -->
      <view
        class="text-preview"
        style="color: {{foregroundColor}}; background-color: {{backgroundColor}}; white-space: pre-wrap;"
      >
        <text class="normal-text">

<text class="bold-text">这是一段普通的示例文本，用来展示对比度效果。</text>

AA级：要求小文本与背景的对比度至少为4.5:1，大文本与背景的对比度至少为3:1。
AAA级：要求小文本与背景的对比度至少为7:1，大文本与背景的对比度至少为4.5:1。</text>
      </view>
    </view>

    <!-- 颜色选择部分 -->
    <view class="section color-section">
      <view class="section-title">颜色选择</view>

      <view class="color-cards">
        <view class="color-cards-row">
          <!-- 前景色卡片 -->
          <view class="color-card {{activeColorType === 'foreground' ? 'active' : ''}}" bindtap="setActiveColorType" data-type="foreground">
            <view class="color-card-header">
              <view class="color-card-title">前景色</view>
              <view class="color-card-copy" catchtap="copyColorValue" data-color="{{foregroundColor}}" data-item="foreground">
                <text class="copy-icon">复制</text>
                <view class="copy-indicator {{copiedItem === 'foreground' ? 'show' : ''}}">
                  <view class="copy-indicator-text">已复制</view>
                </view>
              </view>
            </view>

            <view class="color-card-content">
              <view class="color-preview-large" style="background-color: {{foregroundColor}};" catchtap="showForegroundPicker">
                <view class="color-preview-icon">+</view>
              </view>

              <view class="color-input-group">
                <input class="color-input" value="{{foregroundColor}}" bindinput="onForegroundInput" />
                <view class="color-actions">
                  <button class="action-btn primary" catchtap="showForegroundPicker">选择</button>
                  <button class="action-btn" catchtap="randomColor" data-target="foreground">随机</button>
                </view>
              </view>
            </view>
          </view>

          <!-- 交换按钮 -->
          <view class="swap-btn-container">
            <button class="swap-btn" bindtap="swapColors">
              <text class="swap-icon">⇆</text>
            </button>
          </view>

          <!-- 背景色卡片 -->
          <view class="color-card {{activeColorType === 'background' ? 'active' : ''}}" bindtap="setActiveColorType" data-type="background">
            <view class="color-card-header">
              <view class="color-card-title">背景色</view>
              <view class="color-card-copy" catchtap="copyColorValue" data-color="{{backgroundColor}}" data-item="background">
                <text class="copy-icon">复制</text>
                <view class="copy-indicator {{copiedItem === 'background' ? 'show' : ''}}">
                  <view class="copy-indicator-text">已复制</view>
                </view>
              </view>
            </view>

            <view class="color-card-content">
              <view class="color-preview-large" style="background-color: {{backgroundColor}};" catchtap="showBackgroundPicker">
                <view class="color-preview-icon">+</view>
              </view>

              <view class="color-input-group">
                <input class="color-input" value="{{backgroundColor}}" bindinput="onBackgroundInput" />
                <view class="color-actions">
                  <button class="action-btn primary" catchtap="showBackgroundPicker">选择</button>
                  <button class="action-btn" catchtap="randomColor" data-target="background">随机</button>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 对比度结果 -->
    <view class="section result-section">
      <view class="section-title">对比度结果</view>
      <view class="contrast-result">
        <view class="contrast-ratio-container">
          <view class="contrast-level-indicator {{contrastLevel}}"></view>
          <view class="contrast-ratio-value">
            <text class="contrast-number">{{contrastRatio}}</text>
            <text class="contrast-symbol">:1</text>
          </view>
          <view class="contrast-level-text {{contrastLevel}}">
            {{contrastLevel === 'excellent' ? '优秀' :
              contrastLevel === 'good' ? '良好' :
              contrastLevel === 'fair' ? '一般' : '较差'}}
          </view>
        </view>
        <button class="copy-btn" bindtap="copyResult" data-item="result">
          复制结果
          <view class="copy-indicator {{copiedItem === 'result' ? 'show' : ''}}">
            <view class="copy-indicator-text">已复制</view>
          </view>
        </button>
      </view>
    </view>


  </view>

  <!-- 颜色选择器弹窗 - 前景色 -->
  <view class="color-picker-modal" wx:if="{{showForegroundPicker}}">
    <view class="color-picker-container">
      <view class="color-picker-header">
        <view class="color-picker-title">选择前景色</view>
        <view class="color-picker-close" bindtap="hideColorPicker">×</view>
      </view>
      <!-- 集成颜色选择器组件 -->
      <color-picker
        color="{{foregroundColor}}"
        bindchange="onForegroundColorChange"
        bindconfirm="onForegroundColorConfirm"
        bindcancel="hideColorPicker"
      ></color-picker>
    </view>
  </view>

  <!-- 颜色选择器弹窗 - 背景色 -->
  <view class="color-picker-modal" wx:if="{{showBackgroundPicker}}">
    <view class="color-picker-container">
      <view class="color-picker-header">
        <view class="color-picker-title">选择背景色</view>
        <view class="color-picker-close" bindtap="hideColorPicker">×</view>
      </view>
      <!-- 集成颜色选择器组件 -->
      <color-picker
        color="{{backgroundColor}}"
        bindchange="onBackgroundColorChange"
        bindconfirm="onBackgroundColorConfirm"
        bindcancel="hideColorPicker"
      ></color-picker>
    </view>
  </view>
</view>
