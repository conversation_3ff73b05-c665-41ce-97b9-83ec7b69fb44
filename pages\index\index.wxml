<!--index.wxml - 优化后的语义化结构-->
<view class="page">
  <view class="container" aria-role="main">
    <!-- 顶部主要功能卡片 -->
    <view class="main-cards" aria-role="region" aria-label="主要功能">
      <navigator url="/pages/template/template" open-type="navigate" hover-class="navigator-hover" class="main-card create-card" aria-role="button" aria-label="图片色卡制作">
        <view class="card-title" aria-role="heading">图片色卡制作</view>
        <view class="card-subtitle">上传图片生成配色方案</view>
      </navigator>
      <navigator url="/pages/clothingColorTool/clothingColorTool" open-type="navigate" hover-class="navigator-hover" class="main-card clothing-card" aria-role="button" aria-label="穿搭配色">
        <view class="card-title" aria-role="heading">穿搭配色</view>
        <view class="card-subtitle">设计你的穿搭配色方案</view>
      </navigator>
    </view>

    <!-- 色彩小工具部分 -->
    <view class="tools-section" aria-role="region" aria-label="色彩小工具">
      <view class="section-header">
        <view class="section-indicator"></view>
        <text class="section-title" aria-role="heading">色彩小工具</text>
      </view>
      <view class="tools-grid" aria-role="list">
        <navigator url="/pages/colorPalette/colorPalette" open-type="navigate" hover-class="navigator-hover" class="tool-item" aria-role="listitem" aria-label="配色方案工具">
          <view class="tool-icon palette-icon" aria-hidden="true"></view>
          <view class="tool-name">配色方案</view>
        </navigator>
        <navigator url="/pages/colorConverter/colorConverter" open-type="navigate" hover-class="navigator-hover" class="tool-item" aria-role="listitem" aria-label="色值转换器工具">
          <view class="tool-icon-container">
            <view class="tool-icon converter-icon" aria-hidden="true"></view>
            <view class="hot-badge">Hot</view>
          </view>
          <view class="tool-name">色值转换器</view>
        </navigator>
        <navigator url="/pages/gradientGenerator/gradientGenerator" open-type="navigate" hover-class="navigator-hover" class="tool-item" aria-role="listitem" aria-label="渐变生成器工具">
          <view class="tool-icon gradient-icon" aria-hidden="true"></view>
          <view class="tool-name">渐变生成器</view>
        </navigator>
        <navigator url="/pages/contrastChecker/contrastChecker" open-type="navigate" hover-class="navigator-hover" class="tool-item" aria-role="listitem" aria-label="对比度检测器工具">
          <view class="tool-icon contrast-icon" aria-hidden="true"></view>
          <view class="tool-name">对比度检测</view>
        </navigator>
        <navigator url="/pages/toneGenerator/toneGenerator" open-type="navigate" hover-class="navigator-hover" class="tool-item" aria-role="listitem" aria-label="色调生成器工具">
          <view class="tool-icon tone-generator-icon" aria-hidden="true"></view>
          <view class="tool-name">色调生成器</view>
        </navigator>
        <navigator url="/pages/colorQuery/colorQuery" open-type="navigate" hover-class="navigator-hover" class="tool-item" aria-role="listitem" aria-label="中国传统色工具">
          <view class="tool-icon traditional-color-icon" aria-hidden="true"></view>
          <view class="tool-name">中国传统色</view>
        </navigator>
        <navigator url="/pages/colorblindSimulator/colorblindSimulator" open-type="navigate" hover-class="navigator-hover" class="tool-item" aria-role="listitem" aria-label="色盲模拟器工具">
          <view class="tool-icon colorblind-icon" aria-hidden="true"></view>
          <view class="tool-name">色盲模拟器</view>
        </navigator>
        <navigator url="/pages/imageColorPicker/imageColorPicker" open-type="navigate" hover-class="navigator-hover" class="tool-item" aria-role="listitem" aria-label="图片取色器工具">
          <view class="tool-icon-container">
            <view class="tool-icon image-picker-icon" aria-hidden="true"></view>
            <view class="hot-badge">Hot</view>
          </view>
          <view class="tool-name">图片取色器</view>
        </navigator>
      </view>
    </view>

    <!-- 其他小工具部分 -->
    <view class="tools-section" aria-role="region" aria-label="其他小工具">
      <view class="section-header">
        <view class="section-indicator"></view>
        <text class="section-title" aria-role="heading">其他小工具</text>
      </view>
      <view class="tools-grid" aria-role="list">
        <navigator url="/pages/ambientLight/ambientLight" open-type="navigate" hover-class="navigator-hover" class="tool-item" aria-role="listitem" aria-label="补光灯工具">
          <view class="tool-icon-container">
            <view class="tool-icon ambient-light-icon" aria-hidden="true"></view>
            <view class="hot-badge">Hot</view>
          </view>
          <view class="tool-name">补光灯</view>
        </navigator>
        <navigator url="/pages/gradientWallpaper/gradientWallpaper" open-type="navigate" hover-class="navigator-hover" class="tool-item" aria-role="listitem" aria-label="渐变壁纸工具">
          <view class="tool-icon wallpaper-icon" aria-hidden="true"></view>
          <view class="tool-name">渐变壁纸</view>
        </navigator>
        <navigator url="/pages/skinToneTest/skinToneTest" open-type="navigate" hover-class="navigator-hover" class="tool-item" aria-role="listitem" aria-label="肤色自测工具">
          <view class="tool-icon-container">
            <view class="tool-icon skin-tone-icon" aria-hidden="true"></view>
            <view class="hot-badge">Hot</view>
          </view>
          <view class="tool-name">肤色自测</view>
        </navigator>
        <navigator url="/pages/about/about" open-type="navigate" hover-class="navigator-hover" class="tool-item" aria-role="listitem" aria-label="关于我们页面">
          <view class="tool-icon about-icon" aria-hidden="true"></view>
          <view class="tool-name">关于我们</view>
        </navigator>
      </view>
    </view>
  </view>
</view>
