<!--pages/colorDetail/colorDetail.wxml-->
<view class="page" style="background-color: {{hexValue}}; --text-color: {{textColor}}" bindtouchstart="handleTouchStart" bindtouchend="handleTouchEnd">

  <!-- 上滑提示 -->
  <view class="swipe-tip swipe-tip-top fade-in" wx:if="{{showNextTip}}">
    <text>滑动查看下一个颜色</text>
  </view>

  <!-- 颜色信息区域 -->
  <view class="color-info-container">
    <!-- 颜色名称 -->
    <view class="color-name">{{colorName}}</view>

    <!-- 颜色介绍 -->
    <view class="color-intro" wx:if="{{colorIntro}}">{{colorIntro}}</view>

    <!-- 颜色值列表 -->
    <view class="color-values">
      <view class="color-value-item" bindtap="copyColor" data-type="hex">
        <text class="color-text">HEX: {{hexValue}}</text>
      </view>

      <view class="color-value-item" bindtap="copyColor" data-type="rgb">
        <text class="color-text">RGB: {{rgbDisplay}}</text>
      </view>

      <view class="color-value-item" bindtap="copyColor" data-type="hsb">
        <text class="color-text">HSB: {{hsbDisplay}}</text>
      </view>

      <view class="color-value-item" bindtap="copyColor" data-type="lab">
        <text class="color-text">LAB: {{labDisplay}}</text>
      </view>

      <view class="color-value-item" bindtap="copyColor" data-type="cmyk">
        <text class="color-text">CMYK: {{cmykDisplay}}</text>
      </view>
    </view>
  </view>

  <!-- 下滑提示 -->
  <view class="swipe-tip swipe-tip-bottom fade-in" wx:if="{{showPrevTip}}">
    <text>滑动查看上一个颜色</text>
  </view>

  <!-- 底部工具栏 -->
  <view class="footer">
    <view class="page-indicator">{{currentIndex + 1}} / {{totalColors}}</view>

    <!-- 移除了右下角的3个圆圈按钮 -->
  </view>
</view>
