<!-- components/colorWheelCanvas/colorWheelCanvas.wxml -->
<wxs module="colorUtils">
  // HSL转HEX的WXS版本，供WXML使用
  function hslToHex(h, s, l) {
    h = h % 360;
    s = s / 100;
    l = l / 100;

    var c = (1 - Math.abs(2 * l - 1)) * s;
    var x = c * (1 - Math.abs((h / 60) % 2 - 1));
    var m = l - c / 2;
    var r = 0, g = 0, b = 0;

    if (h >= 0 && h < 60) {
      r = c; g = x; b = 0;
    } else if (h >= 60 && h < 120) {
      r = x; g = c; b = 0;
    } else if (h >= 120 && h < 180) {
      r = 0; g = c; b = x;
    } else if (h >= 180 && h < 240) {
      r = 0; g = x; b = c;
    } else if (h >= 240 && h < 300) {
      r = x; g = 0; b = c;
    } else {
      r = c; g = 0; b = x;
    }

    r = Math.round((r + m) * 255);
    g = Math.round((g + m) * 255);
    b = Math.round((b + m) * 255);

    function toHex(c) {
      var hex = c.toString(16);
      return hex.length == 1 ? "0" + hex : hex;
    }

    return "#" + toHex(r) + toHex(g) + toHex(b);
  }

  module.exports = {
    hslToHex: hslToHex
  };
</wxs>

<view class="color-wheel-container">
  <!-- 色轮使用说明已移至页面中显示 -->

  <!-- 色轮和明度滑块的容器 -->
  <view class="wheel-and-brightness-container">
    <!-- 色轮 - 完全使用静态实现 -->
    <view class="wheel-wrapper">
      <!-- 静态色轮 -->
      <view class="static-color-wheel" style="width: {{wheelSize}}px; height: {{wheelSize}}px;">
        <!-- 饱和度渐变叠加层 -->
        <view class="saturation-overlay"></view>

        <!-- 色轮边框 -->
        <view class="color-wheel-border"></view>

        <!-- 配色方案连线 -->
        <block wx:if="{{paletteType !== 'monochromatic'}}">
          <!-- 连线 - 根据配色方案类型显示不同的连线 -->
          <view wx:for="{{connectionLines}}" wx:key="index" class="color-connection-line {{connectionLines[index].type}}"
                style="left: {{connectionLines[index].startX}}px; top: {{connectionLines[index].startY}}px; width: {{connectionLines[index].length}}px; transform: rotate({{connectionLines[index].angle}}deg);">
          </view>
        </block>

        <!-- 基础颜色点 -->
        <view class="base-color-point" style="background-color: {{selectedColor}}; left: {{selectedPoint.x}}px; top: {{selectedPoint.y}}px;"></view>

        <!-- 配色方案颜色点 -->
        <block wx:if="{{paletteType !== 'monochromatic'}}">
          <view wx:for="{{paletteColors}}" wx:key="index" class="palette-color-point"
                style="background-color: {{item}}; left: {{palettePoints[index].x}}px; top: {{palettePoints[index].y}}px;">
          </view>
        </block>
      </view>
    </view>

    <!-- 亮度指示器 (竖直方向，放在色轮右侧) -->
    <view class="brightness-slider-container">
      <!-- 明度说明文本 -->
      <view class="brightness-label">明度</view>

      <!-- 明度指示轨道 (竖直方向) -->
      <view
        class="brightness-slider-track vertical"
        style="height: {{wheelSize * 0.8}}px; background: linear-gradient(to bottom, #FFFFFF, {{selectedColor}}, #000000);"
      >
        <!-- 明度指示器 -->
        <view
          class="brightness-slider-thumb vertical"
          style="top: {{100 - brightness}}%;"
        >
        </view>
      </view>

      <!-- 明度值显示 -->
      <view class="brightness-value-container">
        <view class="brightness-value">{{brightness}}%</view>
      </view>
    </view>
  </view>

  <!-- 相关颜色说明已移至页面中显示 -->
</view>
