/**
 * 颜色工具函数
 */

/**
 * 判断颜色是否为深色
 * @param {number} r - 红色通道值 (0-255)
 * @param {number} g - 绿色通道值 (0-255)
 * @param {number} b - 蓝色通道值 (0-255)
 * @returns {boolean} 如果颜色是深色返回true，否则返回false
 */
function isColorDark(r, g, b) {
  // 计算亮度 (基于人眼对不同颜色的感知)
  // 公式: (0.299*R + 0.587*G + 0.114*B)
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;
  return brightness < 128; // 亮度小于128认为是深色
}

/**
 * 将HEX颜色转换为RGB对象
 * @param {string} hex - 十六进制颜色值 (例如: "#FF0000")
 * @returns {Object} 包含r, g, b属性的对象
 */
function hexToRgb(hex) {
  // 移除#前缀
  hex = hex.replace('#', '');

  // 解析RGB值
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  return { r, g, b };
}

/**
 * 获取适合背景色的文字颜色
 * @param {string} hexColor - 十六进制背景颜色值
 * @param {boolean} forceBlackHex - 强制使用黑色作为16进制文字颜色
 * @param {boolean} preferWhite - 优先使用白色文字，只有在对比度很差时才使用黑色
 * @returns {Object} 包含hexTextColor和rgbTextColor的对象
 */
function getTextColorsForBackground(hexColor, forceBlackHex = false, preferWhite = false) {
  // 移除#号并解析RGB值
  const rgb = hexToRgb(hexColor);

  // 判断是否为深色
  const isDark = isColorDark(rgb.r, rgb.g, rgb.b);

  // 判断与白色的对比度是否足够
  const hasEnoughContrastWithWhite = hasLowContrastWithWhite(rgb.r, rgb.g, rgb.b);

  // 设置16进制文字颜色
  let hexTextColor;
  if (forceBlackHex) {
    // 强制使用黑色
    hexTextColor = '#000000';
  } else if (preferWhite) {
    // 优先使用白色，只有在对比度很差时才使用黑色
    hexTextColor = hasEnoughContrastWithWhite ? '#000000' : '#FFFFFF';
  } else {
    // 根据背景色深浅自动选择
    hexTextColor = isDark ? '#FFFFFF' : '#000000';
  }

  // 设置RGB文字颜色（带80%透明度）
  let rgbTextColor;
  if (forceBlackHex || (preferWhite && hasEnoughContrastWithWhite)) {
    // 使用黑色文字带80%透明度
    rgbTextColor = 'rgba(0, 0, 0, 0.8)';
  } else {
    // 使用白色文字带80%透明度
    rgbTextColor = 'rgba(255, 255, 255, 0.8)';
  }

  return {
    hexTextColor,
    rgbTextColor
  };
}

/**
 * 判断颜色与白色的对比度是否很低
 * @param {number} r - 红色通道值 (0-255)
 * @param {number} g - 绿色通道值 (0-255)
 * @param {number} b - 蓝色通道值 (0-255)
 * @returns {boolean} 如果颜色与白色对比度很低返回true，否则返回false
 */
function hasLowContrastWithWhite(r, g, b) {
  // 计算亮度 (基于人眼对不同颜色的感知)
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;

  // 亮度值接近255表示颜色接近白色，对比度低
  return brightness > 200; // 阈值可以根据需要调整
}

/**
 * HEX颜色转换为HSL颜色
 * @param {string} hex - 十六进制颜色值，如 "#FF0000"
 * @returns {object} 包含h, s, l属性的对象，分别表示色相(0-360)、饱和度(0-100)和亮度(0-100)
 */
function hexToHSL(hex) {
  // 移除#号
  hex = hex.replace(/^#/, '');

  // 解析RGB值
  let r = parseInt(hex.substring(0, 2), 16) / 255;
  let g = parseInt(hex.substring(2, 4), 16) / 255;
  let b = parseInt(hex.substring(4, 6), 16) / 255;

  // 找出最大和最小RGB值
  let max = Math.max(r, g, b);
  let min = Math.min(r, g, b);

  // 计算亮度
  let l = (max + min) / 2;

  let h, s;

  if (max === min) {
    // 如果最大值等于最小值，则为灰色
    h = s = 0;
  } else {
    // 计算色相和饱和度
    let d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }

    h = Math.round(h * 60);
  }

  s = Math.round(s * 100);
  l = Math.round(l * 100);

  return { h, s, l };
}

/**
 * HSL颜色转换为HEX颜色
 * @param {number} h - 色相(0-360)
 * @param {number} s - 饱和度(0-100)
 * @param {number} l - 亮度(0-100)
 * @returns {string} 十六进制颜色值，如 "#FF0000"
 */
function hslToHex(h, s, l) {
  h /= 360;
  s /= 100;
  l /= 100;

  let r, g, b;

  if (s === 0) {
    // 如果饱和度为0，则为灰色
    r = g = b = l;
  } else {
    const hue2rgb = (p, q, t) => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1/6) return p + (q - p) * 6 * t;
      if (t < 1/2) return q;
      if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
      return p;
    };

    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;

    r = hue2rgb(p, q, h + 1/3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1/3);
  }

  // 转换为HEX
  const toHex = x => {
    const hex = Math.round(x * 255).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };

  return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
}

/**
 * 根据背景色计算文字颜色（黑色或白色）
 * @param {string} hexColor - 十六进制颜色值，如 "#FF0000"
 * @returns {string} 文字颜色，黑色("#000000")或白色("#FFFFFF")
 */
function getTextColorForBackground(hexColor) {
  // 使用已有的函数获取RGB值
  const rgb = hexToRgb(hexColor);

  // 使用已有的函数判断是否为深色
  const isDark = isColorDark(rgb.r, rgb.g, rgb.b);

  // 如果是深色返回白色文字，否则返回黑色文字
  return isDark ? '#FFFFFF' : '#000000';
}

/**
 * RGB颜色转换为HEX颜色
 * @param {number} r - 红色通道值 (0-255)
 * @param {number} g - 绿色通道值 (0-255)
 * @param {number} b - 蓝色通道值 (0-255)
 * @returns {string} 十六进制颜色值，如 "#FF0000"
 */
function rgbToHex(r, g, b) {
  return '#' + [r, g, b].map(x => {
    const hex = Math.round(x).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  }).join('');
}

/**
 * RGB颜色转换为HSL颜色
 * @param {Object} rgb - 包含r, g, b属性的对象，值范围0-255
 * @returns {Object} 包含h, s, l属性的对象，分别表示色相(0-360)、饱和度(0-100)和亮度(0-100)
 */
function rgbToHSL(rgb) {
  let { r, g, b } = rgb;

  // 将RGB值转换为0-1范围
  r /= 255;
  g /= 255;
  b /= 255;

  // 找出最大和最小RGB值
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);

  let h, s, l = (max + min) / 2;

  if (max === min) {
    // 如果最大值等于最小值，则为灰色
    h = s = 0;
  } else {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }

    h = Math.round(h * 60);
  }

  s = Math.round(s * 100);
  l = Math.round(l * 100);

  return { h, s, l };
}

/**
 * HSL颜色转换为RGB颜色
 * @param {number} h - 色相(0-360)
 * @param {number} s - 饱和度(0-100)
 * @param {number} l - 亮度(0-100)
 * @returns {Object} 包含r, g, b属性的对象，值范围0-255
 */
function hslToRGB(h, s, l) {
  h /= 360;
  s /= 100;
  l /= 100;

  let r, g, b;

  if (s === 0) {
    // 如果饱和度为0，则为灰色
    r = g = b = l;
  } else {
    const hue2rgb = (p, q, t) => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1/6) return p + (q - p) * 6 * t;
      if (t < 1/2) return q;
      if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
      return p;
    };

    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;

    r = hue2rgb(p, q, h + 1/3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1/3);
  }

  return {
    r: Math.round(r * 255),
    g: Math.round(g * 255),
    b: Math.round(b * 255)
  };
}

/**
 * RGB颜色转换为XYZ颜色空间
 * @param {Object} rgb - 包含r, g, b属性的对象，值范围0-255
 * @returns {Object} 包含x, y, z属性的对象
 */
function rgbToXyz(rgb) {
  let { r, g, b } = rgb;

  // 将RGB值转换为0-1范围
  r = r / 255;
  g = g / 255;
  b = b / 255;

  // 应用gamma校正
  r = r > 0.04045 ? Math.pow((r + 0.055) / 1.055, 2.4) : r / 12.92;
  g = g > 0.04045 ? Math.pow((g + 0.055) / 1.055, 2.4) : g / 12.92;
  b = b > 0.04045 ? Math.pow((b + 0.055) / 1.055, 2.4) : b / 12.92;

  // 转换为XYZ（使用D65标准光源）
  const x = r * 0.4124564 + g * 0.3575761 + b * 0.1804375;
  const y = r * 0.2126729 + g * 0.7151522 + b * 0.0721750;
  const z = r * 0.0193339 + g * 0.1191920 + b * 0.9503041;

  return { x: x * 100, y: y * 100, z: z * 100 };
}

/**
 * XYZ颜色空间转换为LAB颜色空间
 * @param {Object} xyz - 包含x, y, z属性的对象
 * @returns {Object} 包含l, a, b属性的对象
 */
function xyzToLab(xyz) {
  let { x, y, z } = xyz;

  // D65标准光源的白点
  const xn = 95.047;
  const yn = 100.000;
  const zn = 108.883;

  // 归一化
  x = x / xn;
  y = y / yn;
  z = z / zn;

  // 应用LAB转换函数
  const fx = x > 0.008856 ? Math.pow(x, 1/3) : (7.787 * x + 16/116);
  const fy = y > 0.008856 ? Math.pow(y, 1/3) : (7.787 * y + 16/116);
  const fz = z > 0.008856 ? Math.pow(z, 1/3) : (7.787 * z + 16/116);

  const l = 116 * fy - 16;
  const a = 500 * (fx - fy);
  const b = 200 * (fy - fz);

  return {
    l: Math.round(l * 10) / 10,
    a: Math.round(a * 10) / 10,
    b: Math.round(b * 10) / 10
  };
}

/**
 * RGB颜色直接转换为LAB颜色空间
 * @param {Object} rgb - 包含r, g, b属性的对象，值范围0-255
 * @returns {Object} 包含l, a, b属性的对象
 */
function rgbToLab(rgb) {
  const xyz = rgbToXyz(rgb);
  return xyzToLab(xyz);
}

/**
 * HEX颜色转换为LAB颜色空间
 * @param {string} hex - 十六进制颜色值
 * @returns {Object} 包含l, a, b属性的对象
 */
function hexToLab(hex) {
  const rgb = hexToRgb(hex);
  return rgbToLab(rgb);
}

/**
 * 计算两个颜色之间的ΔE*2000色差
 * @param {string} color1 - 第一个颜色的HEX值
 * @param {string} color2 - 第二个颜色的HEX值
 * @returns {number} ΔE*2000色差值，值越小表示颜色越相似
 */
function calculateColorDistance(color1, color2) {
  const lab1 = hexToLab(color1);
  const lab2 = hexToLab(color2);

  return calculateDeltaE2000(lab1, lab2);
}

/**
 * 计算ΔE*2000色差算法
 * @param {Object} lab1 - 第一个颜色的LAB值 {l, a, b}
 * @param {Object} lab2 - 第二个颜色的LAB值 {l, a, b}
 * @returns {number} ΔE*2000色差值
 */
function calculateDeltaE2000(lab1, lab2) {
  // 权重因子
  const kL = 1.0;
  const kC = 1.0;
  const kH = 1.0;

  // LAB值
  const L1 = lab1.l;
  const a1 = lab1.a;
  const b1 = lab1.b;
  const L2 = lab2.l;
  const a2 = lab2.a;
  const b2 = lab2.b;

  // 计算C*ab
  const C1 = Math.sqrt(a1 * a1 + b1 * b1);
  const C2 = Math.sqrt(a2 * a2 + b2 * b2);
  const Cab = (C1 + C2) / 2;

  // 计算G
  const G = 0.5 * (1 - Math.sqrt(Math.pow(Cab, 7) / (Math.pow(Cab, 7) + Math.pow(25, 7))));

  // 计算a'
  const a1_prime = (1 + G) * a1;
  const a2_prime = (1 + G) * a2;

  // 计算C'
  const C1_prime = Math.sqrt(a1_prime * a1_prime + b1 * b1);
  const C2_prime = Math.sqrt(a2_prime * a2_prime + b2 * b2);

  // 计算h'
  let h1_prime = Math.atan2(b1, a1_prime) * 180 / Math.PI;
  if (h1_prime < 0) h1_prime += 360;

  let h2_prime = Math.atan2(b2, a2_prime) * 180 / Math.PI;
  if (h2_prime < 0) h2_prime += 360;

  // 计算ΔL', ΔC', Δh'
  const deltaL_prime = L2 - L1;
  const deltaC_prime = C2_prime - C1_prime;

  let deltah_prime;
  if (C1_prime * C2_prime === 0) {
    deltah_prime = 0;
  } else if (Math.abs(h2_prime - h1_prime) <= 180) {
    deltah_prime = h2_prime - h1_prime;
  } else if (h2_prime - h1_prime > 180) {
    deltah_prime = h2_prime - h1_prime - 360;
  } else {
    deltah_prime = h2_prime - h1_prime + 360;
  }

  const deltaH_prime = 2 * Math.sqrt(C1_prime * C2_prime) * Math.sin(deltah_prime * Math.PI / 360);

  // 计算平均值
  const L_prime = (L1 + L2) / 2;
  const C_prime = (C1_prime + C2_prime) / 2;

  let H_prime;
  if (C1_prime * C2_prime === 0) {
    H_prime = h1_prime + h2_prime;
  } else if (Math.abs(h1_prime - h2_prime) <= 180) {
    H_prime = (h1_prime + h2_prime) / 2;
  } else if (Math.abs(h1_prime - h2_prime) > 180 && (h1_prime + h2_prime) < 360) {
    H_prime = (h1_prime + h2_prime + 360) / 2;
  } else {
    H_prime = (h1_prime + h2_prime - 360) / 2;
  }

  // 计算T
  const T = 1 - 0.17 * Math.cos((H_prime - 30) * Math.PI / 180) +
            0.24 * Math.cos(2 * H_prime * Math.PI / 180) +
            0.32 * Math.cos((3 * H_prime + 6) * Math.PI / 180) -
            0.20 * Math.cos((4 * H_prime - 63) * Math.PI / 180);

  // 计算ΔΘ
  const deltaTheta = 30 * Math.exp(-Math.pow((H_prime - 275) / 25, 2));

  // 计算RC
  const RC = 2 * Math.sqrt(Math.pow(C_prime, 7) / (Math.pow(C_prime, 7) + Math.pow(25, 7)));

  // 计算SL, SC, SH
  const SL = 1 + (0.015 * Math.pow(L_prime - 50, 2)) / Math.sqrt(20 + Math.pow(L_prime - 50, 2));
  const SC = 1 + 0.045 * C_prime;
  const SH = 1 + 0.015 * C_prime * T;

  // 计算RT
  const RT = -Math.sin(2 * deltaTheta * Math.PI / 180) * RC;

  // 计算ΔE*2000
  const deltaE2000 = Math.sqrt(
    Math.pow(deltaL_prime / (kL * SL), 2) +
    Math.pow(deltaC_prime / (kC * SC), 2) +
    Math.pow(deltaH_prime / (kH * SH), 2) +
    RT * (deltaC_prime / (kC * SC)) * (deltaH_prime / (kH * SH))
  );

  return deltaE2000;
}

/**
 * 计算颜色相似度百分比（基于ΔE*2000）
 * @param {string} color1 - 第一个颜色的HEX值
 * @param {string} color2 - 第二个颜色的HEX值
 * @returns {number} 相似度百分比 (0-100)，100表示完全相同
 */
function calculateColorSimilarity(color1, color2) {
  const deltaE2000 = calculateColorDistance(color1, color2);

  // ΔE*2000标准的感知阈值：
  // ΔE*2000 < 1.0: 人眼无法察觉差异
  // ΔE*2000 1.0-2.3: 人眼刚好能察觉差异
  // ΔE*2000 2.3-5.0: 人眼可以明显察觉差异
  // ΔE*2000 5.0-10.0: 颜色差异较大
  // ΔE*2000 > 10.0: 颜色差异很大

  // 基于ΔE*2000的更精确的相似度计算
  let similarity;
  if (deltaE2000 < 0.5) {
    similarity = 100; // 完全相同或无法察觉
  } else if (deltaE2000 < 1.0) {
    similarity = 98 - (deltaE2000 - 0.5) * 4; // 98-96%
  } else if (deltaE2000 < 2.3) {
    similarity = 96 - (deltaE2000 - 1.0) * 15.38; // 96-76%
  } else if (deltaE2000 < 5.0) {
    similarity = 76 - (deltaE2000 - 2.3) * 18.52; // 76-26%
  } else if (deltaE2000 < 10.0) {
    similarity = 26 - (deltaE2000 - 5.0) * 4.4; // 26-4%
  } else if (deltaE2000 < 20.0) {
    similarity = 4 - (deltaE2000 - 10.0) * 0.3; // 4-1%
  } else {
    similarity = Math.max(0, 1 - (deltaE2000 - 20.0) * 0.05); // 1%-0%
  }

  return Math.round(similarity * 100) / 100; // 保留两位小数
}

/**
 * 在颜色数组中找到与目标颜色最相似的颜色
 * @param {string} targetColor - 目标颜色的HEX值
 * @param {Array} colorArray - 颜色数组，每个元素包含name和color属性
 * @returns {Object} 包含最相似颜色信息和相似度的对象
 */
function findClosestColor(targetColor, colorArray) {
  let closestColor = null;
  let minDistance = Infinity;
  let maxSimilarity = 0;

  colorArray.forEach(colorItem => {
    const distance = calculateColorDistance(targetColor, colorItem.color);
    const similarity = calculateColorSimilarity(targetColor, colorItem.color);

    if (distance < minDistance) {
      minDistance = distance;
      maxSimilarity = similarity;
      closestColor = {
        ...colorItem,
        similarity: similarity,
        distance: distance
      };
    }
  });

  return closestColor;
}

/**
 * 计算目标颜色与所有颜色的相似度并排序
 * @param {string} targetColor - 目标颜色的HEX值
 * @param {Array} colorArray - 颜色数组，每个元素包含name和color属性
 * @returns {Array} 按相似度降序排列的颜色数组
 */
function calculateAllSimilarities(targetColor, colorArray) {
  return colorArray.map(colorItem => {
    const similarity = calculateColorSimilarity(targetColor, colorItem.color);
    const distance = calculateColorDistance(targetColor, colorItem.color);

    return {
      ...colorItem,
      similarity: similarity,
      distance: distance
    };
  }).sort((a, b) => b.similarity - a.similarity); // 按相似度降序排列
}

module.exports = {
  isColorDark,
  hexToRgb,
  getTextColorsForBackground,
  hasLowContrastWithWhite,
  hexToHSL,
  hslToHex,
  getTextColorForBackground,
  rgbToHex,
  rgbToHSL,
  hslToRGB,
  rgbToXyz,
  xyzToLab,
  rgbToLab,
  hexToLab,
  calculateDeltaE2000,
  calculateColorDistance,
  calculateColorSimilarity,
  findClosestColor,
  calculateAllSimilarities
};
