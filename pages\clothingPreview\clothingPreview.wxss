/* pages/clothingPreview/clothingPreview.wxss */
.preview-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  position: relative;
  padding: 0; /* 移除内边距 */
  margin: 0; /* 移除外边距 */
  overflow: hidden; /* 防止溢出 */
}

/* 图片容器样式 - 优化布局 */
.image-container {
  width: 100%;
  height: calc(100vh - 44px); /* 占据整个屏幕高度减去导航栏高度 */
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #ffffff;
  padding: 0 10rpx; /* 添加少量水平内边距 */
  box-sizing: border-box;
  overflow: hidden; /* 防止溢出 */
  margin-top: -10rpx; /* 向上微调，减少与导航栏的间距 */
}

/* SVG图片样式 - 放大图片尺寸 */
.svg-image {
  width: 100%; /* 使用100%宽度 */
  max-width: 800rpx; /* 增加最大宽度 */
  display: block; /* 确保正确显示 */
  margin: 0 auto; /* 水平居中 */
  /* 使用widthFix模式，高度会自动按比例调整 */
}

/* 加载中状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.loading-spinner {
  width: 64rpx;
  height: 64rpx;
  border: 6rpx solid rgba(0, 0, 0, 0.1);
  border-top: 6rpx solid #1aad19;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 错误状态样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.error-text {
  font-size: 32rpx;
  color: #666;
  margin: 20rpx 0 40rpx;
}

/* 底部按钮相关样式已移除 */

/* 返回按钮样式 - 用于错误状态 */
.back-btn {
  background-color: #1aad19;
  color: #fff;
  font-size: 32rpx;
  padding: 16rpx 40rpx;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(26, 173, 25, 0.2);
  margin-top: 30rpx;
}
