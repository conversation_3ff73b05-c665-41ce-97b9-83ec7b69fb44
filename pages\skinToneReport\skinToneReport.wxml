<!--pages/skinToneReport/skinToneReport.wxml-->
<view class="page">
  <view class="container">
    <!-- 用户选择的颜色 -->
    <view class="section" wx:if="{{reportGenerated}}">
      <view class="section-title">您的肤色</view>

      <!-- 多肤色模式显示 -->
      <view wx:if="{{isMultiColor}}" class="multi-color-report">
        <view class="user-color-container" style="background-color: {{userColor}};" bindtap="copyColorCode" data-color="{{userColor}}">
          <view>
            <view class="user-color-hex">LAB: {{labValues}}</view>
            <view class="user-color-details">
              <view class="color-detail-item">HEX: {{userColor}}</view>
              <view class="color-detail-item">RGB: {{rgbValues}}</view>
            </view>
          </view>
          <button class="copy-btn">复制色值</button>
        </view>
      </view>

      <!-- 单肤色模式显示 -->
      <view wx:else class="single-color-report">
        <view class="user-color-container" style="background-color: {{userColor}};" bindtap="copyColorCode" data-color="{{userColor}}">
          <view>
            <view class="user-color-hex">LAB: {{labValues}}</view>
            <view class="user-color-details">
              <view class="color-detail-item">HEX: {{userColor}}</view>
              <view class="color-detail-item">RGB: {{rgbValues}}</view>
            </view>
          </view>
          <button class="copy-btn">复制色值</button>
        </view>
      </view>

      <!-- 肤色分析内容（合并到您的肤色模块中） -->
      <view class="analysis-container">
        <!-- L值（亮度）分析 -->
        <view class="analysis-item">
          <view class="analysis-title">
            <text class="analysis-icon">💡</text>
            <text class="analysis-label">L值分析（亮度：{{skinAnalysis.lValue}}）</text>
          </view>
          <view class="analysis-content">
            <view class="analysis-category">{{skinAnalysis.lCategory}}</view>
            <view class="analysis-description">{{skinAnalysis.lDescription}}</view>
          </view>
        </view>

        <!-- a值（红绿轴）分析 -->
        <view class="analysis-item">
          <view class="analysis-title">
            <text class="analysis-icon">🔴</text>
            <text class="analysis-label">a值分析（红绿轴：{{skinAnalysis.aValue}}）</text>
          </view>
          <view class="analysis-content">
            <view class="analysis-category">{{skinAnalysis.aCategory}}</view>
            <view class="analysis-description">{{skinAnalysis.aDescription}}</view>
          </view>
        </view>

        <!-- b值（黄蓝轴）分析 -->
        <view class="analysis-item">
          <view class="analysis-title">
            <text class="analysis-icon">🟡</text>
            <text class="analysis-label">b值分析（黄蓝轴：{{skinAnalysis.bValue}}）</text>
          </view>
          <view class="analysis-content">
            <view class="analysis-category">{{skinAnalysis.bCategory}}</view>
            <view class="analysis-description">{{skinAnalysis.bDescription}}</view>
          </view>
        </view>

        <!-- 护肤建议 -->
        <view class="analysis-item">
          <view class="analysis-title">
            <text class="analysis-icon">🧴</text>
            <text class="analysis-label">护肤建议</text>
          </view>
          <view class="analysis-content">
            <view class="skincare-suggestions">
              <view class="suggestion-item" wx:for="{{skinAnalysis.skincareSuggestions}}" wx:key="*this">
                <text class="suggestion-bullet">•</text>
                <text class="suggestion-text">{{item}}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 美妆建议 -->
        <view class="analysis-item">
          <view class="analysis-title">
            <text class="analysis-icon">💄</text>
            <text class="analysis-label">美妆建议</text>
          </view>
          <view class="analysis-content">
            <view class="makeup-suggestions">
              <view class="makeup-category">
                <view class="makeup-category-title">粉底液选择</view>
                <view class="makeup-category-content">{{skinAnalysis.makeupSuggestions.foundation}}</view>
              </view>
              <view class="makeup-category">
                <view class="makeup-category-title">遮瑕膏选择</view>
                <view class="makeup-category-content">{{skinAnalysis.makeupSuggestions.concealer}}</view>
              </view>
              <view class="makeup-category">
                <view class="makeup-category-title">唇釉选择</view>
                <view class="makeup-category-content">{{skinAnalysis.makeupSuggestions.lip}}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 匹配结果（色卡1） -->
    <view class="section" wx:if="{{reportGenerated && closestMatchOne}}">
      <view class="section-title">匹配结果（色卡1）</view>

      <!-- 最佳匹配结果 -->
      <view class="best-match-container">
        <view class="match-result-text">
          <view class="match-title">最佳匹配</view>
          <view class="match-content">
            <view class="match-name-text">{{closestMatchOne.name}}</view>
            <view class="match-details">
              <text class="similarity-text">相似度：{{closestMatchOne.similarity}}%</text>
              <text class="level-text">{{closestMatchOne.level}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 详细相似度分析 -->
      <view class="similarity-analysis" wx:if="{{allSimilaritiesOne.length > 0}}">
        <view class="similarity-list">
          <view
            class="similarity-item {{index === 0 ? 'best-match' : ''}}"
            wx:for="{{allSimilaritiesOne}}"
            wx:key="name"
            wx:if="{{index < 3 || expandedOne}}"
          >
            <view class="similarity-rank">{{index + 1}}</view>

            <!-- 颜色对比区域 -->
            <view class="color-comparison">
              <view class="color-contrast-block" style="background-color: {{item.color}};">
                <view class="user-skin-circle" style="background-color: {{userColor}};"></view>
              </view>
            </view>

            <view class="similarity-info">
              <view class="similarity-name">{{item.name}}</view>
              <view class="similarity-percentage" style="color: {{item.similarityColor}};">
                {{item.similarity}}%
              </view>
              <view class="similarity-level" style="color: {{item.similarityColor}};">
                {{item.similarityLevel}}
              </view>
            </view>

            <view class="similarity-bar">
              <view class="similarity-progress" style="width: {{item.similarity}}%; background-color: {{item.similarityColor}};"></view>
            </view>
            <view class="similarity-badge" wx:if="{{index === 0}}">最佳</view>
          </view>
        </view>

        <!-- 展开/收起按钮 -->
        <view class="expand-toggle" wx:if="{{allSimilaritiesOne.length > 3}}" bindtap="toggleExpandOne">
          <view class="expand-text">{{expandedOne ? '收起' : '展开更多'}}</view>
          <view class="expand-icon {{expandedOne ? 'expanded' : ''}}">▼</view>
        </view>
      </view>
    </view>

    <!-- 匹配结果（色卡2） -->
    <view class="section" wx:if="{{reportGenerated && closestMatchTwo}}">
      <view class="section-title">匹配结果（色卡2）</view>

      <!-- 最佳匹配结果 -->
      <view class="best-match-container">
        <view class="match-result-text">
          <view class="match-title">最佳匹配</view>
          <view class="match-content">
            <view class="match-name-text">{{closestMatchTwo.name}}</view>
            <view class="match-details">
              <text class="similarity-text">相似度：{{closestMatchTwo.similarity}}%</text>
              <text class="level-text">{{closestMatchTwo.level}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 详细相似度分析 -->
      <view class="similarity-analysis" wx:if="{{allSimilaritiesTwo.length > 0}}">
        <view class="similarity-list">
          <view
            class="similarity-item {{index === 0 ? 'best-match' : ''}}"
            wx:for="{{allSimilaritiesTwo}}"
            wx:key="name"
            wx:if="{{index < 3 || expandedTwo}}"
          >
            <view class="similarity-rank">{{index + 1}}</view>

            <!-- 颜色对比区域 -->
            <view class="color-comparison">
              <view class="color-contrast-block" style="background-color: {{item.color}};">
                <view class="user-skin-circle" style="background-color: {{userColor}};"></view>
              </view>
            </view>

            <view class="similarity-info">
              <view class="similarity-name">{{item.name}}</view>
              <view class="similarity-percentage" style="color: {{item.similarityColor}};">
                {{item.similarity}}%
              </view>
              <view class="similarity-level" style="color: {{item.similarityColor}};">
                {{item.similarityLevel}}
              </view>
            </view>

            <view class="similarity-bar">
              <view class="similarity-progress" style="width: {{item.similarity}}%; background-color: {{item.similarityColor}};"></view>
            </view>
            <view class="similarity-badge" wx:if="{{index === 0}}">最佳</view>
          </view>
        </view>

        <!-- 展开/收起按钮 -->
        <view class="expand-toggle" wx:if="{{allSimilaritiesTwo.length > 3}}" bindtap="toggleExpandTwo">
          <view class="expand-text">{{expandedTwo ? '收起' : '展开更多'}}</view>
          <view class="expand-icon {{expandedTwo ? 'expanded' : ''}}">▼</view>
        </view>
      </view>
    </view>

    <!-- 算法说明模块 -->
    <view class="section" wx:if="{{reportGenerated}}">
      <view class="section-title">算法说明</view>
      <view class="algorithm-explanation">
        <view class="algorithm-text">
          本程序使用了CIEDE2000 算法进行肤色相似度计算，该算法被认为是肤色相似度计算的最优选择之一，原因在于它是国际照明委员会（CIE）基于人类视觉感知特性开发的色差公式，专门针对均匀颜色空间中颜色差异的视觉一致性进行了优化。该算法通过引入亮度、色调和饱和度的非线性变换，结合视觉感知的不均匀性（如色调响应的角度权重、明度和彩度的视觉敏感差异），更精准地模拟了人类眼睛对颜色差异的主观感受。在肤色这类对色调和明度变化敏感的场景中，CIEDE2000 通过修正色调差的权重函数、引入明度和彩度的视觉适应因子，有效降低了传统色差公式（如 CIELAB）在中低明度区域的计算偏差，能更可靠地反映肤色样本之间的视觉相似性，因此在肤色检测、图像肤色匹配等领域表现出显著优势。
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="section" wx:if="{{reportGenerated}}">
      <view class="action-buttons">
        <view class="action-btn secondary" bindtap="retestSkinTone">
          <text class="action-btn-icon">↻</text>
          <text class="action-btn-text">重新测试</text>
        </view>
        <view class="action-btn primary" bindtap="generateShareImage">
          <text class="action-btn-icon">📷</text>
          <text class="action-btn-text">生成分享图</text>
        </view>
        <button class="action-btn primary" open-type="share">
          <text class="action-btn-icon">↗</text>
          <text class="action-btn-text">分享小程序</text>
        </button>
      </view>
    </view>



    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{!reportGenerated}}">
      <view class="loading-spinner"></view>
      <view class="loading-text">正在生成报告...</view>
    </view>
  </view>

  <!-- Canvas用于生成分享图片（隐藏） - 使用Canvas 2D API -->
  <canvas
    type="2d"
    id="shareCanvas"
    style="width: 750px; height: 3800px; position: fixed; top: -4500px; left: -1000px; z-index: -1;"
  ></canvas>
</view>
