/* pages/preview/preview.wxss */
/* 使用全局样式变量，不再重复定义 */
page {
  background-color: var(--bg-page);
}

.page-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  box-sizing: border-box;
  animation: fadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.scroll-area {
  padding: 0 var(--spacing-sm);
  /* padding-top 现在通过内联样式动态设置，已减少空隙 */
  padding-bottom: 200rpx; /* 增加底部内边距，确保内容不被底部按钮遮挡 */
  flex: 1;
  box-sizing: border-box;
  overflow-y: auto;
  /* 添加负边距，进一步减少顶部空隙 */
  margin-top: -50rpx; /* 保持顶部负边距 */
}

.card-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: -20rpx; /* 增加负边距，进一步减少顶部空隙 */
  margin-bottom: var(--spacing-sm); /* 减少底部边距 */
  padding: 0;
  transition: transform var(--transition-normal);
}

.color-card {
  width: 700rpx; /* 稍微增加宽度，使图片更加突出 */
  background-color: transparent; /* 移除背景色，使其透明 */
  overflow: hidden;
  box-shadow: none; /* 移除阴影效果 */
  transition: all var(--transition-normal);
  transform: translateY(0);
  animation: cardAppear 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: var(--radius-sm); /* 添加轻微圆角，增强现代感 */
}

@keyframes cardAppear {
  from {
    opacity: 0;
    transform: translateY(15rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.color-card:active {
  transform: scale(0.99); /* 添加轻微缩放效果，增强交互感 */
  opacity: 0.98;
}

.card-image {
  width: 100%;
  background-color: transparent; /* 移除背景色，使其透明 */
  display: block;
  margin: 0;
  border-radius: var(--radius-sm); /* 图片也添加轻微圆角 */
}

.btn-container {
  padding: 20rpx 30rpx 50rpx; /* 增加底部内边距，确保白色背景延伸到屏幕底部 */
  width: 100%;
  box-sizing: border-box;
  position: fixed;
  bottom: 0; /* 改回0，让容器紧贴屏幕底部 */
  left: 0;
  background-color: white;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: row; /* 水平排列 */
  gap: 20rpx; /* 按钮间距 */
}

.save-btn {
  background-color: #07c160;
  color: white;
  font-size: 30rpx;
  font-weight: 500;
  padding: 18rpx 0;
  flex: 1; /* 使用flex布局，按钮平分空间 */
  transition: all 0.2s ease;
  border: none;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.15);
}

.save-btn:active {
  opacity: 0.9;
  transform: scale(0.98);
}

.home-btn {
  background-color: #f5f5f5;
  color: #333333;
  font-size: 30rpx;
  font-weight: 500;
  padding: 18rpx 0;
  flex: 1; /* 使用flex布局，按钮平分空间 */
  transition: all 0.2s ease;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  border-radius: 8rpx;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

.home-btn:active {
  opacity: 0.9;
  transform: scale(0.98);
}

/* 页面内容区域样式 */

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg) 0;
  min-height: 500rpx;
  background-color: rgba(245, 245, 245, 0.5);
  border-radius: var(--radius-sm);
  margin: 20rpx 0;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(52, 152, 219, 0.15); /* 使用主色调 */
  border-top: 6rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 0.8s cubic-bezier(0.4, 0, 0.2, 1) infinite; /* 更流畅的动画 */
  margin-bottom: var(--spacing-sm);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 30rpx;
  color: var(--primary-color);
  letter-spacing: 1rpx; /* 增加字间距 */
  font-weight: 500;
}

.placeholder-image {
  opacity: 1; /* 设置为完全不透明 */
  transition: opacity var(--transition-normal);
  background-color: transparent; /* 确保背景透明 */
}

/* 信息提示 - 极简设计 */
.card-info {
  margin-top: var(--spacing-xs); /* 减少上边距 */
  margin-bottom: 0; /* 移除下边距 */
  padding: var(--spacing-xs) 0; /* 减少内边距 */
  text-align: center;
  background: none; /* 移除背景 */
  box-shadow: none; /* 移除阴影 */
  border: none;
}

.info-text-simple {
  font-size: 24rpx; /* 更小的字体 */
  color: var(--text-secondary);
  opacity: 0.8; /* 降低不透明度，更加轻量 */
}

/* 按钮图标 */
.btn-icon {
  margin-right: var(--spacing-xs);
  font-size: 30rpx; /* 增大图标 */
}

/* 保存成功提示 */
.save-success {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.8);
  background-color: rgba(0, 0, 0, 0.7);
  padding: 30rpx 40rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0;
  transition: all 0.3s ease;
  pointer-events: none;
  z-index: 1000;
}

.save-success.show {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

.success-icon {
  width: 80rpx;
  height: 80rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2307c160'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  margin-bottom: 16rpx;
}

.success-text {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 500;
}
