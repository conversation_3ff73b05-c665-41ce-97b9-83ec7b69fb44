<!--pages/clothingPreview/clothingPreview.wxml-->
<view class="preview-container">
  <!-- 加载中状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 加载错误状态 -->
  <view class="error-container" wx:elif="{{loadError}}">
    <icon type="warn" size="64" color="#ff3b30"></icon>
    <text class="error-text">图片加载失败</text>
    <button class="back-btn" bindtap="goBack">返回</button>
  </view>

  <!-- 图片显示 - 使用与穿搭配色页面相同的方式 -->
  <view class="image-container" wx:else
    bindtouchstart="touchStart"
    bindtouchend="touchEnd"
    bindtap="doubleTap">
    <image
      src="{{svgImageSrc}}"
      mode="widthFix"
      class="svg-image"
      bindload="onImageLoad"
      binderror="onImageError"
      show-menu-by-longpress="{{false}}"
      catchlongtap="goBack"
    ></image>
  </view>

  <!-- 底部按钮区域已移除 -->
</view>
