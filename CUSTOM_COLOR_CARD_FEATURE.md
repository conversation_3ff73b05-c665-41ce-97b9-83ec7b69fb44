# 自定义色卡功能说明

## 功能概述

色卡制作工具现在支持两种模式：
1. **图片主题色**：从图片中提取颜色制作色卡（原有功能）
2. **自定义色卡**：手动编辑颜色值制作色卡（新增功能）

## 功能特点

### 模式切换
- 在模板选择页面顶部有切换按钮
- 可以在"图片主题色"和"自定义色卡"之间切换
- 切换时会自动重置模板选择状态

### 自定义色卡模式
- 提供预设的"草莓布丁"模板
- 支持4个颜色的编辑
- 颜色格式：#RRGGBB（如 #FF6B9D）
- 提供常用颜色快速选择
- 支持随机生成颜色功能
- 支持重置为默认颜色

### 颜色编辑功能
- 点击色块可编辑对应颜色
- 支持手动输入颜色代码
- 提供8种常用粉色系预设颜色
- 实时预览颜色效果
- 点击颜色代码可复制到剪贴板

### 色卡生成
- 生成的色卡样式与图片主题色模式相同
- 包含大圆形色块、颜色代码和小色块预览
- 支持保存到相册
- 支持图片预览

## 使用流程

### 自定义色卡制作流程
1. 打开模板选择页面
2. 点击"自定义色卡"切换到自定义模式
3. 选择"草莓布丁"模板
4. 点击"下一步，编辑色值"
5. 在编辑页面点击色块编辑颜色
6. 输入颜色代码或选择预设颜色
7. 点击"生成色卡"
8. 在预览页面保存或分享色卡

### 图片主题色制作流程（保持不变）
1. 打开模板选择页面
2. 确保选择"图片主题色"模式
3. 选择喜欢的模板
4. 点击"下一步，选择图片"
5. 选择并裁剪图片
6. 调整颜色提取
7. 生成并保存色卡

## 技术实现

### 新增文件
- `pages/customColorEditor/` - 自定义色卡编辑页面
- `components/color-card-custom/` - 自定义色卡组件

### 修改文件
- `pages/template/template.js` - 添加模式切换逻辑
- `pages/template/template.wxml` - 添加切换按钮和自定义模板UI
- `pages/template/template.wxss` - 添加自定义模板样式
- `pages/preview/preview.js` - 支持自定义色卡预览
- `pages/preview/preview.wxml` - 添加自定义色卡组件
- `pages/preview/preview.json` - 注册自定义色卡组件
- `app.json` - 注册新页面

### 核心功能
- 模式切换：在图片主题色和自定义色卡间切换
- 颜色编辑：支持手动输入和预设颜色选择
- Canvas绘制：使用Canvas API生成自定义色卡
- 数据传递：通过URL参数在页面间传递颜色数据

## 注意事项

1. 颜色格式必须是 #RRGGBB 格式（6位十六进制）
2. 自定义色卡目前支持4个颜色
3. 生成的色卡分辨率为 1600x2303 像素
4. 预览页面的样式在两种模式下保持一致
5. 所有功能都支持微信小程序环境

## 未来扩展

- 支持更多自定义模板
- 支持不同数量的颜色（3-6个）
- 支持渐变色卡
- 支持色彩搭配建议
- 支持从图片中提取颜色到自定义编辑器
