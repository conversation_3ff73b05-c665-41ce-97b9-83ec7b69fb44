<!--pages/colorConverter/colorConverter.wxml-->
<view class="page">
  <view class="container">
    <!-- 输入颜色部分 -->
    <view class="section preview-section">
      <view class="section-title">输入颜色</view>
      <view class="base-color-container">
        <!-- 左侧颜色色块，在中央显示16进制色值 -->
        <view class="color-preview" style="background-color: {{currentColor}};" bindtap="showColorPicker">
          <view class="color-hex-value" style="color: {{textColor}};">{{currentColor}}</view>
        </view>
        <!-- 右侧上下并列的按钮 -->
        <view class="color-actions-column">
          <view class="btn-wrapper">
            <view class="custom-btn random-btn" bindtap="randomColor">随机颜色</view>
          </view>
          <view class="btn-wrapper">
            <view class="custom-btn picker-btn" bindtap="showColorPicker">选择颜色</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 颜色值显示 -->
    <view class="section">
      <view class="section-title">颜色格式与值</view>

      <!-- 所有颜色格式值 -->
      <view class="color-values-all">
        <!-- HEX 格式 -->
        <view class="color-value-item">
          <view class="color-value-label">HEX</view>
          <view class="color-value-content">
            <input class="color-value-input" value="{{hexValue}}" bindinput="onHexInput" />
            <view class="color-value-copy" catchtap="copyColorValue" data-value="{{hexValue}}">
              <text class="copy-icon">复制</text>
            </view>
          </view>
        </view>

        <!-- RGB 格式 -->
        <view class="color-value-item">
          <view class="color-value-label">RGB</view>
          <view class="color-value-content">
            <input class="color-value-input" disabled value="rgb({{rgbValue}})" />
            <view class="color-value-copy" catchtap="copyColorValue" data-value="rgb({{rgbValue}})">
              <text class="copy-icon">复制</text>
            </view>
          </view>
        </view>

        <!-- HSL 格式 -->
        <view class="color-value-item">
          <view class="color-value-label">HSL</view>
          <view class="color-value-content">
            <input class="color-value-input" disabled value="hsl({{hslValue}})" />
            <view class="color-value-copy" catchtap="copyColorValue" data-value="hsl({{hslValue}})">
              <text class="copy-icon">复制</text>
            </view>
          </view>
        </view>

        <!-- HSV 格式 -->
        <view class="color-value-item">
          <view class="color-value-label">HSV</view>
          <view class="color-value-content">
            <input class="color-value-input" disabled value="hsv({{hsvValue}})" />
            <view class="color-value-copy" catchtap="copyColorValue" data-value="hsv({{hsvValue}})">
              <text class="copy-icon">复制</text>
            </view>
          </view>
        </view>

        <!-- CMYK 格式 -->
        <view class="color-value-item">
          <view class="color-value-label">CMYK</view>
          <view class="color-value-content">
            <input class="color-value-input" disabled value="cmyk({{cmykValue}})" />
            <view class="color-value-copy" catchtap="copyColorValue" data-value="cmyk({{cmykValue}})">
              <text class="copy-icon">复制</text>
            </view>
          </view>
        </view>

        <!-- LAB 格式 -->
        <view class="color-value-item">
          <view class="color-value-label">LAB</view>
          <view class="color-value-content">
            <input class="color-value-input" disabled value="lab({{labValue}})" />
            <view class="color-value-copy" catchtap="copyColorValue" data-value="lab({{labValue}})">
              <text class="copy-icon">复制</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 颜色选择器弹窗 -->
  <view class="color-picker-modal" wx:if="{{showColorPicker}}">
    <view class="color-picker-container">
      <view class="color-picker-header">
        <view class="color-picker-title">选择颜色</view>
        <view class="color-picker-close" bindtap="hideColorPicker">×</view>
      </view>
      <!-- 集成颜色选择器组件 -->
      <color-picker
        color="{{currentColor}}"
        bindchange="onColorPickerChange"
        bindconfirm="onColorPickerConfirm"
        bindcancel="hideColorPicker"
      ></color-picker>
    </view>
  </view>
</view>
